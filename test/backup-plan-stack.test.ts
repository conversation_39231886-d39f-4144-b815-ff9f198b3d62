import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import * as backup from 'aws-cdk-lib/aws-backup';
import * as events from 'aws-cdk-lib/aws-events';
import * as kms from 'aws-cdk-lib/aws-kms';
import { BackupPlanStack } from '../lib/stack/backup-plan-stack';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

describe('The backup plan stack', () => {
  let app: cdk.App;
  let helperStack: cdk.Stack;
  let primaryVault: backup.BackupVault;
  let secondaryVault: backup.BackupVault;
  let appKey: kms.IKey;

  beforeEach(() => {
    app = new cdk.App();

    // Create helper stack for mock resources
    helperStack = new cdk.Stack(app, 'HelperStack', { env: getProcEnv() });

    // Create mock KMS key
    appKey = new kms.Key(helperStack, 'TestAppKey', {
      description: 'Test application key for backup vault',
    });

    // Create primary backup vault
    primaryVault = new backup.BackupVault(helperStack, 'PrimaryVault', {
      encryptionKey: appKey,
    });

    // Create secondary backup vault for DR
    secondaryVault = new backup.BackupVault(helperStack, 'SecondaryVault', {
      encryptionKey: appKey,
    });
  });

  // Assert Snapshot
  test('Should Match Expected Snapshot When Backup Plan Stack Is Created With Basic Configuration', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackSnapshot', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  // Assert Number of Resources
  test('Should Create Correct Number Of Resources When Basic Configuration Is Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackResourceCount', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify exact resource counts
    template.resourceCountIs('AWS::Backup::BackupPlan', 1);
    template.resourceCountIs('AWS::Backup::BackupSelection', 1);
    template.resourceCountIs('AWS::IAM::Role', 1);
  });

  // Assert Correct Policy & Assert Resource Properties
  test('Should Create Backup Plan When Primary Vault Only Is Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStack', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup plan is created
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanName: 'TestBackupPlanStack',
        BackupPlanRule: [
          {
            RuleName: Match.anyValue(),
            TargetBackupVault: {
              'Fn::ImportValue': Match.stringLikeRegexp('.*PrimaryVault.*'),
            },
            ScheduleExpression: 'cron(0 2 * * ? *)',
            Lifecycle: {
              DeleteAfterDays: 30,
            },
          },
        ],
      },
    });

    // Verify backup plan resource count
    template.resourceCountIs('AWS::Backup::BackupPlan', 1);
  });

  test('Should Create Backup Plan With Copy Actions When Primary And Secondary Vaults Are Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackDR', {
      vault: primaryVault,
      secondaryVault: secondaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup plan is created with copy actions
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanName: 'TestBackupPlanStackDR',
        BackupPlanRule: [
          {
            RuleName: Match.anyValue(),
            TargetBackupVault: {
              'Fn::ImportValue': Match.stringLikeRegexp('.*PrimaryVault.*'),
            },
            ScheduleExpression: 'cron(0 2 * * ? *)',
            Lifecycle: {
              DeleteAfterDays: 30,
            },
            CopyActions: [
              {
                DestinationBackupVaultArn: {
                  'Fn::ImportValue': Match.stringLikeRegexp('.*SecondaryVault.*'),
                },
              },
            ],
          },
        ],
      },
    });

    // Verify backup plan resource count
    template.resourceCountIs('AWS::Backup::BackupPlan', 1);
  });

  test('Should Create Backup Selection With Tag-Based Resources When Configuration Is Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackSelection', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup selection is created
    template.hasResourceProperties('AWS::Backup::BackupSelection', {
      BackupPlanId: {
        'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*TestBackupPlanStackSelection.*'), 'BackupPlanId']),
      },
      BackupSelection: {
        SelectionName: 'Selection',
        ListOfTags: [
          {
            ConditionType: 'STRINGEQUALS',
            ConditionKey: 'AWSBackup',
            ConditionValue: 'True',
          },
        ],
      },
    });

    // Verify backup selection resource count
    template.resourceCountIs('AWS::Backup::BackupSelection', 1);
  });

  test('Should Use Custom Schedule When Different Backup Schedule Is Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackSchedule', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '30', hour: '6' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup plan has correct schedule
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanRule: [
          {
            ScheduleExpression: 'cron(30 6 * * ? *)',
          },
        ],
      },
    });
  });

  test('Should Use Custom Retention Period When Different Retention Period Is Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackRetention', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(90),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup plan has correct retention period
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanRule: [
          {
            Lifecycle: {
              DeleteAfterDays: 90,
            },
          },
        ],
      },
    });
  });

  test('Should Use Stack Name As Backup Plan Name When Stack Is Created', () => {
    // Arrange & Act
    const stackName = 'BackupPlanStack';
    const stack = new BackupPlanStack(app, stackName, {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup plan uses stack name
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanName: stackName,
      },
    });
  });

  test('Should Work With Cross-Region References When Cross-Region References Are Enabled', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackCrossRegion', {
      vault: primaryVault,
      secondaryVault: secondaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
      crossRegionReferences: true,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup plan is still created with cross-region references enabled
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanName: 'TestBackupPlanStackCrossRegion',
      },
    });

    // Verify stack is created successfully
    expect(stack.stackName).toBe('TestBackupPlanStackCrossRegion');
  });

  test('Should Handle Weekly Backup Schedule When Weekly Schedule Is Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackWeekly', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2', weekDay: '1' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup plan has weekly cron schedule
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanRule: [
          {
            ScheduleExpression: 'cron(0 2 ? * 1 *)',
          },
        ],
      },
    });
  });

  test('Should Create Proper IAM Service Role When Backup Plan Is Created', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackIAM', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup selection has IAM role
    template.hasResourceProperties('AWS::Backup::BackupSelection', {
      BackupSelection: {
        IamRoleArn: {
          'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*Role.*'), 'Arn']),
        },
      },
    });
  });

  test('Should Not Have Copy Actions When Secondary Vault Is Not Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackNoCopy', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Get the backup plan template and verify no copy actions
    const resources = template.toJSON().Resources;
    const backupPlanResource = Object.values(resources).find(
      (resource: any) => resource.Type === 'AWS::Backup::BackupPlan',
    ) as any;

    expect(backupPlanResource).toBeDefined();
    expect(backupPlanResource.Properties.BackupPlan.BackupPlanRule[0].CopyActions).toBeUndefined();
  });

  test('Should Handle Long Retention Periods When Long Retention Period Is Provided', () => {
    // Arrange & Act
    const stack = new BackupPlanStack(app, 'TestBackupPlanStackLongRetention', {
      vault: primaryVault,
      backupSchedule: events.Schedule.cron({ minute: '0', hour: '2' }),
      retentionPeriod: cdk.Duration.days(365),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup plan has long retention period
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanRule: [
          {
            Lifecycle: {
              DeleteAfterDays: 365,
            },
          },
        ],
      },
    });
  });
});
