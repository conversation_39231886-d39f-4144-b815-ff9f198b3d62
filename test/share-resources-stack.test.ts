import * as cdk from 'aws-cdk-lib';
import { Match, Template } from 'aws-cdk-lib/assertions';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
import { config, envKey, getProcEnv, pjPrefix } from '../test';
let app: cdk.App;

beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';
  cdk.Tags.of(app).add(envTagName, envKey);
});

describe(`${pjPrefix} ShareResources Stack`, () => {
  const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
    pjPrefix: pjPrefix,
    notifyEmail: '<EMAIL>',
    channelId: 'test-channel-id',
    workspaceId: 'test-workspace-id',
    domainPrefix: pjPrefix.toLowerCase(),
    urlForCallback: ['https://example.com/callback'],
    urlForLogout: ['https://example.com/logout'],
    myVpcCidr: '10.0.0.0/16',
    myVpcMaxAzs: 2,
    myVpcNatGateways: 1,
    myFlowLogBucketLifecycleRule: [],
    logRemovalPolicyParam: config.LogRemovalPolicyParam,
    kmsPendingWindow: cdk.Duration.days(7),
    env: getProcEnv(),
  });

  const template = Template.fromStack(shareResources);

  test('should match snapshot when stack is created', () => {
    expect(template).toMatchSnapshot();
  });

  test('should expose public properties when stack is initialized', () => {
    expect(shareResources.appKey).toBeDefined();
    expect(shareResources.myVpc).toBeDefined();
    expect(shareResources.alarmTopic).toBeDefined();
  });

  test('should create SNS topic and subscription when notifyEmail is provided', () => {
    template.resourceCountIs('AWS::SNS::Topic', 1);
    template.hasResourceProperties('AWS::SNS::Subscription', {
      Protocol: 'email',
      Endpoint: '<EMAIL>',
    });
  });

  test('should create Chatbot configuration when channelId and workspaceId are provided', () => {
    template.hasResourceProperties('AWS::Chatbot::SlackChannelConfiguration', {
      SlackChannelId: 'test-channel-id',
      SlackWorkspaceId: 'test-workspace-id',
      ConfigurationName: Match.stringLikeRegexp(`${pjPrefix}-Chatbot`),
    });
  });

  test('should create Cognito resources with correct domain when domainPrefix is provided', () => {
    template.resourceCountIs('AWS::Cognito::UserPool', 1);
    template.hasResourceProperties('AWS::Cognito::UserPoolDomain', {
      Domain: pjPrefix.toLowerCase(),
    });
    template.hasResourceProperties('AWS::Cognito::UserPoolClient', {
      CallbackURLs: Match.arrayEquals(['https://example.com/callback']),
      LogoutURLs: Match.arrayEquals(['https://example.com/logout']),
    });
  });

  test('should create KMS key with rotation enabled when stack is initialized', () => {
    template.hasResourceProperties('AWS::KMS::Key', {
      EnableKeyRotation: true,
      PendingWindowInDays: 7,
    });
  });

  test('should create VPC with specified CIDR when myVpcCidr is provided', () => {
    template.hasResourceProperties('AWS::EC2::VPC', {
      CidrBlock: '10.0.0.0/16',
      EnableDnsHostnames: true,
      EnableDnsSupport: true,
    });
  });

  test('should create correct number of subnets when myVpcMaxAzs is set to 2', () => {
    template.resourceCountIs('AWS::EC2::Subnet', 6); // 2 AZs with 3 subnets each (public, private, isolated)
  });

  test('should create specified number of NAT gateways when myVpcNatGateways is set to 1', () => {
    template.resourceCountIs('AWS::EC2::NatGateway', 1);
  });

  test('should create VPC flow logs when stack is initialized', () => {
    template.hasResourceProperties('AWS::EC2::FlowLog', {
      ResourceType: 'VPC',
      TrafficType: 'ALL',
    });
  });

  test('should create 2 NAT gateways when myVpcNatGateways is set to 2', () => {
    const shareResourcesWithMoreNats = new ShareResourcesStack(app, `${pjPrefix}-ShareResources-MoreNats`, {
      pjPrefix: pjPrefix,
      notifyEmail: '<EMAIL>',
      channelId: 'test-channel-id',
      workspaceId: 'test-workspace-id',
      domainPrefix: pjPrefix.toLowerCase(),
      urlForCallback: ['https://example.com/callback'],
      urlForLogout: ['https://example.com/logout'],
      myVpcCidr: '10.0.0.0/16',
      myVpcMaxAzs: 2,
      myVpcNatGateways: 2, // Increased NAT gateways
      myFlowLogBucketLifecycleRule: [],
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: cdk.Duration.days(7),
      env: getProcEnv(),
    });

    const moreNatsTemplate = Template.fromStack(shareResourcesWithMoreNats);
    moreNatsTemplate.resourceCountIs('AWS::EC2::NatGateway', 2);
  });

  test('should maintain subnet count when myVpcMaxAzs is increased to 3', () => {
    const shareResourcesWithMoreAzs = new ShareResourcesStack(app, `${pjPrefix}-ShareResources-MoreAzs`, {
      pjPrefix: pjPrefix,
      notifyEmail: '<EMAIL>',
      channelId: 'test-channel-id',
      workspaceId: 'test-workspace-id',
      domainPrefix: pjPrefix.toLowerCase(),
      urlForCallback: ['https://example.com/callback'],
      urlForLogout: ['https://example.com/logout'],
      myVpcCidr: '10.0.0.0/16',
      myVpcMaxAzs: 3, // Increased max AZs
      myVpcNatGateways: 1,
      myFlowLogBucketLifecycleRule: [],
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: cdk.Duration.days(7),
      env: getProcEnv(),
    });

    const moreAzsTemplate = Template.fromStack(shareResourcesWithMoreAzs);
    moreAzsTemplate.resourceCountIs('AWS::EC2::Subnet', 9); // Should still be 6 subnets
  });

  test('should create KMS key with 14-day pending window when kmsPendingWindow is set to 14 days', () => {
    const shareResourcesWithDiffPendingWindow = new ShareResourcesStack(
      app,
      `${pjPrefix}-ShareResources-DiffPendingWindow`,
      {
        pjPrefix: pjPrefix,
        notifyEmail: '<EMAIL>',
        channelId: 'test-channel-id',
        workspaceId: 'test-workspace-id',
        domainPrefix: pjPrefix.toLowerCase(),
        urlForCallback: ['https://example.com/callback'],
        urlForLogout: ['https://example.com/logout'],
        myVpcCidr: '10.0.0.0/16',
        myVpcMaxAzs: 2,
        myVpcNatGateways: 1,
        myFlowLogBucketLifecycleRule: [],
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
        kmsPendingWindow: cdk.Duration.days(14), // Different pending window
        env: getProcEnv(),
      },
    );

    const diffPendingWindowTemplate = Template.fromStack(shareResourcesWithDiffPendingWindow);
    diffPendingWindowTemplate.hasResourceProperties('AWS::KMS::Key', {
      PendingWindowInDays: 14,
    });
  });

  test('should create Cognito with specified callback URLs when custom URL is provided', () => {
    const shareResourcesWithDiffCallbacks = new ShareResourcesStack(app, `${pjPrefix}-ShareResources-DiffCallbacks`, {
      pjPrefix: pjPrefix,
      notifyEmail: '<EMAIL>',
      channelId: 'test-channel-id',
      workspaceId: 'test-workspace-id',
      domainPrefix: pjPrefix.toLowerCase(),
      urlForCallback: ['https://diff.example.com/callback'], // Different callback URL
      urlForLogout: ['https://example.com/logout'],
      myVpcCidr: '10.0.0.0/16',
      myVpcMaxAzs: 2,
      myVpcNatGateways: 1,
      myFlowLogBucketLifecycleRule: [],
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: cdk.Duration.days(7),
      env: getProcEnv(),
    });

    const diffCallbacksTemplate = Template.fromStack(shareResourcesWithDiffCallbacks);
    diffCallbacksTemplate.hasResourceProperties('AWS::Cognito::UserPoolClient', {
      CallbackURLs: Match.arrayEquals(['https://diff.example.com/callback']),
    });
  });
});
