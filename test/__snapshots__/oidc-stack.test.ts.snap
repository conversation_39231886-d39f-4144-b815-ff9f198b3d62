// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BLEA-Test OIDC Stack should match snapshot when stack is created 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderHandlerF2C543E0": Object {
      "DependsOn": Array [
        "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "62fa02efcaa700e1c247e1d3cc2aa0cd07a0808a9a3e3d2230e51f57a02233fb.zip",
        },
        "Handler": "__entrypoint__.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65",
            "Arn",
          ],
        },
        "Runtime": "nodejs22.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "iam:CreateOpenIDConnectProvider",
                    "iam:DeleteOpenIDConnectProvider",
                    "iam:UpdateOpenIDConnectProviderThumbprint",
                    "iam:AddClientIDToOpenIDConnectProvider",
                    "iam:RemoveClientIDFromOpenIDConnectProvider",
                  ],
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "Inline",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "EcsDeployRole1B3E8031": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:test-org/ecs-deploy-repo:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "EcsDeployRoleDefaultPolicyDB87CE4E": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "cloudformation:DescribeStacks",
                "s3:PutObject",
                "ecr:GetAuthorizationToken",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload",
                "ecr:BatchCheckLayerAvailability",
                "ecr:PutImage",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "EcsDeployRoleDefaultPolicyDB87CE4E",
        "Roles": Array [
          Object {
            "Ref": "EcsDeployRole1B3E8031",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "GithubActionsOidcProviderF9E986BE": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ClientIDList": Array [
          "sts.amazonaws.com",
        ],
        "CodeHash": "62fa02efcaa700e1c247e1d3cc2aa0cd07a0808a9a3e3d2230e51f57a02233fb",
        "RejectUnauthorized": false,
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderHandlerF2C543E0",
            "Arn",
          ],
        },
        "Url": "https://token.actions.githubusercontent.com",
      },
      "Type": "Custom::AWSCDKOpenIdConnectProvider",
      "UpdateReplacePolicy": "Delete",
    },
    "InfraResourcesRoleDefaultPolicy650E5DA3": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "cloudformation:DescribeStacks",
                "s3:PutObject",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "InfraResourcesRoleDefaultPolicy650E5DA3",
        "Roles": Array [
          Object {
            "Ref": "InfraResourcesRoleE6C761A4",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "InfraResourcesRoleE6C761A4": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:test-org/infra-repo:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "WafRoleA713F7DC": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:test-org/waf-repo:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "WafRoleDefaultPolicy729EDA04": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "wafv2:ListWebACLs",
                "wafv2:GetWebACL",
                "wafv2:UpdateWebACL",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "WafRoleDefaultPolicy729EDA04",
        "Roles": Array [
          Object {
            "Ref": "WafRoleA713F7DC",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
