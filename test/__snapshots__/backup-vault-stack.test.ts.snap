// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`The backup vault stack Should Match Expected Snapshot When Backup Vault Stack Is Created With Basic Configuration 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "Vault23237E5B": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "BackupVaultName": "TestBackupVaultStackSnapshotVault1CDC5117",
        "EncryptionKeyArn": Object {
          "Fn::ImportValue": "HelperStack:ExportsOutputFnGetAttTestAppKey723CB44AArn0AAA5B71",
        },
      },
      "Type": "AWS::Backup::BackupVault",
      "UpdateReplacePolicy": "Retain",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
