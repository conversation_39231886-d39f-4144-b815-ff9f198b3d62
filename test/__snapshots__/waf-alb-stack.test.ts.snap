// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BLEA-Test WAF ALB Stack Should snapshot matching When WAF ALB Stack is created 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "WafAlbBLEATestWafAlbWafLogsBucket47B52E85",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs22.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "WafAlbBLEATestWafAlbWafLogsBucket47B52E85": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "BucketName": "aws-waf-logs-123456789012-blea-test-wafalb-bucket",
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "WafAlbBLEATestWafAlbWafLogsBucketAutoDeleteObjectsCustomResource738D6BB4": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "WafAlbBLEATestWafAlbWafLogsBucketPolicyCAE6CBAC",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "WafAlbBLEATestWafAlbWafLogsBucket47B52E85",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "WafAlbBLEATestWafAlbWafLogsBucketPolicyCAE6CBAC": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "WafAlbBLEATestWafAlbWafLogsBucket47B52E85",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "WafAlbBLEATestWafAlbWafLogsBucket47B52E85",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "WafAlbBLEATestWafAlbWafLogsBucket47B52E85",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "WafAlbBLEATestWafAlbWebAcl0612EC87": Object {
      "Properties": Object {
        "DefaultAction": Object {
          "Allow": Object {},
        },
        "Rules": Array [
          Object {
            "Action": Object {
              "Allow": Object {},
            },
            "Name": "IPset",
            "Priority": 11,
            "Statement": Object {
              "IPSetReferenceStatement": Object {
                "Arn": Object {
                  "Fn::GetAtt": Array [
                    "WafAlbIPset08CA3181",
                    "Arn",
                  ],
                },
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "IPset",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Action": Object {
              "Allow": Object {},
            },
            "Name": "preSharedKey",
            "Priority": 12,
            "Statement": Object {
              "ByteMatchStatement": Object {
                "FieldToMatch": Object {
                  "SingleHeader": Object {
                    "name": "x-pre-shared-key",
                  },
                },
                "PositionalConstraint": "EXACTLY",
                "SearchString": "cc56a81d",
                "TextTransformations": Array [
                  Object {
                    "Priority": 0,
                    "Type": "NONE",
                  },
                ],
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "IPset",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesCommonRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 2,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesCommonRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesCommonRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesKnownBadInputsRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 3,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesKnownBadInputsRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesKnownBadInputsRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesAmazonIpReputationList",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 4,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesAmazonIpReputationList",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesAmazonIpReputationList",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesLinuxRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 5,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesLinuxRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesLinuxRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesSQLiRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 6,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesSQLiRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesSQLiRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Action": Object {
              "Block": Object {},
            },
            "Name": "CSIRTBlockSpecificIPs",
            "Priority": 0,
            "Statement": Object {
              "IPSetReferenceStatement": Object {
                "Arn": "arn:aws:wafv2:ap-northeast-1:111111111111:regional/ipset/CSIRTIpSet/XXXX",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "CSIRTBlockSpecificIPs",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "CSIRTManagerRules",
            "OverrideAction": Object {
              "None": Object {},
            },
            "Priority": 1,
            "Statement": Object {
              "RuleGroupReferenceStatement": Object {
                "Arn": "arn:aws:wafv2:ap-northeast-1:111111111111:regional/rulegroup/CSIRTManagerRules/XXXX",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "CSIRTManagerRules",
              "SampledRequestsEnabled": true,
            },
          },
        ],
        "Scope": "REGIONAL",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VisibilityConfig": Object {
          "CloudWatchMetricsEnabled": true,
          "MetricName": "WafAcl",
          "SampledRequestsEnabled": true,
        },
      },
      "Type": "AWS::WAFv2::WebACL",
    },
    "WafAlbBLEATestWafAlbwafV2LoggingConfiguration6947FEDE": Object {
      "DependsOn": Array [
        "WafAlbBLEATestWafAlbWafLogsBucket47B52E85",
        "WafAlbBLEATestWafAlbWebAcl0612EC87",
      ],
      "Properties": Object {
        "LogDestinationConfigs": Array [
          Object {
            "Fn::GetAtt": Array [
              "WafAlbBLEATestWafAlbWafLogsBucket47B52E85",
              "Arn",
            ],
          },
        ],
        "ResourceArn": Object {
          "Fn::GetAtt": Array [
            "WafAlbBLEATestWafAlbWebAcl0612EC87",
            "Arn",
          ],
        },
      },
      "Type": "AWS::WAFv2::LoggingConfiguration",
    },
    "WafAlbIPset08CA3181": Object {
      "Properties": Object {
        "Addresses": Array [
          "***************/25",
        ],
        "IPAddressVersion": "IPV4",
        "Name": "IPset",
        "Scope": "REGIONAL",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::WAFv2::IPSet",
    },
    "WafAlbWebAclAssociation07344E7B9": Object {
      "Properties": Object {
        "ResourceArn": "arn:aws:elasticloadbalancing:ap-northeast-1:123456789012:loadbalancer/app/test-alb/1234567890123456",
        "WebACLArn": Object {
          "Fn::GetAtt": Array [
            "WafAlbBLEATestWafAlbWebAcl0612EC87",
            "Arn",
          ],
        },
      },
      "Type": "AWS::WAFv2::WebACLAssociation",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
