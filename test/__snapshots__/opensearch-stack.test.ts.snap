// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BLEA-Test OpenSearch Stack should match snapshot when stack is created 1`] = `
Object {
  "Outputs": Object {
    "ExportsOutputFnGetAttAppSecurityGroupC396D536GroupId4ECD26A6": Object {
      "Export": Object {
        "Name": "BLEA-Test-OpenSearch:ExportsOutputFnGetAttAppSecurityGroupC396D536GroupId4ECD26A6",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "AppSecurityGroupC396D536",
          "GroupId",
        ],
      },
    },
    "ExportsOutputFnGetAttBastionSecurityGroupDAB89EBDGroupId0F2FE988": Object {
      "Export": Object {
        "Name": "BLEA-Test-OpenSearch:ExportsOutputFnGetAttBastionSecurityGroupDAB89EBDGroupId0F2FE988",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "BastionSecurityGroupDAB89EBD",
          "GroupId",
        ],
      },
    },
    "ExportsOutputRefOpenSearchVpcAC31F793BFF6C56B": Object {
      "Export": Object {
        "Name": "BLEA-Test-OpenSearch:ExportsOutputRefOpenSearchVpcAC31F793BFF6C56B",
      },
      "Value": Object {
        "Ref": "OpenSearchVpcAC31F793",
      },
    },
    "ExportsOutputRefOpenSearchVpcIsolatedSubnet1Subnet6D017270B4E7B4C2": Object {
      "Export": Object {
        "Name": "BLEA-Test-OpenSearch:ExportsOutputRefOpenSearchVpcIsolatedSubnet1Subnet6D017270B4E7B4C2",
      },
      "Value": Object {
        "Ref": "OpenSearchVpcIsolatedSubnet1Subnet6D017270",
      },
    },
    "ExportsOutputRefOpenSearchVpcIsolatedSubnet2Subnet278C09C7453F4DDC": Object {
      "Export": Object {
        "Name": "BLEA-Test-OpenSearch:ExportsOutputRefOpenSearchVpcIsolatedSubnet2Subnet278C09C7453F4DDC",
      },
      "Value": Object {
        "Ref": "OpenSearchVpcIsolatedSubnet2Subnet278C09C7",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "AppSecurityGroupC396D536": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-OpenSearch/AppSecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BastionSecurityGroupDAB89EBD": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-OpenSearch/BastionSecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "OpenSearchVpcAC31F793": Object {
      "Properties": Object {
        "CidrBlock": "10.0.0.0/16",
        "EnableDnsHostnames": true,
        "EnableDnsSupport": true,
        "InstanceTenancy": "default",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc",
          },
        ],
      },
      "Type": "AWS::EC2::VPC",
    },
    "OpenSearchVpcIGW01DD8EF8": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc",
          },
        ],
      },
      "Type": "AWS::EC2::InternetGateway",
    },
    "OpenSearchVpcIsolatedSubnet1RouteTable0F844C7D": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/IsolatedSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "OpenSearchVpcIsolatedSubnet1RouteTableAssociation104B98E6": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcIsolatedSubnet1RouteTable0F844C7D",
        },
        "SubnetId": Object {
          "Ref": "OpenSearchVpcIsolatedSubnet1Subnet6D017270",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "OpenSearchVpcIsolatedSubnet1Subnet6D017270": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "********/24",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Isolated",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Isolated",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/IsolatedSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "OpenSearchVpcIsolatedSubnet2RouteTableAssociation8038CE28": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcIsolatedSubnet2RouteTableE500F5AF",
        },
        "SubnetId": Object {
          "Ref": "OpenSearchVpcIsolatedSubnet2Subnet278C09C7",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "OpenSearchVpcIsolatedSubnet2RouteTableE500F5AF": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/IsolatedSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "OpenSearchVpcIsolatedSubnet2Subnet278C09C7": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "********/24",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Isolated",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Isolated",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/IsolatedSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "OpenSearchVpcPrivateSubnet1DefaultRoute4C5FFE4C": Object {
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "NatGatewayId": Object {
          "Ref": "OpenSearchVpcPublicSubnet1NATGateway862C04D1",
        },
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcPrivateSubnet1RouteTableEBEEEC3C",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "OpenSearchVpcPrivateSubnet1RouteTableAssociation6B8448B7": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcPrivateSubnet1RouteTableEBEEEC3C",
        },
        "SubnetId": Object {
          "Ref": "OpenSearchVpcPrivateSubnet1Subnet83143899",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "OpenSearchVpcPrivateSubnet1RouteTableEBEEEC3C": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PrivateSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "OpenSearchVpcPrivateSubnet1Subnet83143899": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "********/24",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Private",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Private",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PrivateSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "OpenSearchVpcPrivateSubnet2DefaultRoute016D09D0": Object {
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "NatGatewayId": Object {
          "Ref": "OpenSearchVpcPublicSubnet2NATGateway65AE72C6",
        },
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcPrivateSubnet2RouteTable0CE0FB59",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "OpenSearchVpcPrivateSubnet2RouteTable0CE0FB59": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PrivateSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "OpenSearchVpcPrivateSubnet2RouteTableAssociation5D436170": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcPrivateSubnet2RouteTable0CE0FB59",
        },
        "SubnetId": Object {
          "Ref": "OpenSearchVpcPrivateSubnet2Subnet42E18FC2",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "OpenSearchVpcPrivateSubnet2Subnet42E18FC2": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "********/24",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Private",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Private",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PrivateSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "OpenSearchVpcPublicSubnet1DefaultRoute1BC1B9D5": Object {
      "DependsOn": Array [
        "OpenSearchVpcVPCGWE1173B50",
      ],
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "GatewayId": Object {
          "Ref": "OpenSearchVpcIGW01DD8EF8",
        },
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcPublicSubnet1RouteTableB0BF64BF",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "OpenSearchVpcPublicSubnet1EIPAA769616": Object {
      "Properties": Object {
        "Domain": "vpc",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PublicSubnet1",
          },
        ],
      },
      "Type": "AWS::EC2::EIP",
    },
    "OpenSearchVpcPublicSubnet1NATGateway862C04D1": Object {
      "DependsOn": Array [
        "OpenSearchVpcPublicSubnet1DefaultRoute1BC1B9D5",
        "OpenSearchVpcPublicSubnet1RouteTableAssociation48D68450",
      ],
      "Properties": Object {
        "AllocationId": Object {
          "Fn::GetAtt": Array [
            "OpenSearchVpcPublicSubnet1EIPAA769616",
            "AllocationId",
          ],
        },
        "SubnetId": Object {
          "Ref": "OpenSearchVpcPublicSubnet1SubnetF00258E7",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PublicSubnet1",
          },
        ],
      },
      "Type": "AWS::EC2::NatGateway",
    },
    "OpenSearchVpcPublicSubnet1RouteTableAssociation48D68450": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcPublicSubnet1RouteTableB0BF64BF",
        },
        "SubnetId": Object {
          "Ref": "OpenSearchVpcPublicSubnet1SubnetF00258E7",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "OpenSearchVpcPublicSubnet1RouteTableB0BF64BF": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PublicSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "OpenSearchVpcPublicSubnet1SubnetF00258E7": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "10.0.0.0/24",
        "MapPublicIpOnLaunch": true,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Public",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Public",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PublicSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "OpenSearchVpcPublicSubnet2DefaultRoute63E89CB5": Object {
      "DependsOn": Array [
        "OpenSearchVpcVPCGWE1173B50",
      ],
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "GatewayId": Object {
          "Ref": "OpenSearchVpcIGW01DD8EF8",
        },
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcPublicSubnet2RouteTableB4754520",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "OpenSearchVpcPublicSubnet2EIP22263061": Object {
      "Properties": Object {
        "Domain": "vpc",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PublicSubnet2",
          },
        ],
      },
      "Type": "AWS::EC2::EIP",
    },
    "OpenSearchVpcPublicSubnet2NATGateway65AE72C6": Object {
      "DependsOn": Array [
        "OpenSearchVpcPublicSubnet2DefaultRoute63E89CB5",
        "OpenSearchVpcPublicSubnet2RouteTableAssociation9377A375",
      ],
      "Properties": Object {
        "AllocationId": Object {
          "Fn::GetAtt": Array [
            "OpenSearchVpcPublicSubnet2EIP22263061",
            "AllocationId",
          ],
        },
        "SubnetId": Object {
          "Ref": "OpenSearchVpcPublicSubnet2Subnet95A270A2",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PublicSubnet2",
          },
        ],
      },
      "Type": "AWS::EC2::NatGateway",
    },
    "OpenSearchVpcPublicSubnet2RouteTableAssociation9377A375": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "OpenSearchVpcPublicSubnet2RouteTableB4754520",
        },
        "SubnetId": Object {
          "Ref": "OpenSearchVpcPublicSubnet2Subnet95A270A2",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "OpenSearchVpcPublicSubnet2RouteTableB4754520": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PublicSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "OpenSearchVpcPublicSubnet2Subnet95A270A2": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "********/24",
        "MapPublicIpOnLaunch": true,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Public",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Public",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-OpenSearch/OpenSearchVpc/PublicSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "OpenSearchVpcVPCGWE1173B50": Object {
      "Properties": Object {
        "InternetGatewayId": Object {
          "Ref": "OpenSearchVpcIGW01DD8EF8",
        },
        "VpcId": Object {
          "Ref": "OpenSearchVpcAC31F793",
        },
      },
      "Type": "AWS::EC2::VPCGatewayAttachment",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
