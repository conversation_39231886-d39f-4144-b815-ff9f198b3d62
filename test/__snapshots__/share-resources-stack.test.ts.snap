// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BLEA-Test ShareResources Stack should match snapshot when stack is created 1`] = `
Object {
  "Outputs": Object {
    "BLEATestVpcsubnetID39BDEBCB": Object {
      "Value": Object {
        "Ref": "BLEATestVpcPrivateSubnet1Subnet554CAFDD",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BLEATestAlarmNotifyEmail377625C0": Object {
      "Properties": Object {
        "Endpoint": "<EMAIL>",
        "Protocol": "email",
        "TopicArn": Object {
          "Ref": "BLEATestAlarmSNSTopic4CD80F3D",
        },
      },
      "Type": "AWS::SNS::Subscription",
    },
    "BLEATestAlarmSNSTopic4CD80F3D": Object {
      "Type": "AWS::SNS::Topic",
    },
    "BLEATestAlarmSNSTopicPolicy939C8A0A": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sns:Publish",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "cloudwatch.amazonaws.com",
              },
              "Resource": Object {
                "Ref": "BLEATestAlarmSNSTopic4CD80F3D",
              },
              "Sid": "0",
            },
          ],
          "Version": "2012-10-17",
        },
        "Topics": Array [
          Object {
            "Ref": "BLEATestAlarmSNSTopic4CD80F3D",
          },
        ],
      },
      "Type": "AWS::SNS::TopicPolicy",
    },
    "BLEATestAppKey9EEB3404": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Description": "Custom KMS key",
        "EnableKeyRotation": true,
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::123456789012:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "kms:Encrypt*",
                "kms:Decrypt*",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:Describe*",
              ],
              "Condition": Object {
                "ArnLike": Object {
                  "kms:EncryptionContext:aws:logs:arn": "arn:aws:logs:ap-northeast-1:123456789012:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Service": "logs.ap-northeast-1.amazonaws.com",
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PendingWindowInDays": 7,
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestAppKeyAliasF6487C2B": Object {
      "Properties": Object {
        "AliasName": "alias/BLEA-Test-AppKey-for-app",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "BLEATestAppKey9EEB3404",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
    },
    "BLEATestChatbotChatbotChannelB2421A0F": Object {
      "Properties": Object {
        "ConfigurationName": "BLEA-Test-Chatbot-test-workspace-id",
        "IamRoleArn": Object {
          "Fn::GetAtt": Array [
            "BLEATestChatbotChatbotRole781C9432",
            "Arn",
          ],
        },
        "SlackChannelId": "test-channel-id",
        "SlackWorkspaceId": "test-workspace-id",
        "SnsTopicArns": Array [
          Object {
            "Ref": "BLEATestAlarmSNSTopic4CD80F3D",
          },
        ],
      },
      "Type": "AWS::Chatbot::SlackChannelConfiguration",
    },
    "BLEATestChatbotChatbotRole781C9432": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "chatbot.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/ReadOnlyAccess",
              ],
            ],
          },
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/CloudWatchReadOnlyAccess",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "BLEATestCognitoCognitoUserPool6A0244BA": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccountRecoverySetting": Object {
          "RecoveryMechanisms": Array [
            Object {
              "Name": "verified_phone_number",
              "Priority": 1,
            },
            Object {
              "Name": "verified_email",
              "Priority": 2,
            },
          ],
        },
        "AdminCreateUserConfig": Object {
          "AllowAdminCreateUserOnly": true,
        },
        "EmailVerificationMessage": "The verification code to your new account is {####}",
        "EmailVerificationSubject": "Verify your new account",
        "SmsVerificationMessage": "The verification code to your new account is {####}",
        "VerificationMessageTemplate": Object {
          "DefaultEmailOption": "CONFIRM_WITH_CODE",
          "EmailMessage": "The verification code to your new account is {####}",
          "EmailSubject": "Verify your new account",
          "SmsMessage": "The verification code to your new account is {####}",
        },
      },
      "Type": "AWS::Cognito::UserPool",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCognitoCognitoUserPoolUserPoolClientC32AE0ED": Object {
      "Properties": Object {
        "AllowedOAuthFlows": Array [
          "implicit",
          "code",
        ],
        "AllowedOAuthFlowsUserPoolClient": true,
        "AllowedOAuthScopes": Array [
          "email",
          "openid",
          "profile",
        ],
        "CallbackURLs": Array [
          "https://example.com/callback",
        ],
        "GenerateSecret": false,
        "LogoutURLs": Array [
          "https://example.com/logout",
        ],
        "SupportedIdentityProviders": Array [
          "COGNITO",
        ],
        "UserPoolId": Object {
          "Ref": "BLEATestCognitoCognitoUserPool6A0244BA",
        },
      },
      "Type": "AWS::Cognito::UserPoolClient",
    },
    "BLEATestCognitoCognitoUserPoolUserPoolDomain886DA792": Object {
      "Properties": Object {
        "Domain": "blea-test",
        "UserPoolId": Object {
          "Ref": "BLEATestCognitoCognitoUserPool6A0244BA",
        },
      },
      "Type": "AWS::Cognito::UserPoolDomain",
    },
    "BLEATestVpc22A2B86A": Object {
      "Properties": Object {
        "CidrBlock": "10.0.0.0/16",
        "EnableDnsHostnames": true,
        "EnableDnsSupport": true,
        "InstanceTenancy": "default",
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
      },
      "Type": "AWS::EC2::VPC",
    },
    "BLEATestVpcEc2EndpointForPrivate716A6A7A": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "BLEATestVpcEc2EndpointForPrivateSecurityGroup91FA12BE",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ec2",
        "SubnetIds": Array [
          Object {
            "Ref": "BLEATestVpcProtectedSubnet1Subnet718D92D4",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet2Subnet2D0D6510",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "BLEATestVpcEc2EndpointForPrivateSecurityGroup91FA12BE": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/Ec2EndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "BLEATestVpc22A2B86A",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "BLEATestVpc22A2B86A",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BLEATestVpcEc2MessagesEndpointForPrivateEE362B16": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "BLEATestVpcEc2MessagesEndpointForPrivateSecurityGroupFF567564",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ec2messages",
        "SubnetIds": Array [
          Object {
            "Ref": "BLEATestVpcProtectedSubnet1Subnet718D92D4",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet2Subnet2D0D6510",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "BLEATestVpcEc2MessagesEndpointForPrivateSecurityGroupFF567564": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/Ec2MessagesEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "BLEATestVpc22A2B86A",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "BLEATestVpc22A2B86A",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BLEATestVpcEcrDkrEndpointForPrivate7573B443": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "BLEATestVpcEcrDkrEndpointForPrivateSecurityGroup4F3C5300",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ecr.dkr",
        "SubnetIds": Array [
          Object {
            "Ref": "BLEATestVpcProtectedSubnet1Subnet718D92D4",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet2Subnet2D0D6510",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "BLEATestVpcEcrDkrEndpointForPrivateSecurityGroup4F3C5300": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/EcrDkrEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "BLEATestVpc22A2B86A",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "BLEATestVpc22A2B86A",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BLEATestVpcEcrEndpointForPrivate372A66DB": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "BLEATestVpcEcrEndpointForPrivateSecurityGroup0005AC2A",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ecr.api",
        "SubnetIds": Array [
          Object {
            "Ref": "BLEATestVpcProtectedSubnet1Subnet718D92D4",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet2Subnet2D0D6510",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "BLEATestVpcEcrEndpointForPrivateSecurityGroup0005AC2A": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/EcrEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "BLEATestVpc22A2B86A",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "BLEATestVpc22A2B86A",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BLEATestVpcFlowLogBucket7070E91C": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "KMSMasterKeyID": Object {
                  "Fn::GetAtt": Array [
                    "BLEATestVpcKeyAF1ADC0A",
                    "Arn",
                  ],
                },
                "SSEAlgorithm": "aws:kms",
              },
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestVpcFlowLogBucketAutoDeleteObjectsCustomResource368F82BD": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "BLEATestVpcFlowLogBucketPolicyC5B308BD",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "BLEATestVpcFlowLogBucket7070E91C",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestVpcFlowLogBucketPolicyC5B308BD": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "BLEATestVpcFlowLogBucket7070E91C",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestVpcFlowLogBucket7070E91C",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestVpcFlowLogBucket7070E91C",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestVpcFlowLogBucket7070E91C",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestVpcFlowLogBucket7070E91C",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "BLEATestVpcFlowLogsFlowLogF0C8C2D8": Object {
      "DependsOn": Array [
        "BLEATestVpcFlowLogBucketAutoDeleteObjectsCustomResource368F82BD",
        "BLEATestVpcFlowLogBucketPolicyC5B308BD",
      ],
      "Properties": Object {
        "LogDestination": Object {
          "Fn::GetAtt": Array [
            "BLEATestVpcFlowLogBucket7070E91C",
            "Arn",
          ],
        },
        "LogDestinationType": "s3",
        "ResourceId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
        "ResourceType": "VPC",
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/FlowLogs",
          },
        ],
        "TrafficType": "ALL",
      },
      "Type": "AWS::EC2::FlowLog",
    },
    "BLEATestVpcIGW3B6BF359": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
      },
      "Type": "AWS::EC2::InternetGateway",
    },
    "BLEATestVpcKeyAF1ADC0A": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Description": "for VPC Flow log",
        "EnableKeyRotation": true,
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::123456789012:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "kms:Encrypt*",
                "kms:Decrypt*",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:Describe*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "Service": "delivery.logs.amazonaws.com",
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PendingWindowInDays": 7,
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestVpcKeyAliasDEB60FBB": Object {
      "Properties": Object {
        "AliasName": "alias/BLEA-Test-Vpc-for-flowlog",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "BLEATestVpcKeyAF1ADC0A",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
    },
    "BLEATestVpcLogsEndpointForPrivate30B0A7E6": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "BLEATestVpcLogsEndpointForPrivateSecurityGroup5E9595E3",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.logs",
        "SubnetIds": Array [
          Object {
            "Ref": "BLEATestVpcProtectedSubnet1Subnet718D92D4",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet2Subnet2D0D6510",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "BLEATestVpcLogsEndpointForPrivateSecurityGroup5E9595E3": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/LogsEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "BLEATestVpc22A2B86A",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "BLEATestVpc22A2B86A",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BLEATestVpcNaclPrivate8505AAFC": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/NaclPrivate",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::NetworkAcl",
    },
    "BLEATestVpcNaclPrivateDefaultAssociationBLEATestShareResourcesBLEATestVpcPrivateSubnet14B7840FFB9BD47F1": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "BLEATestVpcNaclPrivate8505AAFC",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPrivateSubnet1Subnet554CAFDD",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "BLEATestVpcNaclPrivateDefaultAssociationBLEATestShareResourcesBLEATestVpcPrivateSubnet2074EB5D248377B52": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "BLEATestVpcNaclPrivate8505AAFC",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPrivateSubnet2SubnetFF40AA9E",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "BLEATestVpcNaclPrivateNaclEgressPrivate762E76E5": Object {
      "Properties": Object {
        "CidrBlock": "0.0.0.0/0",
        "Egress": true,
        "NetworkAclId": Object {
          "Ref": "BLEATestVpcNaclPrivate8505AAFC",
        },
        "Protocol": -1,
        "RuleAction": "allow",
        "RuleNumber": 100,
      },
      "Type": "AWS::EC2::NetworkAclEntry",
    },
    "BLEATestVpcNaclPrivateNaclIngressPrivate4B5113C7": Object {
      "Properties": Object {
        "CidrBlock": "0.0.0.0/0",
        "Egress": false,
        "NetworkAclId": Object {
          "Ref": "BLEATestVpcNaclPrivate8505AAFC",
        },
        "Protocol": -1,
        "RuleAction": "allow",
        "RuleNumber": 120,
      },
      "Type": "AWS::EC2::NetworkAclEntry",
    },
    "BLEATestVpcNaclPublic66896366": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/NaclPublic",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::NetworkAcl",
    },
    "BLEATestVpcNaclPublicDefaultAssociationBLEATestShareResourcesBLEATestVpcPublicSubnet142026681E52CDF0C": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "BLEATestVpcNaclPublic66896366",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPublicSubnet1Subnet711A27D6",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "BLEATestVpcNaclPublicDefaultAssociationBLEATestShareResourcesBLEATestVpcPublicSubnet2C7FB0344FAD36BED": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "BLEATestVpcNaclPublic66896366",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPublicSubnet2Subnet6CBA85C9",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "BLEATestVpcNaclPublicNaclEgressPublic07AF8BE1": Object {
      "Properties": Object {
        "CidrBlock": "0.0.0.0/0",
        "Egress": true,
        "NetworkAclId": Object {
          "Ref": "BLEATestVpcNaclPublic66896366",
        },
        "Protocol": -1,
        "RuleAction": "allow",
        "RuleNumber": 100,
      },
      "Type": "AWS::EC2::NetworkAclEntry",
    },
    "BLEATestVpcNaclPublicNaclIngressPublicAC80B7A2": Object {
      "Properties": Object {
        "CidrBlock": "0.0.0.0/0",
        "Egress": false,
        "NetworkAclId": Object {
          "Ref": "BLEATestVpcNaclPublic66896366",
        },
        "Protocol": -1,
        "RuleAction": "allow",
        "RuleNumber": 100,
      },
      "Type": "AWS::EC2::NetworkAclEntry",
    },
    "BLEATestVpcPrivateSubnet1DefaultRouteB6733D48": Object {
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "NatGatewayId": Object {
          "Ref": "BLEATestVpcPublicSubnet1NATGateway105FD473",
        },
        "RouteTableId": Object {
          "Ref": "BLEATestVpcPrivateSubnet1RouteTable9D99AAE8",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "BLEATestVpcPrivateSubnet1RouteTable9D99AAE8": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PrivateSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "BLEATestVpcPrivateSubnet1RouteTableAssociationCD824D17": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "BLEATestVpcPrivateSubnet1RouteTable9D99AAE8",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPrivateSubnet1Subnet554CAFDD",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "BLEATestVpcPrivateSubnet1Subnet554CAFDD": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "********/22",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Private",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Private",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PrivateSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "BLEATestVpcPrivateSubnet2DefaultRoute3C5F738F": Object {
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "NatGatewayId": Object {
          "Ref": "BLEATestVpcPublicSubnet1NATGateway105FD473",
        },
        "RouteTableId": Object {
          "Ref": "BLEATestVpcPrivateSubnet2RouteTable05F76268",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "BLEATestVpcPrivateSubnet2RouteTable05F76268": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PrivateSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "BLEATestVpcPrivateSubnet2RouteTableAssociationE19C9D76": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "BLEATestVpcPrivateSubnet2RouteTable05F76268",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPrivateSubnet2SubnetFF40AA9E",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "BLEATestVpcPrivateSubnet2SubnetFF40AA9E": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "********/22",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Private",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Private",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PrivateSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "BLEATestVpcProtectedSubnet1RouteTableAssociation1FB460F0": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "BLEATestVpcProtectedSubnet1RouteTableEEDE5824",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcProtectedSubnet1Subnet718D92D4",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "BLEATestVpcProtectedSubnet1RouteTableEEDE5824": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/ProtectedSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "BLEATestVpcProtectedSubnet1Subnet718D92D4": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "*********/22",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Protected",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Isolated",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/ProtectedSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "BLEATestVpcProtectedSubnet2RouteTableAssociationD45A34D0": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "BLEATestVpcProtectedSubnet2RouteTableB5D86F33",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcProtectedSubnet2Subnet2D0D6510",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "BLEATestVpcProtectedSubnet2RouteTableB5D86F33": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/ProtectedSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "BLEATestVpcProtectedSubnet2Subnet2D0D6510": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "*********/22",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Protected",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Isolated",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/ProtectedSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "BLEATestVpcPublicSubnet1DefaultRoute245B2512": Object {
      "DependsOn": Array [
        "BLEATestVpcVPCGW3312B4AD",
      ],
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "GatewayId": Object {
          "Ref": "BLEATestVpcIGW3B6BF359",
        },
        "RouteTableId": Object {
          "Ref": "BLEATestVpcPublicSubnet1RouteTable5C6EE195",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "BLEATestVpcPublicSubnet1EIP7C88B28A": Object {
      "Properties": Object {
        "Domain": "vpc",
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PublicSubnet1",
          },
        ],
      },
      "Type": "AWS::EC2::EIP",
    },
    "BLEATestVpcPublicSubnet1NATGateway105FD473": Object {
      "DependsOn": Array [
        "BLEATestVpcPublicSubnet1DefaultRoute245B2512",
        "BLEATestVpcPublicSubnet1RouteTableAssociationD048239E",
      ],
      "Properties": Object {
        "AllocationId": Object {
          "Fn::GetAtt": Array [
            "BLEATestVpcPublicSubnet1EIP7C88B28A",
            "AllocationId",
          ],
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPublicSubnet1Subnet711A27D6",
        },
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PublicSubnet1",
          },
        ],
      },
      "Type": "AWS::EC2::NatGateway",
    },
    "BLEATestVpcPublicSubnet1RouteTable5C6EE195": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PublicSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "BLEATestVpcPublicSubnet1RouteTableAssociationD048239E": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "BLEATestVpcPublicSubnet1RouteTable5C6EE195",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPublicSubnet1Subnet711A27D6",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "BLEATestVpcPublicSubnet1Subnet711A27D6": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "10.0.0.0/24",
        "MapPublicIpOnLaunch": true,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Public",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Public",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PublicSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "BLEATestVpcPublicSubnet2DefaultRouteEBA5F8D0": Object {
      "DependsOn": Array [
        "BLEATestVpcVPCGW3312B4AD",
      ],
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "GatewayId": Object {
          "Ref": "BLEATestVpcIGW3B6BF359",
        },
        "RouteTableId": Object {
          "Ref": "BLEATestVpcPublicSubnet2RouteTable0824475E",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "BLEATestVpcPublicSubnet2RouteTable0824475E": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PublicSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "BLEATestVpcPublicSubnet2RouteTableAssociation984FFC16": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "BLEATestVpcPublicSubnet2RouteTable0824475E",
        },
        "SubnetId": Object {
          "Ref": "BLEATestVpcPublicSubnet2Subnet6CBA85C9",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "BLEATestVpcPublicSubnet2Subnet6CBA85C9": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "********/24",
        "MapPublicIpOnLaunch": true,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Public",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Public",
          },
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/PublicSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "BLEATestVpcS3EndpointForPrivateC74529C9": Object {
      "Properties": Object {
        "RouteTableIds": Array [
          Object {
            "Ref": "BLEATestVpcPrivateSubnet1RouteTable9D99AAE8",
          },
          Object {
            "Ref": "BLEATestVpcPrivateSubnet2RouteTable05F76268",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet1RouteTableEEDE5824",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet2RouteTableB5D86F33",
          },
        ],
        "ServiceName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "com.amazonaws.",
              Object {
                "Ref": "AWS::Region",
              },
              ".s3",
            ],
          ],
        },
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcEndpointType": "Gateway",
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "BLEATestVpcSsmEndpointForPrivate45A44FAB": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "BLEATestVpcSsmEndpointForPrivateSecurityGroup1CB47FDE",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ssm",
        "SubnetIds": Array [
          Object {
            "Ref": "BLEATestVpcProtectedSubnet1Subnet718D92D4",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet2Subnet2D0D6510",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "BLEATestVpcSsmEndpointForPrivateSecurityGroup1CB47FDE": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/SsmEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "BLEATestVpc22A2B86A",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "BLEATestVpc22A2B86A",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BLEATestVpcSsmMessagesEndpointForPrivate31964C44": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "BLEATestVpcSsmMessagesEndpointForPrivateSecurityGroup3C4F2360",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ssmmessages",
        "SubnetIds": Array [
          Object {
            "Ref": "BLEATestVpcProtectedSubnet1Subnet718D92D4",
          },
          Object {
            "Ref": "BLEATestVpcProtectedSubnet2Subnet2D0D6510",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "BLEATestVpcSsmMessagesEndpointForPrivateSecurityGroup3C4F2360": Object {
      "Properties": Object {
        "GroupDescription": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc/SsmMessagesEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "BLEATestVpc22A2B86A",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "BLEATestVpc22A2B86A",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-ShareResources/BLEA-Test-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BLEATestVpcVPCGW3312B4AD": Object {
      "Properties": Object {
        "InternetGatewayId": Object {
          "Ref": "BLEATestVpcIGW3B6BF359",
        },
        "VpcId": Object {
          "Ref": "BLEATestVpc22A2B86A",
        },
      },
      "Type": "AWS::EC2::VPCGatewayAttachment",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "BLEATestVpcFlowLogBucket7070E91C",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs22.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
