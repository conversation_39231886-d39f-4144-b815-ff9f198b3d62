// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BLEA-Test WafCloudfront Stack should match snapshot when stack is created 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BLEATestWafCloudfrontWebAcl3578D13D": Object {
      "Properties": Object {
        "DefaultAction": Object {
          "Allow": Object {},
        },
        "Rules": Array [
          Object {
            "Name": "AWSManagedRulesCommonRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 2,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesCommonRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesCommonRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesKnownBadInputsRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 3,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesKnownBadInputsRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesKnownBadInputsRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesAmazonIpReputationList",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 4,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesAmazonIpReputationList",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesAmazonIpReputationList",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesLinuxRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 5,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesLinuxRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesLinuxRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesSQLiRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 6,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesSQLiRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesSQLiRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
        ],
        "Scope": "CLOUDFRONT",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VisibilityConfig": Object {
          "CloudWatchMetricsEnabled": true,
          "MetricName": "WafAcl",
          "SampledRequestsEnabled": true,
        },
      },
      "Type": "AWS::WAFv2::WebACL",
    },
    "BLEATestWafCloudfrontWebAclBLEATestWafCloudfrontWafLogsBucket01FD21C1": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "BucketName": "aws-waf-logs-123456789012-blea-test-wafcloudfront-bucket",
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Retain",
    },
    "BLEATestWafCloudfrontWebAclBLEATestWafCloudfrontwafV2LoggingConfiguration3503848A": Object {
      "DependsOn": Array [
        "BLEATestWafCloudfrontWebAcl3578D13D",
      ],
      "Properties": Object {
        "LogDestinationConfigs": Array [
          Object {
            "Fn::GetAtt": Array [
              "BLEATestWafCloudfrontWebAclBLEATestWafCloudfrontWafLogsBucket01FD21C1",
              "Arn",
            ],
          },
        ],
        "ResourceArn": Object {
          "Fn::GetAtt": Array [
            "BLEATestWafCloudfrontWebAcl3578D13D",
            "Arn",
          ],
        },
      },
      "Type": "AWS::WAFv2::LoggingConfiguration",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
