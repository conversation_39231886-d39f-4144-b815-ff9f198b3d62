// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`The EFS stack Should Match Expected Snapshot When EFS Stack Is Created With Basic Configuration 1`] = `
Object {
  "Outputs": Object {
    "EfsEfsFileSystemId35E16D6A": Object {
      "Export": Object {
        "Name": "FileSystemId",
      },
      "Value": Object {
        "Ref": "EfsEfsFileSystemD6411CDE",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "EfsEfsFileSystemD6411CDE": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "Encrypted": true,
        "FileSystemTags": Array [
          Object {
            "Key": "Name",
            "Value": "BLEA-Test-efs",
          },
        ],
        "KmsKeyId": Object {
          "Fn::ImportValue": "HelperStack:ExportsOutputFnGetAttTestAppKey723CB44AArn0AAA5B71",
        },
        "PerformanceMode": "generalPurpose",
        "ThroughputMode": "bursting",
      },
      "Type": "AWS::EFS::FileSystem",
      "UpdateReplacePolicy": "Retain",
    },
    "EfsEfsFileSystemEfsMountTarget11C7BB783": Object {
      "Properties": Object {
        "FileSystemId": Object {
          "Ref": "EfsEfsFileSystemD6411CDE",
        },
        "SecurityGroups": Array [
          Object {
            "Fn::GetAtt": Array [
              "EfsEfsSecurityGroup6F40EA3B",
              "GroupId",
            ],
          },
        ],
        "SubnetId": Object {
          "Fn::ImportValue": "HelperStack:ExportsOutputRefTestVpcProtectedSubnet1Subnet1C391C12B881ABCF",
        },
      },
      "Type": "AWS::EFS::MountTarget",
    },
    "EfsEfsFileSystemEfsMountTarget22017CB56": Object {
      "Properties": Object {
        "FileSystemId": Object {
          "Ref": "EfsEfsFileSystemD6411CDE",
        },
        "SecurityGroups": Array [
          Object {
            "Fn::GetAtt": Array [
              "EfsEfsSecurityGroup6F40EA3B",
              "GroupId",
            ],
          },
        ],
        "SubnetId": Object {
          "Fn::ImportValue": "HelperStack:ExportsOutputRefTestVpcProtectedSubnet2Subnet852645CB89E6FA03",
        },
      },
      "Type": "AWS::EFS::MountTarget",
    },
    "EfsEfsSecurityGroup6F40EA3B": Object {
      "Properties": Object {
        "GroupDescription": "EFS Security Group",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": Object {
          "Fn::ImportValue": "HelperStack:ExportsOutputRefTestVpcE77CE67811D13B85",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
