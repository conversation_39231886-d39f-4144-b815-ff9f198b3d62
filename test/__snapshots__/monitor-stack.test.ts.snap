// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BLEA-Test Monitor Stack Basic Tests Should match snapshot When Monitor Stack is created 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BLEATestDashboard07A0D9A4": Object {
      "Properties": Object {
        "DashboardBody": Object {
          "Fn::Join": Array [
            "",
            Array [
              "{\\"widgets\\":[{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":0,\\"properties\\":{\\"markdown\\":\\"# Requests\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":1,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"CloudFront Requests\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/CloudFront\\",\\"Requests\\",\\"DistributionId\\",\\"E1234567890123\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":1,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ALB Requests\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"RequestCount\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"NewConnectionCount\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"RejectedConnectionCount\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":12,\\"y\\":1,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Target Group Requests\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"HTTPCode_Target_2XX_Count\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":18,\\"y\\":1,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Aurora Connections\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/RDS\\",\\"DatabaseConnections\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"Writer: \${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/RDS\\",\\"DatabaseConnections\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"READER\\",{\\"label\\":\\"Reader: \${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":7,\\"properties\\":{\\"markdown\\":\\"# Response Time\\"}},{\\"type\\":\\"metric\\",\\"width\\":8,\\"height\\":6,\\"x\\":0,\\"y\\":8,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Target Group Response Time\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"TargetResponseTime\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":8,\\"height\\":6,\\"x\\":8,\\"y\\":8,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Aurora Operation Lantency (Writer)\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/RDS\\",\\"InsertLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"SelectLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"UpdateLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"CommitLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"DDLLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"DeleteLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"DMLLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"ReadLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}],[\\"AWS/RDS\\",\\"WriteLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":8,\\"height\\":6,\\"x\\":16,\\"y\\":8,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Aurora Operation Lantency (Reader)\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/RDS\\",\\"SelectLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"READER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"ReadLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"READER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}],[\\"AWS/RDS\\",\\"WriteLatency\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"READER\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":14,\\"properties\\":{\\"markdown\\":\\"# Errors\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":15,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"CloudFront Error Rates\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"AWS/CloudFront\\",\\"5xxErrorRate\\",\\"DistributionId\\",\\"E1234567890123\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60}],[\\"AWS/CloudFront\\",\\"4xxErrorRate\\",\\"DistributionId\\",\\"E1234567890123\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":15,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ALB Errors\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"ClientTLSNegotiationErrorCount\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"HTTPCode_ELB_5XX_Count\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"HTTPCode_ELB_4XX_Count\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":12,\\"y\\":15,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Target Group Errors\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"HTTPCode_Target_5XX_Count\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"HTTPCode_Target_4XX_Count\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"TargetConnectionErrorCount\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"TargetTLSNegotiationErrorCount\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":18,\\"y\\":15,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Aurora CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/RDS\\",\\"CPUUtilization\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"Writer: \${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"CPUUtilization\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"READER\\",{\\"label\\":\\"Reader: \${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":21,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Aurora Free Memory\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/RDS\\",\\"FreeableMemory\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"Writer: \${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"FreeableMemory\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"READER\\",{\\"label\\":\\"Reader: \${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":21,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Aurora Free Local Storage\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/RDS\\",\\"FreeLocalStorage\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"WRITER\\",{\\"label\\":\\"Writer: \${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"AWS/RDS\\",\\"FreeLocalStorage\\",\\"DBClusterIdentifier\\",\\"test-db-cluster\\",\\"Role\\",\\"READER\\",{\\"label\\":\\"Reader: \${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":27,\\"properties\\":{\\"markdown\\":\\"# Resources\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":28,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"CPUUtilization\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":28,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Memory Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"MemoryUtilization\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":12,\\"y\\":28,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Desired Task Count\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"DesiredTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":18,\\"y\\":28,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Task Count\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":12,\\"height\\":6,\\"x\\":0,\\"y\\":34,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Auto Scaling with Requests per tasks\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"RequestCountPerTarget\\",\\"LoadBalancer\\",\\"mock-alb-fullname\\",\\"TargetGroup\\",\\"mock-target-group\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}]],\\"annotations\\":{\\"horizontal\\":[{\\"value\\":50,\\"label\\":\\"Threshold: Requests per tasks\\",\\"color\\":\\"#aec7e8\\",\\"fill\\":\\"below\\",\\"yAxis\\":\\"left\\"}]},\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":12,\\"height\\":6,\\"x\\":12,\\"y\\":34,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Auto Scaling with CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"CPUUtilization\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}]],\\"annotations\\":{\\"horizontal\\":[{\\"value\\":70,\\"label\\":\\"Threshold: CPU Utilization\\",\\"color\\":\\"#aec7e8\\",\\"fill\\":\\"below\\",\\"yAxis\\":\\"left\\"}]},\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":40,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Alarm for UnHealthy Host in Target Group\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"annotations\\":{\\"alarms\\":[\\"",
              Object {
                "Fn::ImportValue": "MockStack:ExportsOutputFnGetAttMockAlarm1667158D8Arn791C0A12",
              },
              "\\"]},\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":46,\\"properties\\":{\\"markdown\\":\\"# Resources\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":47,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"CPUUtilization\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-internal-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":47,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Memory Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"MemoryUtilization\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-internal-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":12,\\"y\\":47,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Desired Task Count\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"DesiredTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-internal-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":18,\\"y\\":47,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Task Count\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-internal-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-internal-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":12,\\"height\\":6,\\"x\\":0,\\"y\\":53,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Auto Scaling with CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"CPUUtilization\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-alb-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-internal-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"test-cluster\\",\\"ServiceName\\",\\"test-internal-service-1\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}]],\\"annotations\\":{\\"horizontal\\":[{\\"value\\":70,\\"label\\":\\"Threshold: CPU Utilization\\",\\"color\\":\\"#aec7e8\\",\\"fill\\":\\"below\\",\\"yAxis\\":\\"left\\"}]},\\"yAxis\\":{}}}]}",
            ],
          ],
        },
        "DashboardName": "BLEA-Test-ECSApp",
      },
      "Type": "AWS::CloudWatch::Dashboard",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
