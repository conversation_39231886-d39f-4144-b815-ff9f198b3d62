// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`The KMS stack Should Match Expected Snapshot When KMS Stack Is Created With Basic Configuration 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "Key961B73FD": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "Description": "Custom KMS key",
        "EnableKeyRotation": true,
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::123456789012:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PendingWindowInDays": 7,
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Retain",
    },
    "KeyAlias910D852D": Object {
      "Properties": Object {
        "AliasName": "alias/TestKMSStackSnapshot-for-app",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "Key961B73FD",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
