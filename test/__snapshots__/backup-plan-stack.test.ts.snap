// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`The backup plan stack Should Match Expected Snapshot When Backup Plan Stack Is Created With Basic Configuration 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "TestBackupPlanStackSnapshot4E55D74F": Object {
      "Properties": Object {
        "BackupPlan": Object {
          "BackupPlanName": "TestBackupPlanStackSnapshot",
          "BackupPlanRule": Array [
            Object {
              "Lifecycle": Object {
                "DeleteAfterDays": 30,
              },
              "RuleName": "TestBackupPlanStackSnapshotRule0",
              "ScheduleExpression": "cron(0 2 * * ? *)",
              "TargetBackupVault": Object {
                "Fn::ImportValue": "HelperStack:ExportsOutputFnGetAttPrimaryVault9BBEBB0DBackupVaultName27957152",
              },
            },
          ],
        },
      },
      "Type": "AWS::Backup::BackupPlan",
    },
    "TestBackupPlanStackSnapshotSelectionDBD5BA74": Object {
      "Properties": Object {
        "BackupPlanId": Object {
          "Fn::GetAtt": Array [
            "TestBackupPlanStackSnapshot4E55D74F",
            "BackupPlanId",
          ],
        },
        "BackupSelection": Object {
          "IamRoleArn": Object {
            "Fn::GetAtt": Array [
              "TestBackupPlanStackSnapshotSelectionRole31149056",
              "Arn",
            ],
          },
          "ListOfTags": Array [
            Object {
              "ConditionKey": "AWSBackup",
              "ConditionType": "STRINGEQUALS",
              "ConditionValue": "True",
            },
          ],
          "SelectionName": "Selection",
        },
      },
      "Type": "AWS::Backup::BackupSelection",
    },
    "TestBackupPlanStackSnapshotSelectionRole31149056": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "backup.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
