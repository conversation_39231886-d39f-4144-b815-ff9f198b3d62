// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`The protect resource stack Should Match Expected Snapshot When Protect Resource Stack Is Created With Basic Configuration 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomResourceCustomResourceProviderframeworkonEvent2B401E58": Object {
      "DependsOn": Array [
        "CustomResourceCustomResourceProviderframeworkonEventServiceRoleDefaultPolicyA6FD9B02",
        "CustomResourceCustomResourceProviderframeworkonEventServiceRoleF40ACD22",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "bdc104ed9cab1b5b6421713c8155f0b753380595356f710400609664d3635eca.zip",
        },
        "Description": "AWS CDK resource provider framework - onEvent (TestProtectResourceStackSnapshot/CustomResource/CustomResourceProvider)",
        "Environment": Object {
          "Variables": Object {
            "USER_ON_EVENT_FUNCTION_ARN": Object {
              "Fn::GetAtt": Array [
                "CustomResourceFunction3BEC9AAF",
                "Arn",
              ],
            },
          },
        },
        "Handler": "framework.onEvent",
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomResourceCustomResourceProviderframeworkonEventServiceRoleF40ACD22",
            "Arn",
          ],
        },
        "Runtime": "nodejs22.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomResourceCustomResourceProviderframeworkonEventLogRetention3495D88E": Object {
      "Properties": Object {
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/lambda/",
              Object {
                "Ref": "CustomResourceCustomResourceProviderframeworkonEvent2B401E58",
              },
            ],
          ],
        },
        "RetentionInDays": 1,
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A",
            "Arn",
          ],
        },
      },
      "Type": "Custom::LogRetention",
    },
    "CustomResourceCustomResourceProviderframeworkonEventServiceRoleDefaultPolicyA6FD9B02": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "lambda:InvokeFunction",
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "CustomResourceFunction3BEC9AAF",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "CustomResourceFunction3BEC9AAF",
                          "Arn",
                        ],
                      },
                      ":*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "lambda:GetFunction",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "CustomResourceFunction3BEC9AAF",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "CustomResourceCustomResourceProviderframeworkonEventServiceRoleDefaultPolicyA6FD9B02",
        "Roles": Array [
          Object {
            "Ref": "CustomResourceCustomResourceProviderframeworkonEventServiceRoleF40ACD22",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "CustomResourceCustomResourceProviderframeworkonEventServiceRoleF40ACD22": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "CustomResourceCustomResourceRoleFBDE5DE4": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "CustomResourceE051727D": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomResourceCustomResourceProviderframeworkonEvent2B401E58",
            "Arn",
          ],
        },
        "StackDisableProtection": "DatabaseStack",
        "StackEnableProtection": "VpcStack,BackupStack",
      },
      "Type": "AWS::CloudFormation::CustomResource",
      "UpdateReplacePolicy": "Delete",
    },
    "CustomResourceFunction3BEC9AAF": Object {
      "DependsOn": Array [
        "CustomResourceCustomResourceRoleFBDE5DE4",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "97eecf9f69dc8925796a9592f0fd0b3fbf09506b59654087a21e8668e3d0f36f.zip",
        },
        "Handler": "stackPolicy.on_event",
        "MemorySize": 256,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomResourceCustomResourceRoleFBDE5DE4",
            "Arn",
          ],
        },
        "Runtime": "python3.12",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A": Object {
      "DependsOn": Array [
        "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB",
        "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "2819175352ad1ce0dae768e83fc328fb70fb5f10b4a8ff0ccbcb791f02b0716d.zip",
        },
        "Handler": "index.handler",
        "Role": Object {
          "Fn::GetAtt": Array [
            "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB",
            "Arn",
          ],
        },
        "Runtime": "nodejs22.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "logs:PutRetentionPolicy",
                "logs:DeleteRetentionPolicy",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB",
        "Roles": Array [
          Object {
            "Ref": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "SetStackPolicy9BF995E1": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "cloudformation:SetStackPolicy",
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "SetStackPolicy9BF995E1",
        "Roles": Array [
          Object {
            "Ref": "CustomResourceCustomResourceRoleFBDE5DE4",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
