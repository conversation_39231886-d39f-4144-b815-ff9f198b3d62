// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BLEA-Test CloudFront Stack Should snapshot matching When CloudFront Stack is created 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BLEATestCloudFrontClosedBucket55808D14": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
          },
          "LogFilePrefix": "closedBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCloudFrontClosedBucketAutoDeleteObjectsCustomResource8B850CEA": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "BLEATestCloudFrontClosedBucketPolicy5B74B3E9",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "BLEATestCloudFrontClosedBucket55808D14",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCloudFrontClosedBucketPolicy5B74B3E9": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "BLEATestCloudFrontClosedBucket55808D14",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestCloudFrontClosedBucket55808D14",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestCloudFrontClosedBucket55808D14",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestCloudFrontClosedBucket55808D14",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestCloudFrontClosedBucket55808D14",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "BLEATestCloudFrontCloudFrontLogBucketAutoDeleteObjectsCustomResource6EE42189": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "BLEATestCloudFrontCloudFrontLogBucketPolicyAF207401",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "BLEATestCloudFrontCloudFrontLogBucketD5BE86E9",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCloudFrontCloudFrontLogBucketD5BE86E9": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "OwnershipControls": Object {
          "Rules": Array [
            Object {
              "ObjectOwnership": "ObjectWriter",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCloudFrontCloudFrontLogBucketPolicyAF207401": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "BLEATestCloudFrontCloudFrontLogBucketD5BE86E9",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestCloudFrontCloudFrontLogBucketD5BE86E9",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestCloudFrontCloudFrontLogBucketD5BE86E9",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestCloudFrontCloudFrontLogBucketD5BE86E9",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestCloudFrontCloudFrontLogBucketD5BE86E9",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "BLEATestCloudFrontDistribution89EE3957": Object {
      "Properties": Object {
        "DistributionConfig": Object {
          "CacheBehaviors": Array [
            Object {
              "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6",
              "Compress": true,
              "PathPattern": "/static/*",
              "TargetOriginId": "BLEATestCloudfrontBLEATestCloudFrontDistributionOrigin245A58BC7",
              "ViewerProtocolPolicy": "redirect-to-https",
            },
          ],
          "CustomErrorResponses": Array [
            Object {
              "ErrorCachingMinTTL": 20,
              "ErrorCode": 403,
              "ResponseCode": 403,
              "ResponsePagePath": "/static/sorry.html",
            },
          ],
          "DefaultCacheBehavior": Object {
            "AllowedMethods": Array [
              "GET",
              "HEAD",
              "OPTIONS",
              "PUT",
              "PATCH",
              "POST",
              "DELETE",
            ],
            "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad",
            "Compress": true,
            "OriginRequestPolicyId": "216adef6-5c7f-47e4-b989-5492eafa07d3",
            "TargetOriginId": "BLEATestCloudfrontBLEATestCloudFrontDistributionOrigin1FD33C909",
            "ViewerProtocolPolicy": "redirect-to-https",
          },
          "DefaultRootObject": "/",
          "Enabled": true,
          "HttpVersion": "http2",
          "IPV6Enabled": true,
          "Logging": Object {
            "Bucket": Object {
              "Fn::GetAtt": Array [
                "BLEATestCloudFrontCloudFrontLogBucketD5BE86E9",
                "RegionalDomainName",
              ],
            },
            "IncludeCookies": true,
            "Prefix": "CloudFrontAccessLogs/",
          },
          "Origins": Array [
            Object {
              "CustomOriginConfig": Object {
                "OriginProtocolPolicy": "http-only",
                "OriginSSLProtocols": Array [
                  "TLSv1.2",
                ],
              },
              "DomainName": Object {
                "Fn::ImportValue": "MockStack:ExportsOutputFnGetAttMockAlb4391079BDNSName4538F4D4",
              },
              "Id": "BLEATestCloudfrontBLEATestCloudFrontDistributionOrigin1FD33C909",
              "OriginCustomHeaders": Array [
                Object {
                  "HeaderName": "x-pre-shared-key",
                  "HeaderValue": "test-pre-shared-key",
                },
              ],
            },
            Object {
              "DomainName": Object {
                "Fn::GetAtt": Array [
                  "BLEATestCloudFrontWebBucketD01F2F23",
                  "RegionalDomainName",
                ],
              },
              "Id": "BLEATestCloudfrontBLEATestCloudFrontDistributionOrigin245A58BC7",
              "OriginAccessControlId": Object {
                "Fn::GetAtt": Array [
                  "BLEATestCloudFrontDistributionOrigin2S3OriginAccessControl479B3499",
                  "Id",
                ],
              },
              "S3OriginConfig": Object {
                "OriginAccessIdentity": "",
              },
            },
          ],
          "WebACLId": Object {
            "Fn::ImportValue": "MockStack:ExportsOutputFnGetAttMockWebAclArnDC4724B3",
          },
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::CloudFront::Distribution",
    },
    "BLEATestCloudFrontDistributionOrigin2S3OriginAccessControl479B3499": Object {
      "Properties": Object {
        "OriginAccessControlConfig": Object {
          "Name": "BLEATestCloudfrontBLEATestClOrigin2S3OriginAccessControlD7815E4B",
          "OriginAccessControlOriginType": "s3",
          "SigningBehavior": "always",
          "SigningProtocol": "sigv4",
        },
      },
      "Type": "AWS::CloudFront::OriginAccessControl",
    },
    "BLEATestCloudFrontWebBucketAutoDeleteObjectsCustomResourceBF31DBE4": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "BLEATestCloudFrontWebBucketPolicy3B50F3DF",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "BLEATestCloudFrontWebBucketD01F2F23",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCloudFrontWebBucketD01F2F23": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
          },
          "LogFilePrefix": "webContentBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCloudFrontWebBucketPolicy3B50F3DF": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "BLEATestCloudFrontWebBucketD01F2F23",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestCloudFrontWebBucketD01F2F23",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestCloudFrontWebBucketD01F2F23",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestCloudFrontWebBucketD01F2F23",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestCloudFrontWebBucketD01F2F23",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "s3:GetObject",
              "Condition": Object {
                "StringEquals": Object {
                  "AWS:SourceArn": Object {
                    "Fn::Join": Array [
                      "",
                      Array [
                        "arn:",
                        Object {
                          "Ref": "AWS::Partition",
                        },
                        ":cloudfront::",
                        Object {
                          "Ref": "AWS::AccountId",
                        },
                        ":distribution/",
                        Object {
                          "Ref": "BLEATestCloudFrontDistribution89EE3957",
                        },
                      ],
                    ],
                  },
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Service": "cloudfront.amazonaws.com",
              },
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "BLEATestCloudFrontWebBucketD01F2F23",
                        "Arn",
                      ],
                    },
                    "/*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "BLEATestCloudFronts3AccessLogsBucketAutoDeleteObjectsCustomResource19D6D51C": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "BLEATestCloudFronts3AccessLogsBucketPolicyE59D8BBA",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCloudFronts3AccessLogsBucketB00C7CBA": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "LogDeliveryWrite",
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "OwnershipControls": Object {
          "Rules": Array [
            Object {
              "ObjectOwnership": "ObjectWriter",
            },
          ],
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "BLEATestCloudFronts3AccessLogsBucketPolicyE59D8BBA": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "BLEATestCloudFronts3AccessLogsBucketB00C7CBA",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs22.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
