// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BLEA-Test InfraResourcesPipeline Stack should match snapshot when stack is created 1`] = `
Object {
  "Outputs": Object {
    "SourceBucketName": Object {
      "Value": Object {
        "Ref": "SourceBucketDDD2130A",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db8347ECC3691": Object {
      "DependsOn": Array [
        "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36",
        "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
      ],
      "Properties": Object {
        "Code": Object {
          "ZipFile": "import boto3  # type: ignore
import json
import logging
import urllib.request

s3 = boto3.client(\\"s3\\")

EVENTBRIDGE_CONFIGURATION = 'EventBridgeConfiguration'
CONFIGURATION_TYPES = [\\"TopicConfigurations\\", \\"QueueConfigurations\\", \\"LambdaFunctionConfigurations\\"]

def handler(event: dict, context):
  response_status = \\"SUCCESS\\"
  error_message = \\"\\"
  try:
    props = event[\\"ResourceProperties\\"]
    notification_configuration = props[\\"NotificationConfiguration\\"]
    managed = props.get('Managed', 'true').lower() == 'true'
    skipDestinationValidation = props.get('SkipDestinationValidation', 'false').lower() == 'true'
    stack_id = event['StackId']
    old = event.get(\\"OldResourceProperties\\", {}).get(\\"NotificationConfiguration\\", {})
    if managed:
      config = handle_managed(event[\\"RequestType\\"], notification_configuration)
    else:
      config = handle_unmanaged(props[\\"BucketName\\"], stack_id, event[\\"RequestType\\"], notification_configuration, old)
    s3.put_bucket_notification_configuration(Bucket=props[\\"BucketName\\"], NotificationConfiguration=config, SkipDestinationValidation=skipDestinationValidation)
  except Exception as e:
    logging.exception(\\"Failed to put bucket notification configuration\\")
    response_status = \\"FAILED\\"
    error_message = f\\"Error: {str(e)}. \\"
  finally:
    submit_response(event, context, response_status, error_message)

def handle_managed(request_type, notification_configuration):
  if request_type == 'Delete':
    return {}
  return notification_configuration

def handle_unmanaged(bucket, stack_id, request_type, notification_configuration, old):
  def get_id(n):
    n['Id'] = ''
    sorted_notifications = sort_filter_rules(n)
    strToHash=json.dumps(sorted_notifications, sort_keys=True).replace('\\"Name\\": \\"prefix\\"', '\\"Name\\": \\"Prefix\\"').replace('\\"Name\\": \\"suffix\\"', '\\"Name\\": \\"Suffix\\"')
    return f\\"{stack_id}-{hash(strToHash)}\\"
  def with_id(n):
    n['Id'] = get_id(n)
    return n

  external_notifications = {}
  existing_notifications = s3.get_bucket_notification_configuration(Bucket=bucket)
  for t in CONFIGURATION_TYPES:
    if request_type == 'Update':
        old_incoming_ids = [get_id(n) for n in old.get(t, [])]
        external_notifications[t] = [n for n in existing_notifications.get(t, []) if not get_id(n) in old_incoming_ids]      
    elif request_type == 'Delete':
        external_notifications[t] = [n for n in existing_notifications.get(t, []) if not n['Id'].startswith(f\\"{stack_id}-\\")]
    elif request_type == 'Create':
        external_notifications[t] = [n for n in existing_notifications.get(t, [])]
  if EVENTBRIDGE_CONFIGURATION in existing_notifications:
    external_notifications[EVENTBRIDGE_CONFIGURATION] = existing_notifications[EVENTBRIDGE_CONFIGURATION]

  if request_type == 'Delete':
    return external_notifications

  notifications = {}
  for t in CONFIGURATION_TYPES:
    external = external_notifications.get(t, [])
    incoming = [with_id(n) for n in notification_configuration.get(t, [])]
    notifications[t] = external + incoming

  if EVENTBRIDGE_CONFIGURATION in notification_configuration:
    notifications[EVENTBRIDGE_CONFIGURATION] = notification_configuration[EVENTBRIDGE_CONFIGURATION]
  elif EVENTBRIDGE_CONFIGURATION in external_notifications:
    notifications[EVENTBRIDGE_CONFIGURATION] = external_notifications[EVENTBRIDGE_CONFIGURATION]

  return notifications

def submit_response(event: dict, context, response_status: str, error_message: str):
  response_body = json.dumps(
    {
      \\"Status\\": response_status,
      \\"Reason\\": f\\"{error_message}See the details in CloudWatch Log Stream: {context.log_stream_name}\\",
      \\"PhysicalResourceId\\": event.get(\\"PhysicalResourceId\\") or event[\\"LogicalResourceId\\"],
      \\"StackId\\": event[\\"StackId\\"],
      \\"RequestId\\": event[\\"RequestId\\"],
      \\"LogicalResourceId\\": event[\\"LogicalResourceId\\"],
      \\"NoEcho\\": False,
    }
  ).encode(\\"utf-8\\")
  headers = {\\"content-type\\": \\"\\", \\"content-length\\": str(len(response_body))}
  try:
    req = urllib.request.Request(url=event[\\"ResponseURL\\"], headers=headers, data=response_body, method=\\"PUT\\")
    with urllib.request.urlopen(req) as response:
      print(response.read().decode(\\"utf-8\\"))
    print(\\"Status code: \\" + response.reason)
  except Exception as e:
      print(\\"send(..) failed executing request.urlopen(..): \\" + str(e))

def sort_filter_rules(json_obj):
  if not isinstance(json_obj, dict):
      return json_obj
  for key, value in json_obj.items():
      if isinstance(value, dict):
          json_obj[key] = sort_filter_rules(value)
      elif isinstance(value, list):
          json_obj[key] = [sort_filter_rules(item) for item in value]
  if \\"Filter\\" in json_obj and \\"Key\\" in json_obj[\\"Filter\\"] and \\"FilterRules\\" in json_obj[\\"Filter\\"][\\"Key\\"]:
      filter_rules = json_obj[\\"Filter\\"][\\"Key\\"][\\"FilterRules\\"]
      sorted_filter_rules = sorted(filter_rules, key=lambda x: x[\\"Name\\"])
      json_obj[\\"Filter\\"][\\"Key\\"][\\"FilterRules\\"] = sorted_filter_rules
  return json_obj",
        },
        "Description": "AWS CloudFormation handler for \\"Custom::S3BucketNotifications\\" resources (@aws-cdk/aws-s3)",
        "Handler": "index.handler",
        "Role": Object {
          "Fn::GetAtt": Array [
            "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
            "Arn",
          ],
        },
        "Runtime": "python3.11",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "Timeout": 300,
      },
      "Type": "AWS::Lambda::Function",
    },
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:PutBucketNotification",
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36",
        "Roles": Array [
          Object {
            "Ref": "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "SourceBucketDDD2130A",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs22.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "Log95422804": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "KmsKeyId": "arn:aws:kms:ap-northeast-1:************:key/mock-key-id",
        "RetentionInDays": 30,
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Retain",
    },
    "PipelineTriggerEventRule0AE0646E": Object {
      "Properties": Object {
        "EventPattern": Object {
          "account": Array [
            "************",
          ],
          "detail": Object {
            "bucket": Object {
              "name": Array [
                Object {
                  "Ref": "SourceBucketDDD2130A",
                },
              ],
            },
            "object": Object {
              "key": Array [
                "image.zip",
              ],
            },
          },
          "detail-type": Array [
            "Object Created",
          ],
          "source": Array [
            "aws.s3",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "arn:",
                  Object {
                    "Ref": "AWS::Partition",
                  },
                  ":codepipeline:ap-northeast-1:************:",
                  Object {
                    "Ref": "ProjectC78D97AD",
                  },
                ],
              ],
            },
            "Id": "Target0",
            "RoleArn": Object {
              "Fn::GetAtt": Array [
                "ProjectEventsRole256975EC",
                "Arn",
              ],
            },
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "ProjectArtifactsBucketA004C8A7": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "KMSMasterKeyID": Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                    "Arn",
                  ],
                },
                "SSEAlgorithm": "aws:kms",
              },
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ProjectArtifactsBucketEncryptionKey9C4C3F23": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Delete",
    },
    "ProjectArtifactsBucketEncryptionKeyAlias9B199404": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AliasName": "alias/codepipeline-bleatestinfraresourcespipelineproject35862231",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "ProjectArtifactsBucketEncryptionKey9C4C3F23",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
      "UpdateReplacePolicy": "Delete",
    },
    "ProjectArtifactsBucketPolicyB28439E8": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ProjectArtifactsBucketA004C8A7",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketA004C8A7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ProjectArtifactsBucketA004C8A7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ProjectBuildCodeBuildCodePipelineActionRole39C24142": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "ProjectRole4CCB274E",
                    "Arn",
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ProjectBuildCodeBuildCodePipelineActionRoleDefaultPolicyE7B213A3": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "codebuild:BatchGetBuilds",
                "codebuild:StartBuild",
                "codebuild:StopBuild",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "buildProjectB3CE3E56",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ProjectBuildCodeBuildCodePipelineActionRoleDefaultPolicyE7B213A3",
        "Roles": Array [
          Object {
            "Ref": "ProjectBuildCodeBuildCodePipelineActionRole39C24142",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ProjectC78D97AD": Object {
      "DependsOn": Array [
        "ProjectRoleDefaultPolicy7F29461B",
        "ProjectRole4CCB274E",
      ],
      "Properties": Object {
        "ArtifactStore": Object {
          "EncryptionKey": Object {
            "Id": Object {
              "Fn::GetAtt": Array [
                "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                "Arn",
              ],
            },
            "Type": "KMS",
          },
          "Location": Object {
            "Ref": "ProjectArtifactsBucketA004C8A7",
          },
          "Type": "S3",
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "ProjectRole4CCB274E",
            "Arn",
          ],
        },
        "Stages": Array [
          Object {
            "Actions": Array [
              Object {
                "ActionTypeId": Object {
                  "Category": "Source",
                  "Owner": "AWS",
                  "Provider": "S3",
                  "Version": "1",
                },
                "Configuration": Object {
                  "PollForSourceChanges": false,
                  "S3Bucket": Object {
                    "Ref": "SourceBucketDDD2130A",
                  },
                  "S3ObjectKey": "image.zip",
                },
                "Name": "SourceBucket",
                "OutputArtifacts": Array [
                  Object {
                    "Name": "SourceArtifact",
                  },
                ],
                "RoleArn": Object {
                  "Fn::GetAtt": Array [
                    "ProjectSourceSourceBucketCodePipelineActionRole30AADFBE",
                    "Arn",
                  ],
                },
                "RunOrder": 1,
              },
            ],
            "Name": "Source",
          },
          Object {
            "Actions": Array [
              Object {
                "ActionTypeId": Object {
                  "Category": "Build",
                  "Owner": "AWS",
                  "Provider": "CodeBuild",
                  "Version": "1",
                },
                "Configuration": Object {
                  "ProjectName": Object {
                    "Ref": "buildProjectB3CE3E56",
                  },
                },
                "InputArtifacts": Array [
                  Object {
                    "Name": "SourceArtifact",
                  },
                ],
                "Name": "CodeBuild",
                "OutputArtifacts": Array [
                  Object {
                    "Name": "Artifact_Build_CodeBuild",
                  },
                ],
                "RoleArn": Object {
                  "Fn::GetAtt": Array [
                    "ProjectBuildCodeBuildCodePipelineActionRole39C24142",
                    "Arn",
                  ],
                },
                "RunOrder": 1,
              },
            ],
            "Name": "Build",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::CodePipeline::Pipeline",
    },
    "ProjectEventsRole256975EC": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "events.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ProjectEventsRoleDefaultPolicy00ED7125": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "codepipeline:StartPipelineExecution",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":codepipeline:ap-northeast-1:************:",
                    Object {
                      "Ref": "ProjectC78D97AD",
                    },
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ProjectEventsRoleDefaultPolicy00ED7125",
        "Roles": Array [
          Object {
            "Ref": "ProjectEventsRole256975EC",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ProjectRole4CCB274E": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "codepipeline.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ProjectRoleDefaultPolicy7F29461B": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketA004C8A7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ProjectArtifactsBucketA004C8A7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:DescribeKey",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectSourceSourceBucketCodePipelineActionRole30AADFBE",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectBuildCodeBuildCodePipelineActionRole39C24142",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ProjectRoleDefaultPolicy7F29461B",
        "Roles": Array [
          Object {
            "Ref": "ProjectRole4CCB274E",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ProjectSourceSourceBucketCodePipelineActionRole30AADFBE": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "ProjectRole4CCB274E",
                    "Arn",
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ProjectSourceSourceBucketCodePipelineActionRoleDefaultPolicyBD09B9B2": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "SourceBucketDDD2130A",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "SourceBucketDDD2130A",
                          "Arn",
                        ],
                      },
                      "/image.zip",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketA004C8A7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ProjectArtifactsBucketA004C8A7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:Decrypt",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ProjectSourceSourceBucketCodePipelineActionRoleDefaultPolicyBD09B9B2",
        "Roles": Array [
          Object {
            "Ref": "ProjectSourceSourceBucketCodePipelineActionRole30AADFBE",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "SlackChannel425D424B": Object {
      "Properties": Object {
        "ConfigurationName": "test-channel-name",
        "IamRoleArn": Object {
          "Fn::GetAtt": Array [
            "SlackChannelConfigurationRole7EE950F2",
            "Arn",
          ],
        },
        "SlackChannelId": "test-channel-id",
        "SlackWorkspaceId": "test-workspace-id",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::Chatbot::SlackChannelConfiguration",
    },
    "SlackChannelConfigurationRole7EE950F2": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "chatbot.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "SourceBucketAutoDeleteObjectsCustomResourceC68FC040": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "SourceBucketPolicy703DFBF9",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "SourceBucketDDD2130A",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "SourceBucketDDD2130A": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "SourceBucketNotifications0A6F2084": Object {
      "DependsOn": Array [
        "SourceBucketPolicy703DFBF9",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "SourceBucketDDD2130A",
        },
        "Managed": true,
        "NotificationConfiguration": Object {
          "EventBridgeConfiguration": Object {},
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "BucketNotificationsHandler050a0587b7544547bf325f094a3db8347ECC3691",
            "Arn",
          ],
        },
        "SkipDestinationValidation": false,
      },
      "Type": "Custom::S3BucketNotifications",
    },
    "SourceBucketPolicy703DFBF9": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "SourceBucketDDD2130A",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "SourceBucketDDD2130A",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "SourceBucketDDD2130A",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "buildProjectB3CE3E56": Object {
      "Properties": Object {
        "Artifacts": Object {
          "Type": "CODEPIPELINE",
        },
        "Cache": Object {
          "Type": "NO_CACHE",
        },
        "EncryptionKey": Object {
          "Fn::GetAtt": Array [
            "ProjectArtifactsBucketEncryptionKey9C4C3F23",
            "Arn",
          ],
        },
        "Environment": Object {
          "ComputeType": "BUILD_GENERAL1_SMALL",
          "Image": "aws/codebuild/standard:7.0",
          "ImagePullCredentialsType": "CODEBUILD",
          "PrivilegedMode": false,
          "Type": "LINUX_CONTAINER",
        },
        "LogsConfig": Object {
          "CloudWatchLogs": Object {
            "GroupName": Object {
              "Ref": "Log95422804",
            },
            "Status": "ENABLED",
          },
        },
        "Name": "BLEA-Test-InfraResourcesPipeline-BuildProject",
        "ServiceRole": Object {
          "Fn::GetAtt": Array [
            "buildProjectRoleAF642118",
            "Arn",
          ],
        },
        "Source": Object {
          "BuildSpec": "{
  \\"version\\": \\"0.2\\",
  \\"cache\\": {
    \\"paths\\": [
      \\"node_modules/**/*\\",
      \\"/var/lib/docker\\",
      \\"/tmp/.buildx-cache\\"
    ]
  },
  \\"phases\\": {
    \\"install\\": {
      \\"commands\\": [
        \\"npm ci || npm install\\"
      ]
    },
    \\"build\\": {
      \\"commands\\": [
        \\"npx cdk deploy --require-approval never --all -c environment=dev\\"
      ]
    }
  }
}",
          "Type": "CODEPIPELINE",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "TimeoutInMinutes": 180,
      },
      "Type": "AWS::CodeBuild::Project",
    },
    "buildProjectNotifyOnBuildSucceeded0CCFF51E": Object {
      "Properties": Object {
        "DetailType": "FULL",
        "EventTypeIds": Array [
          "codebuild-project-build-state-succeeded",
        ],
        "Name": "InfraResourcesPipelinebuildProjectNotifyOnBuildSucceeded96ECB4E9",
        "Resource": Object {
          "Fn::GetAtt": Array [
            "buildProjectB3CE3E56",
            "Arn",
          ],
        },
        "Tags": Object {
          "Environment": "dev",
        },
        "Targets": Array [
          Object {
            "TargetAddress": Object {
              "Ref": "SlackChannel425D424B",
            },
            "TargetType": "AWSChatbotSlack",
          },
        ],
      },
      "Type": "AWS::CodeStarNotifications::NotificationRule",
    },
    "buildProjectNotifyOnBuildfailed2BA6AA1C": Object {
      "Properties": Object {
        "DetailType": "FULL",
        "EventTypeIds": Array [
          "codebuild-project-build-state-failed",
        ],
        "Name": "estInfraResourcesPipelinebuildProjectNotifyOnBuildfailedF98EAE9F",
        "Resource": Object {
          "Fn::GetAtt": Array [
            "buildProjectB3CE3E56",
            "Arn",
          ],
        },
        "Tags": Object {
          "Environment": "dev",
        },
        "Targets": Array [
          Object {
            "TargetAddress": Object {
              "Ref": "SlackChannel425D424B",
            },
            "TargetType": "AWSChatbotSlack",
          },
        ],
      },
      "Type": "AWS::CodeStarNotifications::NotificationRule",
    },
    "buildProjectRoleAF642118": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "codebuild.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "buildProjectRoleDefaultPolicy1C8C8518": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "Log95422804",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":logs:ap-northeast-1:************:log-group:/aws/codebuild/",
                      Object {
                        "Ref": "buildProjectB3CE3E56",
                      },
                    ],
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":logs:ap-northeast-1:************:log-group:/aws/codebuild/",
                      Object {
                        "Ref": "buildProjectB3CE3E56",
                      },
                      ":*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "codebuild:CreateReportGroup",
                "codebuild:CreateReport",
                "codebuild:UpdateReport",
                "codebuild:BatchPutTestCases",
                "codebuild:BatchPutCodeCoverages",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":codebuild:ap-northeast-1:************:report-group/",
                    Object {
                      "Ref": "buildProjectB3CE3E56",
                    },
                    "-*",
                  ],
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": "arn:aws:iam::*:role/cdk-*",
            },
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketA004C8A7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ProjectArtifactsBucketA004C8A7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:DescribeKey",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "buildProjectRoleDefaultPolicy1C8C8518",
        "Roles": Array [
          Object {
            "Ref": "buildProjectRoleAF642118",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
