// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`The SendGrid log stack Should Match Expected Snapshot When SendGrid Log Stack Is Created With Basic Configuration 1`] = `
Object {
  "Outputs": Object {
    "testSendGridLogapp1ApiEndpointE4A580EC": Object {
      "Value": Object {
        "Fn::Join": Array [
          "",
          Array [
            "https://",
            Object {
              "Ref": "testSendGridLogapp1ApiA533E012",
            },
            ".execute-api.ap-northeast-1.",
            Object {
              "Ref": "AWS::URLSuffix",
            },
            "/",
            Object {
              "Ref": "testSendGridLogapp1ApiDeploymentStageprod3442B87C",
            },
            "/",
          ],
        ],
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "testSendGridLogapp1ApiA533E012": Object {
      "Properties": Object {
        "Name": "test-app1-apigateway-sendgrid",
      },
      "Type": "AWS::ApiGateway::RestApi",
    },
    "testSendGridLogapp1ApiAccount2878BD06": Object {
      "DeletionPolicy": "Retain",
      "DependsOn": Array [
        "testSendGridLogapp1ApiA533E012",
      ],
      "Properties": Object {
        "CloudWatchRoleArn": Object {
          "Fn::GetAtt": Array [
            "testSendGridLogapp1ApiCloudWatchRole1FD77E3B",
            "Arn",
          ],
        },
      },
      "Type": "AWS::ApiGateway::Account",
      "UpdateReplacePolicy": "Retain",
    },
    "testSendGridLogapp1ApiCloudWatchRole1FD77E3B": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "apigateway.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs",
              ],
            ],
          },
        ],
        "RoleName": "test-app1-apigateway-to-cloudwatch-log-role",
      },
      "Type": "AWS::IAM::Role",
      "UpdateReplacePolicy": "Retain",
    },
    "testSendGridLogapp1ApiDeployment0AEAAFD06fc261412d732e7ee1664d706de107b2": Object {
      "DependsOn": Array [
        "testSendGridLogapp1ApiPOST5ADDF0AD",
      ],
      "Properties": Object {
        "Description": "Automatically created by the RestApi construct",
        "RestApiId": Object {
          "Ref": "testSendGridLogapp1ApiA533E012",
        },
      },
      "Type": "AWS::ApiGateway::Deployment",
    },
    "testSendGridLogapp1ApiDeploymentStageprod3442B87C": Object {
      "DependsOn": Array [
        "testSendGridLogapp1ApiAccount2878BD06",
      ],
      "Properties": Object {
        "DeploymentId": Object {
          "Ref": "testSendGridLogapp1ApiDeployment0AEAAFD06fc261412d732e7ee1664d706de107b2",
        },
        "MethodSettings": Array [
          Object {
            "DataTraceEnabled": false,
            "HttpMethod": "*",
            "MetricsEnabled": true,
            "ResourcePath": "/*",
          },
        ],
        "RestApiId": Object {
          "Ref": "testSendGridLogapp1ApiA533E012",
        },
        "StageName": "prod",
      },
      "Type": "AWS::ApiGateway::Stage",
    },
    "testSendGridLogapp1ApiPOST5ADDF0AD": Object {
      "Properties": Object {
        "AuthorizationType": "NONE",
        "HttpMethod": "POST",
        "Integration": Object {
          "Credentials": Object {
            "Fn::GetAtt": Array [
              "testSendGridLogapp1ApiRoleCAC1947E",
              "Arn",
            ],
          },
          "IntegrationHttpMethod": "POST",
          "IntegrationResponses": Array [
            Object {
              "ResponseTemplates": Object {
                "application/json": "{\\"status\\": \\"OK\\"}",
              },
              "StatusCode": "200",
            },
            Object {
              "ResponseTemplates": Object {
                "application/json": "{\\"status\\": \\"Client Error\\"}",
              },
              "StatusCode": "400",
            },
            Object {
              "ResponseTemplates": Object {
                "application/json": "{\\"status\\": \\"Server Error\\"}",
              },
              "StatusCode": "500",
            },
          ],
          "PassthroughBehavior": "WHEN_NO_TEMPLATES",
          "RequestParameters": Object {
            "integration.request.header.Content-Type": "'application/json'",
          },
          "RequestTemplates": Object {
            "application/json": "
#set($num = 0)
#set($Data = \\"\\")
#foreach($var in $input.path('$'))
#set($tmp = '$[' + $num + ']')
#set($Data = $Data + $input.json($tmp) + \\"
\\")
#set($num = $num + 1)
#end
{
\\"DeliveryStreamName\\": \\"test-app1-apigateway-sendgrid-stream\\",
\\"Record\\": {
  \\"Data\\": \\"$util.base64Encode($Data)\\"
}
}",
          },
          "Type": "AWS",
          "Uri": Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":apigateway:ap-northeast-1:firehose:action/PutRecord",
              ],
            ],
          },
        },
        "MethodResponses": Array [
          Object {
            "StatusCode": "200",
          },
          Object {
            "StatusCode": "400",
          },
          Object {
            "StatusCode": "500",
          },
        ],
        "ResourceId": Object {
          "Fn::GetAtt": Array [
            "testSendGridLogapp1ApiA533E012",
            "RootResourceId",
          ],
        },
        "RestApiId": Object {
          "Ref": "testSendGridLogapp1ApiA533E012",
        },
      },
      "Type": "AWS::ApiGateway::Method",
    },
    "testSendGridLogapp1ApiRoleCAC1947E": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "apigateway.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Description": "Role for apigateway for send logs to firehose.",
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "firehose:*",
                  "Effect": "Allow",
                  "Resource": Object {
                    "Fn::GetAtt": Array [
                      "testSendGridLogapp1FirehoseStream53E3280E",
                      "Arn",
                    ],
                  },
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "sendLog",
          },
        ],
        "RoleName": "test-app1-apigateway-to-firehose-role",
      },
      "Type": "AWS::IAM::Role",
    },
    "testSendGridLogapp1AthenaDatabase9F1D332F": Object {
      "Properties": Object {
        "CatalogId": "123456789012",
        "DatabaseInput": Object {
          "Description": "Athena database for SendGrid log",
          "Name": "test_app1_sendgrid_logs",
        },
      },
      "Type": "AWS::Glue::Database",
    },
    "testSendGridLogapp1AthenaTableF16EC9C8": Object {
      "Properties": Object {
        "CatalogId": "123456789012",
        "DatabaseName": "test_app1_sendgrid_logs",
        "TableInput": Object {
          "Description": "Athena table for SendGrid log",
          "Name": "apigateway_sendgrid",
          "Parameters": Object {
            "classification": "json",
            "compressionType": "none",
            "projection.date.format": "yyyy-MM-dd",
            "projection.date.interval": "1",
            "projection.date.interval.unit": "DAYS",
            "projection.date.range": "2024-01-01,NOW",
            "projection.date.type": "date",
            "projection.enabled": "true",
            "storage.location.template": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "s3://",
                  Object {
                    "Ref": "testSendGridLogapp1s3SendGridLogBucket91AC0381",
                  },
                  "/sendgrid/dt=\${date}/",
                ],
              ],
            },
            "typeOfData": "file",
          },
          "PartitionKeys": Array [
            Object {
              "Name": "date",
              "Type": "string",
            },
          ],
          "StorageDescriptor": Object {
            "Columns": Array [
              Object {
                "Name": "email",
                "Type": "string",
              },
              Object {
                "Name": "event",
                "Type": "string",
              },
              Object {
                "Name": "send_at",
                "Type": "bigint",
              },
              Object {
                "Name": "sg_event_id",
                "Type": "string",
              },
              Object {
                "Name": "sg_message_id",
                "Type": "string",
              },
              Object {
                "Name": "smtp_id",
                "Type": "string",
              },
              Object {
                "Name": "timestamp",
                "Type": "bigint",
              },
              Object {
                "Name": "ip",
                "Type": "string",
              },
              Object {
                "Name": "response",
                "Type": "string",
              },
              Object {
                "Name": "tls",
                "Type": "int",
              },
            ],
            "InputFormat": "org.apache.hadoop.mapred.TextInputFormat",
            "Location": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "s3://",
                  Object {
                    "Ref": "testSendGridLogapp1s3SendGridLogBucket91AC0381",
                  },
                  "/sendgrid/",
                ],
              ],
            },
            "OutputFormat": "org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat",
            "SerdeInfo": Object {
              "Parameters": Object {
                "ignore.malformed.json": "true",
              },
              "SerializationLibrary": "org.openx.data.jsonserde.JsonSerDe",
            },
          },
          "TableType": "EXTERNAL_TABLE",
        },
      },
      "Type": "AWS::Glue::Table",
    },
    "testSendGridLogapp1AthenaWorkgroup0711633A": Object {
      "Properties": Object {
        "Name": "test-app1-sendgrid-log-workgroup",
        "RecursiveDeleteOption": true,
        "State": "ENABLED",
        "WorkGroupConfiguration": Object {
          "PublishCloudWatchMetricsEnabled": false,
          "ResultConfiguration": Object {
            "OutputLocation": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "s3://",
                  Object {
                    "Ref": "testSendGridLogapp1s3SendGridLogBucket91AC0381",
                  },
                  "/athena-query-results/",
                ],
              ],
            },
          },
        },
      },
      "Type": "AWS::Athena::WorkGroup",
    },
    "testSendGridLogapp1FirehoseStream53E3280E": Object {
      "Properties": Object {
        "DeliveryStreamName": "test-app1-apigateway-sendgrid-stream",
        "DeliveryStreamType": "DirectPut",
        "S3DestinationConfiguration": Object {
          "BucketARN": Object {
            "Fn::GetAtt": Array [
              "testSendGridLogapp1s3SendGridLogBucket91AC0381",
              "Arn",
            ],
          },
          "CompressionFormat": "GZIP",
          "ErrorOutputPrefix": "kinesis-error/sendgrid/!{firehose:error-output-type}/dt=!{timestamp:yyyy'-'MM'-'dd}/",
          "Prefix": "sendgrid/dt=!{timestamp:yyyy'-'MM'-'dd}/",
          "RoleARN": Object {
            "Fn::GetAtt": Array [
              "testSendGridLogapp1testapp1FireHoseRoleE6529EB9",
              "Arn",
            ],
          },
        },
      },
      "Type": "AWS::KinesisFirehose::DeliveryStream",
    },
    "testSendGridLogapp1s3SendGridLogBucket91AC0381": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketName": "test-app1-sendgrid-log",
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Retain",
    },
    "testSendGridLogapp1s3SendGridLogBucketPolicy3205DE08": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "testSendGridLogapp1s3SendGridLogBucket91AC0381",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "testSendGridLogapp1s3SendGridLogBucket91AC0381",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "testSendGridLogapp1s3SendGridLogBucket91AC0381",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "testSendGridLogapp1testapp1FireHoseRoleDefaultPolicy04FC5759": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:Abort*",
                "s3:DeleteObject*",
                "s3:GetBucket*",
                "s3:GetObject*",
                "s3:List*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "testSendGridLogapp1s3SendGridLogBucket91AC0381",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "testSendGridLogapp1s3SendGridLogBucket91AC0381",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "testSendGridLogapp1testapp1FireHoseRoleDefaultPolicy04FC5759",
        "Roles": Array [
          Object {
            "Ref": "testSendGridLogapp1testapp1FireHoseRoleE6529EB9",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "testSendGridLogapp1testapp1FireHoseRoleE6529EB9": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "firehose.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Description": "Role for firehose",
        "RoleName": "test-app1-firehose-role",
      },
      "Type": "AWS::IAM::Role",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
