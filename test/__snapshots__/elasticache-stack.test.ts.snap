// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`The ElastiCache stack Should Match Expected Snapshot When ElastiCache Stack Is Created With Self-Designed Configuration 1`] = `
Object {
  "Outputs": Object {
    "ElastiCacheEndPoint": Object {
      "Export": Object {
        "Name": "BLEA-ElastiCacheEndPoint",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ElastiCacheBLEAElastiCache0C3B8B73",
          "ConfigurationEndPoint.Address",
        ],
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "ElastiCacheBLEAElastiCache0C3B8B73": Object {
      "Properties": Object {
        "AtRestEncryptionEnabled": true,
        "AutomaticFailoverEnabled": true,
        "CacheNodeType": "cache.m5.large",
        "CacheParameterGroupName": Object {
          "Ref": "ElastiCacheElastiCacheCustomParameterGroup5DAB0725",
        },
        "CacheSubnetGroupName": "TestElastiCacheStackSelfDesignedSnapshot-Subnetgroup",
        "Engine": "redis",
        "EngineVersion": "7.0",
        "KmsKeyId": Object {
          "Fn::ImportValue": "MockResourcesStack2:ExportsOutputRefMockKey4B46A5C551AD5A43",
        },
        "LogDeliveryConfigurations": Array [
          Object {
            "DestinationDetails": Object {
              "CloudWatchLogsDetails": Object {
                "LogGroup": Object {
                  "Ref": "ElastiCacheLoggroup6155D988",
                },
              },
            },
            "DestinationType": "cloudwatch-logs",
            "LogFormat": "json",
            "LogType": "slow-log",
          },
        ],
        "MultiAZEnabled": true,
        "NotificationTopicArn": Object {
          "Fn::ImportValue": "MockResourcesStack2:ExportsOutputRefMockTopic233D7A13E830D326",
        },
        "NumNodeGroups": 2,
        "ReplicasPerNodeGroup": 2,
        "ReplicationGroupDescription": "elasticache",
        "ReplicationGroupId": "TestElastiCacheStackSelfDesignedSnapshot-repGroup",
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "ElastiCacheSecuritygGroup6A20C698",
              "GroupId",
            ],
          },
        ],
        "TransitEncryptionEnabled": true,
      },
      "Type": "AWS::ElastiCache::ReplicationGroup",
      "UpdatePolicy": Object {
        "UseOnlineResharding": true,
      },
    },
    "ElastiCacheElastiCacheCustomParameterGroup5DAB0725": Object {
      "Properties": Object {
        "CacheParameterGroupFamily": "redis7",
        "Description": "CustomParameterGroupForRedis",
        "Properties": Object {
          "cluster-enabled": "yes",
        },
      },
      "Type": "AWS::ElastiCache::ParameterGroup",
    },
    "ElastiCacheElastiCacheShardsScalableTargetElastiCacheShardsCPUUtilizationAB38BD82": Object {
      "Properties": Object {
        "PolicyName": "TestElastiCacheStackSelfDesignedSnapshotElastiCacheElastiCacheShardsScalableTargetElastiCacheShardsCPUUtilization82F48759",
        "PolicyType": "TargetTrackingScaling",
        "ScalingTargetId": Object {
          "Ref": "ElastiCacheElastiCacheShardsScalableTargetF55430D7",
        },
        "TargetTrackingScalingPolicyConfiguration": Object {
          "PredefinedMetricSpecification": Object {
            "PredefinedMetricType": "ElastiCachePrimaryEngineCPUUtilization",
          },
          "TargetValue": 80,
        },
      },
      "Type": "AWS::ApplicationAutoScaling::ScalingPolicy",
    },
    "ElastiCacheElastiCacheShardsScalableTargetF55430D7": Object {
      "DependsOn": Array [
        "ElastiCacheBLEAElastiCache0C3B8B73",
      ],
      "Properties": Object {
        "MaxCapacity": 8,
        "MinCapacity": 2,
        "ResourceId": "replication-group/TestElastiCacheStackSelfDesignedSnapshot-repGroup",
        "RoleARN": Object {
          "Fn::GetAtt": Array [
            "ElastiCacheElastiCacheShardsScalableTargetRole1E6FCA0A",
            "Arn",
          ],
        },
        "ScalableDimension": "elasticache:replication-group:NodeGroups",
        "ServiceNamespace": "elasticache",
      },
      "Type": "AWS::ApplicationAutoScaling::ScalableTarget",
    },
    "ElastiCacheElastiCacheShardsScalableTargetRole1E6FCA0A": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "application-autoscaling.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ElastiCacheLoggroup6155D988": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Retain",
    },
    "ElastiCacheSecret": Object {
      "Type": "AWS::SecretsManager::Secret",
    },
    "ElastiCacheSecuritygGroup6A20C698": Object {
      "Properties": Object {
        "GroupDescription": "TestElastiCacheStackSelfDesignedSnapshot/ElastiCacheSecuritygGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": Object {
          "Fn::ImportValue": "MockResourcesStack2:ExportsOutputRefMockVpc92466B7FEBC0349C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "ElastiCacheSecuritygGroupfromMockResourcesStack2MockBastionSGB3CE5CBE6379C7451769": Object {
      "Properties": Object {
        "Description": "from MockResourcesStack2MockBastionSGB3CE5CBE:6379",
        "FromPort": 6379,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ElastiCacheSecuritygGroup6A20C698",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::ImportValue": "MockResourcesStack2:ExportsOutputFnGetAttMockBastionSG53CD02A6GroupId96F3C7FD",
        },
        "ToPort": 6379,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ElastiCacheSubnetGroup": Object {
      "Properties": Object {
        "CacheSubnetGroupName": "TestElastiCacheStackSelfDesignedSnapshot-Subnetgroup",
        "Description": "for elasticache",
        "SubnetIds": Array [
          Object {
            "Fn::ImportValue": "MockResourcesStack2:ExportsOutputRefMockVpcisolatedSubnet1SubnetECB8B2D4B301CBD4",
          },
          Object {
            "Fn::ImportValue": "MockResourcesStack2:ExportsOutputRefMockVpcisolatedSubnet2Subnet9E7C9670FD53044B",
          },
        ],
      },
      "Type": "AWS::ElastiCache::SubnetGroup",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`The ElastiCache stack Should Match Expected Snapshot When ElastiCache Stack Is Created With Serverless Configuration 1`] = `
Object {
  "Outputs": Object {
    "ElastiCacheEndPoint": Object {
      "Export": Object {
        "Name": "BLEA-ElastiCacheEndPoint",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ElastiCacheServerlessBLEAElastiCacheServerlessFA8DAFDB",
          "Endpoint.Address",
        ],
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "ElastiCacheLoggroup6155D988": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Retain",
    },
    "ElastiCacheSecret": Object {
      "Type": "AWS::SecretsManager::Secret",
    },
    "ElastiCacheSecuritygGroup6A20C698": Object {
      "Properties": Object {
        "GroupDescription": "TestElastiCacheStackServerlessSnapshot/ElastiCacheSecuritygGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": Object {
          "Fn::ImportValue": "MockResourcesStack1:ExportsOutputRefMockVpc92466B7FEBC0349C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "ElastiCacheSecuritygGroupfromMockResourcesStack1MockBastionSG0E598CED6379C872BBB7": Object {
      "Properties": Object {
        "Description": "from MockResourcesStack1MockBastionSG0E598CED:6379",
        "FromPort": 6379,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ElastiCacheSecuritygGroup6A20C698",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::ImportValue": "MockResourcesStack1:ExportsOutputFnGetAttMockBastionSG53CD02A6GroupId96F3C7FD",
        },
        "ToPort": 6379,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ElastiCacheServerlessBLEAElastiCacheServerlessFA8DAFDB": Object {
      "Properties": Object {
        "CacheUsageLimits": Object {
          "DataStorage": Object {
            "Maximum": 123,
            "Minimum": 0,
            "Unit": "GB",
          },
          "ECPUPerSecond": Object {
            "Maximum": 1000,
            "Minimum": 0,
          },
        },
        "Engine": "valkey",
        "KmsKeyId": Object {
          "Fn::ImportValue": "MockResourcesStack1:ExportsOutputRefMockKey4B46A5C551AD5A43",
        },
        "MajorEngineVersion": "7",
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "ElastiCacheSecuritygGroup6A20C698",
              "GroupId",
            ],
          },
        ],
        "ServerlessCacheName": "blea-elasticacheserverless",
        "SubnetIds": Array [
          Object {
            "Fn::ImportValue": "MockResourcesStack1:ExportsOutputRefMockVpcisolatedSubnet1SubnetECB8B2D4B301CBD4",
          },
          Object {
            "Fn::ImportValue": "MockResourcesStack1:ExportsOutputRefMockVpcisolatedSubnet2Subnet9E7C9670FD53044B",
          },
        ],
      },
      "Type": "AWS::ElastiCache::ServerlessCache",
    },
    "ElastiCacheSubnetGroup": Object {
      "Properties": Object {
        "CacheSubnetGroupName": "TestElastiCacheStackServerlessSnapshot-Subnetgroup",
        "Description": "for elasticache",
        "SubnetIds": Array [
          Object {
            "Fn::ImportValue": "MockResourcesStack1:ExportsOutputRefMockVpcisolatedSubnet1SubnetECB8B2D4B301CBD4",
          },
          Object {
            "Fn::ImportValue": "MockResourcesStack1:ExportsOutputRefMockVpcisolatedSubnet2Subnet9E7C9670FD53044B",
          },
        ],
      },
      "Type": "AWS::ElastiCache::SubnetGroup",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
