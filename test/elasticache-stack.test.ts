import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import { ElastiCacheStack } from '../lib/stack/elasticache-stack';
import { IConfig, IElastiCacheParam, IElastiCacheTypeParam } from '../params/interface';

const procEnv = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION ?? 'ap-northeast-1',
};

const pjPrefix = 'BLEA';

const envKey = 'dev';

const config: IConfig = require('../params/' + envKey);

function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnv;
  }
}

// Helper function to create all mock resources in one stack
const createMockResources = (app: cdk.App, stackSuffix = '') => {
  const mockStack = new cdk.Stack(app, `MockResourcesStack${stackSuffix}`, {
    env: procEnv,
  });

  const mockVpc = new ec2.Vpc(mockStack, 'MockVpc', {
    maxAzs: 3,
    subnetConfiguration: [
      {
        cidrMask: 24,
        name: 'isolated',
        subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
      },
    ],
  });

  const mockKey = new kms.Key(mockStack, 'MockKey');

  const mockTopic = new sns.Topic(mockStack, 'MockTopic');

  const mockAppSecurityGroup = new ec2.SecurityGroup(mockStack, 'MockAppSG', {
    vpc: mockVpc,
  });

  const mockBastionSecurityGroup = new ec2.SecurityGroup(mockStack, 'MockBastionSG', {
    vpc: mockVpc,
  });

  return {
    mockVpc,
    mockKey,
    mockTopic,
    mockAppSecurityGroup,
    mockBastionSecurityGroup,
  };
};

// Mock ElastiCache Parameters for SERVERLESS mode
const createServerlessElastiCacheParam = (): IElastiCacheParam => ({
  ElastiCacheSelfDesignedParam: {
    engine: 'valkey',
    engineVersion: '7.2',
    numNodeGroups: 1,
    replicasPerNodeGroup: 1,
    minCapacity: 1,
    maxCapacity: 1,
    targetValueToScale: 70,
    predefinedMetricToScale: appscaling.PredefinedMetric.ELASTICACHE_PRIMARY_ENGINE_CPU_UTILIZATION,
    enableAutoScale: false,
    cacheNodeTypeEnableAutoScale: 'cache.m7g.large',
    cacheNodeTypeDisableAutoScale: 'cache.t4g.small',
    elastiCacheCustomParam: {
      cacheParameterGroupFamily: 'valkey7',
      description: 'CustomParameterGroupForValkey',
      properties: {
        'cluster-enabled': 'no',
      },
    },
  },
  ElastiCacheServerlessParam: {
    engine: 'valkey',
    engineVersion: '7',
    dataStorageMaximum: 123,
    dataStorageMinimum: 0,
    ecpuPerSecondMaximum: 1000,
    ecpuPerSecondMinimum: 0,
  },
});

// Mock ElastiCache Parameters for SELFDESIGNED mode
const createSelfDesignedElastiCacheParam = (): IElastiCacheParam => ({
  ElastiCacheSelfDesignedParam: {
    engine: 'redis',
    engineVersion: '7.0',
    numNodeGroups: 2,
    replicasPerNodeGroup: 2,
    minCapacity: 2,
    maxCapacity: 8,
    targetValueToScale: 80,
    predefinedMetricToScale: appscaling.PredefinedMetric.ELASTICACHE_PRIMARY_ENGINE_CPU_UTILIZATION,
    enableAutoScale: true,
    cacheNodeTypeEnableAutoScale: 'cache.m5.large',
    cacheNodeTypeDisableAutoScale: 'cache.t3.small',
    elastiCacheCustomParam: {
      cacheParameterGroupFamily: 'redis7',
      description: 'CustomParameterGroupForRedis',
      properties: {
        'cluster-enabled': 'yes',
      },
    },
  },
  ElastiCacheServerlessParam: {
    engine: 'valkey',
    engineVersion: '7',
    dataStorageMaximum: 123,
    dataStorageMinimum: 0,
    ecpuPerSecondMaximum: 1000,
    ecpuPerSecondMinimum: 0,
  },
});

// Mock ElastiCache Type for SERVERLESS
const createServerlessTypeParam = (): IElastiCacheTypeParam => ({
  elastiCacheType: 'SERVERLESS',
});

// Mock ElastiCache Type for SELFDESIGNED
const createSelfDesignedTypeParam = (): IElastiCacheTypeParam => ({
  elastiCacheType: 'SELFDESIGNED',
});

describe('The ElastiCache stack', () => {
  let app: cdk.App;

  beforeEach(() => {
    app = new cdk.App();
  });

  // Assert Snapshot
  test('Should Match Expected Snapshot When ElastiCache Stack Is Created With Serverless Configuration', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '1',
    );
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackServerlessSnapshot', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  test('Should Match Expected Snapshot When ElastiCache Stack Is Created With Self-Designed Configuration', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '2',
    );
    const elastiCacheParam = createSelfDesignedElastiCacheParam();
    const elastiCacheType = createSelfDesignedTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackSelfDesignedSnapshot', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  // Assert Number of Resources
  test('Should Create Correct Number Of Resources When Serverless Configuration Is Provided', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '3',
    );
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackServerlessResourceCount', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify expected resource counts for serverless mode
    template.resourceCountIs('AWS::SecretsManager::Secret', 1);
    template.resourceCountIs('AWS::Logs::LogGroup', 1);
    template.resourceCountIs('AWS::EC2::SecurityGroup', 1);
    template.resourceCountIs('AWS::ElastiCache::SubnetGroup', 1);
    template.resourceCountIs('AWS::ElastiCache::ServerlessCache', 1);
    template.resourceCountIs('AWS::EC2::SecurityGroupIngress', 1); // For bastion security group
  });

  test('Should Create Correct Number Of Resources When Self-Designed Configuration Is Provided', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '4',
    );
    const elastiCacheParam = createSelfDesignedElastiCacheParam();
    const elastiCacheType = createSelfDesignedTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackSelfDesignedResourceCount', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify expected resource counts for self-designed mode
    template.resourceCountIs('AWS::SecretsManager::Secret', 1);
    template.resourceCountIs('AWS::Logs::LogGroup', 1);
    template.resourceCountIs('AWS::EC2::SecurityGroup', 1);
    template.resourceCountIs('AWS::ElastiCache::SubnetGroup', 1);
    template.resourceCountIs('AWS::EC2::SecurityGroupIngress', 1); // For bastion security group
    // Note: ElastiCache construct will create additional resources
  });

  // Assert Correct Policy & Assert Resource Properties
  test('Should Create Serverless Cache With Correct Properties When Serverless Configuration Is Provided', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '5',
    );
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackServerless', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Serverless Cache properties
    template.hasResourceProperties('AWS::ElastiCache::ServerlessCache', {
      Engine: 'valkey',
      MajorEngineVersion: '7',
      CacheUsageLimits: {
        DataStorage: {
          Maximum: 123,
          Minimum: 0,
          Unit: 'GB',
        },
        ECPUPerSecond: {
          Maximum: 1000,
          Minimum: 0,
        },
      },
      KmsKeyId: Match.anyValue(),
      SecurityGroupIds: Match.anyValue(),
      SubnetIds: Match.anyValue(),
    });

    // Verify Security Group properties
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      VpcId: Match.anyValue(),
      SecurityGroupEgress: [
        {
          CidrIp: '0.0.0.0/0',
          Description: 'Allow all outbound traffic by default',
          IpProtocol: '-1',
        },
      ],
    });

    // Verify Security Group Ingress Rule for Bastion
    template.hasResourceProperties('AWS::EC2::SecurityGroupIngress', {
      FromPort: 6379,
      ToPort: 6379,
      IpProtocol: 'tcp',
      SourceSecurityGroupId: Match.anyValue(),
    });
  });

  test('Should Create Self-Designed Cache Infrastructure With Correct Properties When Self-Designed Configuration Is Provided', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '6',
    );
    const elastiCacheParam = createSelfDesignedElastiCacheParam();
    const elastiCacheType = createSelfDesignedTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackSelfDesigned', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Secret Manager is created
    template.hasResourceProperties('AWS::SecretsManager::Secret', {});

    // Verify Log Group properties
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      RetentionInDays: 90, // THREE_MONTHS
    });

    // Verify Subnet Group properties
    template.hasResourceProperties('AWS::ElastiCache::SubnetGroup', {
      CacheSubnetGroupName: `${stack.stackName}-Subnetgroup`,
      Description: 'for elasticache',
      SubnetIds: Match.anyValue(),
    });

    // Verify Security Group properties
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      VpcId: Match.anyValue(),
      SecurityGroupEgress: [
        {
          CidrIp: '0.0.0.0/0',
          Description: 'Allow all outbound traffic by default',
          IpProtocol: '-1',
        },
      ],
    });
  });

  test('Should Create Security Group With Bastion Access When Bastion Security Group Is Provided', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '7',
    );
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackBastionAccess', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Security Group Ingress Rule for Bastion is created
    template.hasResourceProperties('AWS::EC2::SecurityGroupIngress', {
      Description: Match.stringLikeRegexp('.*6379'),
      FromPort: 6379,
      ToPort: 6379,
      IpProtocol: 'tcp',
      GroupId: Match.anyValue(),
      SourceSecurityGroupId: Match.anyValue(),
    });
  });

  test('Should Create Security Group Without Bastion Access When Bastion Security Group Is Not Provided', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup } = createMockResources(app, '8');
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackNoBastionAccess', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      // bastionSecurityGroup: undefined (not provided)
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Security Group is still created
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      VpcId: Match.anyValue(),
    });

    // Verify no bastion ingress rule is created when bastion SG is not provided
    // Only verify that security group exists, not specific ingress rules
    template.resourceCountIs('AWS::EC2::SecurityGroupIngress', 0);
  });

  test('Should Apply Custom Removal Policy When Log Group Removal Policy Is Provided', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '9',
    );
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackCustomRemovalPolicy', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      logGroupRemovalPolicy: cdk.RemovalPolicy.DESTROY,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Log Group is created
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      RetentionInDays: 90,
    });

    // Verify removal policy is applied (DeletionPolicy should be 'Delete')
    const logGroups = template.findResources('AWS::Logs::LogGroup');
    const logGroupKeys = Object.keys(logGroups);
    expect(logGroupKeys.length).toBeGreaterThan(0);

    const logGroup = logGroups[logGroupKeys[0]];
    expect(logGroup.DeletionPolicy).toBe('Delete');
    expect(logGroup.UpdateReplacePolicy).toBe('Delete');
  });

  test('Should Use Default RETAIN Removal Policy When No Custom Removal Policy Is Provided', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '10',
    );
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackDefaultRemovalPolicy', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      // logGroupRemovalPolicy: undefined (not provided)
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Log Group is created
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      RetentionInDays: 90,
    });

    // Verify default removal policy is applied (DeletionPolicy should be 'Retain')
    const logGroups = template.findResources('AWS::Logs::LogGroup');
    const logGroupKeys = Object.keys(logGroups);
    expect(logGroupKeys.length).toBeGreaterThan(0);

    const logGroup = logGroups[logGroupKeys[0]];
    expect(logGroup.DeletionPolicy).toBe('Retain');
    expect(logGroup.UpdateReplacePolicy).toBe('Retain');
  });

  test('Should Create Stack Name Based Subnet Group When ElastiCache Stack Is Created', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup } = createMockResources(app, '11');
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackSubnetGroup', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Subnet Group has stack name in the name
    template.hasResourceProperties('AWS::ElastiCache::SubnetGroup', {
      CacheSubnetGroupName: 'TestElastiCacheStackSubnetGroup-Subnetgroup',
      Description: 'for elasticache',
      SubnetIds: Match.anyValue(),
    });
  });

  test('Should Handle Valkey Engine Configuration When Engine Is Set To Valkey', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup } = createMockResources(app, '12');
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackValkeyEngine', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Serverless Cache uses Valkey engine
    template.hasResourceProperties('AWS::ElastiCache::ServerlessCache', {
      Engine: 'valkey',
      MajorEngineVersion: '7',
    });
  });

  test('Should Handle Redis Engine Configuration When Engine Is Set To Redis', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup } = createMockResources(app, '13');
    const elastiCacheParam = createSelfDesignedElastiCacheParam();
    const elastiCacheType = createSelfDesignedTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackRedisEngine', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify basic resources are created (self-designed mode uses construct)
    template.hasResourceProperties('AWS::SecretsManager::Secret', {});
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      RetentionInDays: 90,
    });
    template.hasResourceProperties('AWS::ElastiCache::SubnetGroup', {
      Description: 'for elasticache',
    });
  });

  test('Should Create Stack With Cross-Region References When Cross-Region References Are Enabled', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup, mockBastionSecurityGroup } = createMockResources(
      app,
      '14',
    );
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackCrossRegion', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      bastionSecurityGroup: mockBastionSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
      crossRegionReferences: true,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify stack is created successfully with cross-region references
    expect(stack.stackName).toBe('TestElastiCacheStackCrossRegion');

    // Verify resources are still created properly
    template.resourceCountIs('AWS::SecretsManager::Secret', 1);
    template.resourceCountIs('AWS::Logs::LogGroup', 1);
    template.resourceCountIs('AWS::EC2::SecurityGroup', 1);
    template.resourceCountIs('AWS::ElastiCache::SubnetGroup', 1);
    template.resourceCountIs('AWS::ElastiCache::ServerlessCache', 1);
  });

  test('Should Create Common Resources Regardless Of Cache Type When ElastiCache Stack Is Created', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup } = createMockResources(app, '15');
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackCommonResources', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify common resources are always created
    template.hasResourceProperties('AWS::SecretsManager::Secret', {});

    template.hasResourceProperties('AWS::Logs::LogGroup', {
      RetentionInDays: 90,
    });

    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      VpcId: Match.anyValue(),
      SecurityGroupEgress: [
        {
          CidrIp: '0.0.0.0/0',
          Description: 'Allow all outbound traffic by default',
          IpProtocol: '-1',
        },
      ],
    });

    template.hasResourceProperties('AWS::ElastiCache::SubnetGroup', {
      CacheSubnetGroupName: Match.anyValue(),
      Description: 'for elasticache',
      SubnetIds: Match.anyValue(),
    });
  });

  test('Should Handle Isolated Subnets For ElastiCache When VPC Has Isolated Subnets', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup } = createMockResources(app, '16');
    const elastiCacheParam = createServerlessElastiCacheParam();
    const elastiCacheType = createServerlessTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackIsolatedSubnets', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Subnet Group uses isolated subnets
    template.hasResourceProperties('AWS::ElastiCache::SubnetGroup', {
      SubnetIds: Match.anyValue(),
    });

    // Verify Serverless Cache is placed in subnets
    template.hasResourceProperties('AWS::ElastiCache::ServerlessCache', {
      SubnetIds: Match.anyValue(),
    });
  });

  // test property enableAutoScale is false
  test('Should Not Enable Auto Scaling When enableAutoScale Is False', () => {
    // Arrange
    const { mockVpc, mockKey, mockTopic, mockAppSecurityGroup } = createMockResources(app, '17');
    const elastiCacheParam = createServerlessElastiCacheParam();
    elastiCacheParam.ElastiCacheSelfDesignedParam.enableAutoScale = false; // Disable auto scaling
    const elastiCacheType = createSelfDesignedTypeParam();

    // Act
    const stack = new ElastiCacheStack(app, 'TestElastiCacheStackNoAutoScale', {
      pjPrefix: pjPrefix,
      myVpc: mockVpc,
      appKey: mockKey,
      alarmTopic: mockTopic,
      appServerSecurityGroup: mockAppSecurityGroup,
      ElastiCacheParam: elastiCacheParam,
      elastiCacheType: elastiCacheType,
      env: procEnv,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify no auto scaling policy is created
    template.resourceCountIs('AWS::ApplicationAutoScaling::ScalableTarget', 0);
  });
});
