// test/waf-cloudfront-stack.test.ts
import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { WafCfStack } from '../lib/stack/waf-cloudfront-stack';
import { envKey, getProcEnv, pjPrefix } from '../test';

let app: any;
// Helper function to create test stacks with specific configurations
function createTestStack(id: string, props: Partial<ConstructorParameters<typeof WafCfStack>[2]> = {}) {
  const testApp = new cdk.App();

  // Add environment tag if needed
  const envTagName = 'Environment';
  cdk.Tags.of(testApp).add(envTagName, envKey);

  // Merge default props with the provided props
  const stackProps = {
    scope: 'CLOUDFRONT',
    wafLogBucketLifecycleRules: [],
    env: getProcEnv(),
    ...props,
  };

  const stack = new WafCfStack(testApp, id, stackProps);
  return { stack, template: Template.fromStack(stack) };
}

describe(`${pjPrefix} WafCloudfront Stack`, () => {
  // Base WAF stack for most tests
  let wafCfStack: WafCfStack;
  let template: Template;

  beforeEach(() => {
    app = new cdk.App();
    const envTagName = 'Environment';
    cdk.Tags.of(app).add(envTagName, envKey);

    wafCfStack = new WafCfStack(app, `${pjPrefix}-WafCloudfront`, {
      scope: 'CLOUDFRONT',
      wafLogBucketLifecycleRules: [],
      env: getProcEnv(),
    });
    template = Template.fromStack(wafCfStack);
  });

  test('should match snapshot when stack is created', () => {
    expect(template).toMatchSnapshot();
  });

  test('should create WebACL resource when stack is initialized', () => {
    template.resourceCountIs('AWS::WAFv2::WebACL', 1);
  });

  test('should expose webAcl property when stack is created', () => {
    expect(wafCfStack.webAcl).toBeDefined();
  });

  test('should create AWS managed rule groups when override actions are provided', () => {
    // Arrange
    const { template: rulesTemplate } = createTestStack('MRules', {
      overrideAction_CommonRuleSet: { count: {} },
      overrideAction_KnownBadInputsRuleSet: { count: {} },
      overrideAction_AmazonIpReputationList: { count: {} },
      overrideAction_LinuxRuleSet: { count: {} },
      overrideAction_SQLiRuleSet: { count: {} },
    });

    // Assert
    rulesTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: Match.stringLikeRegexp('AWSManagedRulesCommonRuleSet'),
          OverrideAction: { Count: {} },
        }),
        Match.objectLike({
          Name: Match.stringLikeRegexp('AWSManagedRulesKnownBadInputsRuleSet'),
          OverrideAction: { Count: {} },
        }),
        Match.objectLike({
          Name: Match.stringLikeRegexp('AWSManagedRulesAmazonIpReputationList'),
          OverrideAction: { Count: {} },
        }),
        Match.objectLike({
          Name: Match.stringLikeRegexp('AWSManagedRulesLinuxRuleSet'),
          OverrideAction: { Count: {} },
        }),
        Match.objectLike({
          Name: Match.stringLikeRegexp('AWSManagedRulesSQLiRuleSet'),
          OverrideAction: { Count: {} },
        }),
      ]),
    });
  });

  test('should create IP set rule when allowIPList is provided', () => {
    // Arrange
    const allowIPList = ['***********/32', '10.0.0.0/24'];
    const { template: ipSetTemplate } = createTestStack(`${pjPrefix}-Waf-IP`, {
      allowIPList,
      ruleAction_IPsetRuleSet: { allow: {} },
    });

    // Assert
    ipSetTemplate.resourceCountIs('AWS::WAFv2::IPSet', 1);
    ipSetTemplate.hasResourceProperties('AWS::WAFv2::IPSet', {
      Addresses: Match.arrayEquals(allowIPList),
      IPAddressVersion: 'IPV4',
      Scope: 'CLOUDFRONT',
    });

    ipSetTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: 'IPset',
          Action: { Allow: {} },
        }),
      ]),
    });
  });

  test('should create basic auth rule when basic auth credentials are provided', () => {
    // Arrange
    const basicAuthUserName = 'testuser';
    const basicAuthUserPass = 'testpass';

    const { template: basicAuthTemplate } = createTestStack(`${pjPrefix}-Waf-BA`, {
      basicAuthUserName,
      basicAuthUserPass,
      ruleAction_BasicRuleSet: { allow: {} },
    });

    basicAuthTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: 'BasicAuth',
          Action: { Allow: {} },
        }),
      ]),
    });
  });

  test('should create WAF log configuration when logRemovalPolicyParam is provided', () => {
    // Arrange
    const { template: logsTemplate } = createTestStack('WafLogs', {
      wafLogBucketLifecycleRules: [
        {
          id: 'ExpireAfter90Days',
          enabled: true,
          expiration: cdk.Duration.days(90),
        },
      ],
      logRemovalPolicyParam: {
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        autoDeleteObjects: true,
      },
    });

    logsTemplate.resourceCountIs('AWS::S3::Bucket', 1);
    logsTemplate.resourceCountIs('AWS::WAFv2::LoggingConfiguration', 1);

    logsTemplate.hasResourceProperties('AWS::S3::Bucket', {
      BucketEncryption: {
        ServerSideEncryptionConfiguration: Match.arrayWith([
          Match.objectLike({
            ServerSideEncryptionByDefault: {
              SSEAlgorithm: 'AES256',
            },
          }),
        ]),
      },
    });
  });

  test('should create CSIRT WAF rules when csirtWAFParam is provided', () => {
    // Arrange
    const csirtWAFParam = {
      isUseCSIRTManageRules: false,
      CSIRTManagerRules: {
        overrideAction: { count: {} },
        ruleGroupArn:
          'arn:aws:wafv2:us-east-1:123456789012:global/rulegroup/example/12345678-1234-1234-1234-123456789012',
      },
      CSIRTIpSetArn: '',
      enableMatchingRequestRate: true,
      requestRateRulePriority: 1,
      requestRateLimit: 100,
      matchingRequestRateRuleName: 'CSIRT-RequestRateRule',
    };

    const { template: csirtTemplate } = createTestStack(`${pjPrefix}-Waf-CSIRT`, {
      csirtWAFParam,
    });

    csirtTemplate.resourceCountIs('AWS::WAFv2::WebACL', 1);
  });

  test('should set termination protection when terminationProtection is true', () => {
    // Arrange
    const { stack: stackWithProtection } = createTestStack(`${pjPrefix}-Waf-TP`, {
      terminationProtection: true,
    });

    expect(stackWithProtection.terminationProtection).toBe(true);
  });

  test('should create WebACL with Allow defaultAction when no defaultAction is specified', () => {
    const { template: defaultActionTemplate } = createTestStack(`${pjPrefix}-WafCf-DefAct`);

    defaultActionTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      DefaultAction: {
        Allow: {},
      },
    });
  });

  test('should override action for multiple rule sets with different actions', () => {
    // Arrange
    const { template: mixedOverridesTemplate } = createTestStack(`${pjPrefix}-WafCf-MixedOvr`, {
      overrideAction_CommonRuleSet: { none: {} },
      overrideAction_KnownBadInputsRuleSet: { count: {} },
      overrideAction_AmazonIpReputationList: { none: {} },
      overrideAction_LinuxRuleSet: { count: {} },
      overrideAction_SQLiRuleSet: { none: {} },
    });

    // Assert
    mixedOverridesTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: Match.stringLikeRegexp('AWSManagedRulesCommonRuleSet'),
          OverrideAction: { None: {} },
        }),
        Match.objectLike({
          Name: Match.stringLikeRegexp('AWSManagedRulesKnownBadInputsRuleSet'),
          OverrideAction: { Count: {} },
        }),
      ]),
    });
  });

  test('should create different rule combinations when multiple rule options are provided', () => {
    // Arrange
    const { template: multipleRulesTemplate } = createTestStack(`${pjPrefix}-WafCf-MultiRules`, {
      allowIPList: ['***********/32'],
      ruleAction_IPsetRuleSet: { allow: {} },
      basicAuthUserName: 'testuser',
      basicAuthUserPass: 'testpass',
      ruleAction_BasicRuleSet: { allow: {} },
      overrideAction_CommonRuleSet: { count: {} },
    });

    multipleRulesTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: 'IPset',
          Action: { Allow: {} },
        }),
        Match.objectLike({
          Name: 'BasicAuth',
          Action: { Allow: {} },
        }),
        Match.objectLike({
          Name: 'AWSManagedRulesCommonRuleSet',
          OverrideAction: { Count: {} },
        }),
      ]),
    });
  });

  test('should set correct priority values for rules', () => {
    const { template: priorityTemplate } = createTestStack(`${pjPrefix}-Waf-Priority`, {
      allowIPList: ['***********/32'],
      ruleAction_IPsetRuleSet: { allow: {} },
    });

    priorityTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: 'IPset',
          Priority: Match.anyValue(),
        }),
      ]),
    });
  });

  test('should apply correct tags to resources', () => {
    const { template: tagTemplate } = createTestStack(`${pjPrefix}-Waf-Tags`);

    tagTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      Tags: Match.arrayWith([
        Match.objectLike({
          Key: 'Environment',
          Value: envKey,
        }),
      ]),
    });
  });

  test('should create regional WAF when scope is set to REGIONAL', () => {
    const { template: regionalTemplate } = createTestStack(`${pjPrefix}-Waf-Regional`, {
      scope: 'REGIONAL',
    });

    regionalTemplate.hasResourceProperties('AWS::WAFv2::WebACL', {
      Scope: 'REGIONAL',
    });
  });

  // Test S3 policy creation and duplication prevention
  describe('S3 Policy Management', () => {
    test('Should create unique S3 bucket When WAF construct is instantiated', () => {
      const { template } = createTestStack(`${pjPrefix}-WafCf-S3Policy`, {
        wafLogBucketLifecycleRules: [
          {
            id: 'ExpireAfter30Days',
            enabled: true,
            expiration: cdk.Duration.days(30),
          },
        ],
      });

      // Should create exactly one S3 bucket
      template.resourceCountIs('AWS::S3::Bucket', 1);

      // Should create exactly one logging configuration
      template.resourceCountIs('AWS::WAFv2::LoggingConfiguration', 1);
    });

    test('Should not create duplicate S3 buckets When multiple WAF constructs exist in different stacks', () => {
      // Create first WAF stack
      const { template: template1 } = createTestStack(`${pjPrefix}-WafCf-First`, {
        wafLogBucketLifecycleRules: [
          {
            id: 'ExpireAfter30Days',
            enabled: true,
            expiration: cdk.Duration.days(30),
          },
        ],
      });

      // Create second WAF stack
      const { template: template2 } = createTestStack(`${pjPrefix}-WafCf-Second`, {
        wafLogBucketLifecycleRules: [
          {
            id: 'ExpireAfter60Days',
            enabled: true,
            expiration: cdk.Duration.days(60),
          },
        ],
      });

      // Each stack should have exactly one bucket
      template1.resourceCountIs('AWS::S3::Bucket', 1);
      template2.resourceCountIs('AWS::S3::Bucket', 1);

      // Each stack should have exactly one logging configuration
      template1.resourceCountIs('AWS::WAFv2::LoggingConfiguration', 1);
      template2.resourceCountIs('AWS::WAFv2::LoggingConfiguration', 1);
    });

    test('Should create S3 bucket with unique name When multiple WAF stacks are deployed', () => {
      const { template: template1 } = createTestStack(`${pjPrefix}-WafCf-Unique1`, {
        wafLogBucketLifecycleRules: [],
      });
      const { template: template2 } = createTestStack(`${pjPrefix}-WafCf-Unique2`, {
        wafLogBucketLifecycleRules: [],
      });

      // Both should have buckets with different names based on stack name
      template1.hasResourceProperties('AWS::S3::Bucket', {
        BucketName: Match.stringLikeRegexp('.*-wafcfunique1-.*'),
      });

      template2.hasResourceProperties('AWS::S3::Bucket', {
        BucketName: Match.stringLikeRegexp('.*-wafcfunique2-.*'),
      });
    });

    test('Should create S3 bucket policy with correct permissions When WAF logging is configured', () => {
      const { template } = createTestStack(`${pjPrefix}-WafCf-PolicyPerms`, {
        wafLogBucketLifecycleRules: [
          {
            id: 'ExpireAfter90Days',
            enabled: true,
            expiration: cdk.Duration.days(90),
          },
        ],
      });

      // Note: CloudFront WAF creates bucket but no explicit bucket policy in the snapshot
      // The bucket policy is automatically created by CDK for WAF logging
      template.resourceCountIs('AWS::S3::Bucket', 1);

      // Verify bucket has correct properties for WAF logging
      template.hasResourceProperties('AWS::S3::Bucket', {
        BucketName: Match.stringLikeRegexp('^aws-waf-logs-.*-bucket$'),
        AccessControl: 'Private',
        BucketEncryption: {
          ServerSideEncryptionConfiguration: [
            {
              ServerSideEncryptionByDefault: {
                SSEAlgorithm: 'AES256',
              },
            },
          ],
        },
        PublicAccessBlockConfiguration: {
          BlockPublicAcls: true,
          BlockPublicPolicy: true,
          IgnorePublicAcls: true,
          RestrictPublicBuckets: true,
        },
      });
    });

    test('Should create logging configuration with dependency on bucket policy When bucket policy exists', () => {
      const { template } = createTestStack(`${pjPrefix}-WafCf-LogDep`, {
        wafLogBucketLifecycleRules: [
          {
            id: 'ExpireAfter30Days',
            enabled: true,
            expiration: cdk.Duration.days(30),
          },
        ],
      });

      // Should create logging configuration
      template.resourceCountIs('AWS::WAFv2::LoggingConfiguration', 1);

      // Logging configuration should reference the bucket and WebACL
      template.hasResourceProperties('AWS::WAFv2::LoggingConfiguration', {
        LogDestinationConfigs: [
          Match.objectLike({
            'Fn::GetAtt': Match.arrayWith([
              Match.stringLikeRegexp('.*WafLogsBucket.*'),
              'Arn',
            ]),
          }),
        ],
        ResourceArn: Match.objectLike({
          'Fn::GetAtt': Match.arrayWith([
            Match.stringLikeRegexp('.*WebAcl.*'),
            'Arn',
          ]),
        }),
      });
    });
  });
});
