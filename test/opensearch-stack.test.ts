import * as cdk from 'aws-cdk-lib';
import { Match, Template } from 'aws-cdk-lib/assertions';
import { OpenSearchStack } from '../lib/stack/opensearch-stack';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as opensearch from 'aws-cdk-lib/aws-opensearchservice';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

// Constants for repeated values
const COLLECTION_TYPES = {
  SEARCH: 'SEARCH',
  TIMESERIES: 'TIMESERIES',
};

describe(`${pjPrefix} OpenSearch Stack`, () => {
  let app: cdk.App;

  // Helper function to create a standard test VPC
  function createTestVpc(stack: cdk.Stack, id: string): ec2.Vpc {
    return new ec2.Vpc(stack, id, {
      maxAzs: 2,
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'Public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'Private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 24,
          name: 'Isolated',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
      ],
    });
  }

  // Helper function to create a security group
  function createSecurityGroup(stack: cdk.Stack, id: string, vpc: ec2.Vpc): ec2.SecurityGroup {
    return new ec2.SecurityGroup(stack, id, {
      vpc,
      allowAllOutbound: true,
    });
  }

  // Helper function to create default OpenSearch props
  function getDefaultOpenSearchProps(overrides: any = {}): any {
    return {
      openSearchProvisionedParam: {
        engineVersion: overrides.engineVersion || opensearch.EngineVersion.OPENSEARCH_2_5,
        zoneAwareness: 2,
        ebsVolumeType: ec2.EbsDeviceVolumeType.GENERAL_PURPOSE_SSD_GP3,
        ebsVolumeSize: 10,
        ebsIops: 3000,
        dataNodes: 2,
        masterNodes: 3,
        masterNodeInstanceType: 'r6g.large.search',
        dataNodeInstanceType: 't3.small.search',
        ...overrides.provisionedParams,
      },
      openSearchServerlessParam: {
        collectionType: overrides.collectionType || COLLECTION_TYPES.SEARCH,
        ...overrides.serverlessParams,
      },
    };
  }

  beforeEach(() => {
    app = new cdk.App();
    const envTagName = 'Environment';
    cdk.Tags.of(app).add(envTagName, envKey);
  });

  test('should match snapshot when stack is created', () => {
    // Create the OpenSearch stack for testing
    const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch`, {
      env: getProcEnv(),
    });

    // Create resources within the stack
    const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');
    const testAppSecurityGroup = createSecurityGroup(stack, 'AppSecurityGroup', openSearchVpc);
    const testBastionSecurityGroup = createSecurityGroup(stack, 'BastionSecurityGroup', openSearchVpc);

    // Create the OpenSearch stack as a nested stack within our test stack
    new OpenSearchStack(app, `${pjPrefix}-OpenSearch-Stack`, {
      vpc: openSearchVpc,
      appServerSecurityGroup: testAppSecurityGroup,
      bastionSecurityGroup: testBastionSecurityGroup,
      openSearchType: { openSearchType: 'PROVISION' },
      openSearchProps: getDefaultOpenSearchProps(),
      env: getProcEnv(),
    });

    // Get the template from the stack
    const template = Template.fromStack(stack);

    // Assert
    expect(template).toMatchSnapshot();
  });

  describe('Security Groups', () => {
    test('should create domain security group when stack is initialized', () => {
      // Create the OpenSearch stack for testing
      const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch-SecurityGroup`, {
        env: getProcEnv(),
      });

      // Create resources within the stack
      const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');

      // Create the OpenSearch stack
      const openSearchStack = new OpenSearchStack(app, `${pjPrefix}-OpenSearch-SG-Test`, {
        vpc: openSearchVpc,
        openSearchType: { openSearchType: 'PROVISION' },
        openSearchProps: getDefaultOpenSearchProps(),
        env: getProcEnv(),
      });

      // Get the template from the OpenSearch stack
      const template = Template.fromStack(openSearchStack);

      // Assert that exactly one security group is created
      template.resourceCountIs('AWS::EC2::SecurityGroup', 1);

      // Assert the security group has the expected properties based on the snapshot
      template.hasResourceProperties('AWS::EC2::SecurityGroup', {
        GroupDescription: Match.anyValue(),
        VpcId: Match.anyValue(),
        SecurityGroupEgress: [
          Match.objectLike({
            CidrIp: '0.0.0.0/0',
            IpProtocol: '-1',
          }),
        ],
        Tags: Match.arrayWith([
          Match.objectLike({
            Key: 'Environment',
            Value: 'dev',
          }),
        ]),
      });
    });

    test('should create ingress rules when appServerSecurityGroup is provided', () => {
      // Create the OpenSearch stack for testing
      const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch-AppSG`, {
        env: getProcEnv(),
      });

      // Create resources within the stack
      const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');
      const testAppSecurityGroup = createSecurityGroup(stack, 'AppSecurityGroup', openSearchVpc);

      // Create the OpenSearch stack as a nested stack within our test stack
      const openSearchStack = new OpenSearchStack(app, `${pjPrefix}-OpenSearch-AppSG-Test`, {
        vpc: openSearchVpc,
        appServerSecurityGroup: testAppSecurityGroup,
        openSearchType: { openSearchType: 'PROVISION' },
        openSearchProps: getDefaultOpenSearchProps(),
        env: getProcEnv(),
      });

      // Get the template from the OpenSearch stack
      const template = Template.fromStack(openSearchStack);

      // Find all ingress rules
      const ingressRules = template.findResources('AWS::EC2::SecurityGroupIngress');

      // Assert that at least one ingress rule is created
      expect(Object.keys(ingressRules).length).toBeGreaterThan(0);

      // Assert the ingress rule has the correct properties
      template.hasResourceProperties('AWS::EC2::SecurityGroupIngress', {
        IpProtocol: 'tcp',
        FromPort: 443,
        ToPort: 443,
        GroupId: Match.anyValue(),
        SourceSecurityGroupId: Match.anyValue(),
      });
    });

    test('should create ingress rules when bastionSecurityGroup is provided', () => {
      // Create the OpenSearch stack for testing
      const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch-BastionSG`, {
        env: getProcEnv(),
      });

      // Create resources within the stack
      const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');
      const testBastionSecurityGroup = createSecurityGroup(stack, 'BastionSecurityGroup', openSearchVpc);

      // Create the OpenSearch stack as a nested stack within our test stack
      const openSearchStack = new OpenSearchStack(app, `${pjPrefix}-OpenSearch-BastionSG-Test`, {
        vpc: openSearchVpc,
        bastionSecurityGroup: testBastionSecurityGroup,
        openSearchType: { openSearchType: 'PROVISION' },
        openSearchProps: getDefaultOpenSearchProps(),
        env: getProcEnv(),
      });

      // Get the template from the OpenSearch stack
      const template = Template.fromStack(openSearchStack);

      // Find all ingress rules
      const ingressRules = template.findResources('AWS::EC2::SecurityGroupIngress');

      // Assert that at least one ingress rule is created
      expect(Object.keys(ingressRules).length).toBeGreaterThan(0);

      // Assert the ingress rule has the correct properties
      template.hasResourceProperties('AWS::EC2::SecurityGroupIngress', {
        IpProtocol: 'tcp',
        FromPort: 443,
        ToPort: 443,
        GroupId: Match.anyValue(),
        SourceSecurityGroupId: Match.anyValue(),
      });
    });
  });

  describe('Provisioned Mode', () => {
    test('should create OpenSearch domain when openSearchType is PROVISION', () => {
      // Create the OpenSearch stack for testing
      const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch-Provision`, {
        env: getProcEnv(),
      });

      // Create resources within the stack
      const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');

      // Create the OpenSearch stack as a nested stack within our test stack
      const openSearchStack = new OpenSearchStack(app, `${pjPrefix}-OpenSearch-Provision-Test`, {
        vpc: openSearchVpc,
        openSearchType: { openSearchType: 'PROVISION' },
        openSearchProps: getDefaultOpenSearchProps(),
        env: getProcEnv(),
      });

      // Get the template from the OpenSearch stack
      const template = Template.fromStack(openSearchStack);

      // Assert
      template.resourceCountIs('AWS::OpenSearchService::Domain', 1);
      template.hasResourceProperties('AWS::OpenSearchService::Domain', {
        EngineVersion: opensearch.EngineVersion.OPENSEARCH_2_5.version,
        ClusterConfig: {
          InstanceCount: 2,
          InstanceType: 't3.small.search',
          DedicatedMasterEnabled: true,
          DedicatedMasterCount: 3,
          DedicatedMasterType: 'r6g.large.search',
          ZoneAwarenessEnabled: true,
          ZoneAwarenessConfig: {
            AvailabilityZoneCount: 2,
          },
        },
        EBSOptions: {
          EBSEnabled: true,
          VolumeSize: 10,
          VolumeType: 'gp3',
          Iops: 3000,
        },
        VPCOptions: {
          SecurityGroupIds: Match.anyValue(),
          SubnetIds: Match.anyValue(),
        },
      });
    });

    test('should use specified engine version when creating OpenSearch domain in PROVISION mode', () => {
      // Create the OpenSearch stack for testing
      const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch-EngineVersion`, {
        env: getProcEnv(),
      });

      // Create resources within the stack
      const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');

      // Create the OpenSearch stack as a nested stack within our test stack
      const openSearchStack = new OpenSearchStack(app, `${pjPrefix}-OpenSearch-EngineVersion-Test`, {
        vpc: openSearchVpc,
        openSearchType: { openSearchType: 'PROVISION' },
        openSearchProps: getDefaultOpenSearchProps({
          engineVersion: opensearch.EngineVersion.OPENSEARCH_1_3,
        }),
        env: getProcEnv(),
      });

      // Get the template from the OpenSearch stack
      const template = Template.fromStack(openSearchStack);

      // Assert
      template.resourceCountIs('AWS::OpenSearchService::Domain', 1);
      template.hasResourceProperties('AWS::OpenSearchService::Domain', {
        EngineVersion: opensearch.EngineVersion.OPENSEARCH_1_3.version,
      });
    });

    test('should enable encryption at rest when creating OpenSearch domain in PROVISION mode', () => {
      // Create the OpenSearch stack for testing
      const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch-Encryption`, {
        env: getProcEnv(),
      });

      // Create resources within the stack
      const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');

      // Create the OpenSearch stack as a nested stack within our test stack
      const openSearchStack = new OpenSearchStack(app, `${pjPrefix}-OpenSearch-Encryption-Test`, {
        vpc: openSearchVpc,
        openSearchType: { openSearchType: 'PROVISION' },
        openSearchProps: getDefaultOpenSearchProps(),
        env: getProcEnv(),
      });

      // Get the template from the OpenSearch stack
      const template = Template.fromStack(openSearchStack);

      // Assert
      template.hasResourceProperties('AWS::OpenSearchService::Domain', {
        EncryptionAtRestOptions: {
          Enabled: true,
        },
        NodeToNodeEncryptionOptions: {
          Enabled: false,
        },
      });
    });
  });

  describe('Serverless Mode', () => {
    test('should create OpenSearchServerless collection when openSearchType is SERVERLESS', () => {
      // Create the OpenSearch stack for testing
      const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch-Serverless`, {
        env: getProcEnv(),
      });

      // Create resources within the stack
      const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');

      // Create the OpenSearch stack as a nested stack within our test stack
      const openSearchStack = new OpenSearchStack(app, `${pjPrefix}-OpenSearch-Serverless-Test`, {
        vpc: openSearchVpc,
        openSearchType: { openSearchType: 'SERVERLESS' },
        openSearchProps: getDefaultOpenSearchProps(),
        env: getProcEnv(),
      });

      // Get the template from the OpenSearch stack
      const template = Template.fromStack(openSearchStack);

      // Assert
      template.resourceCountIs('AWS::OpenSearchServerless::Collection', 1);
      template.hasResourceProperties('AWS::OpenSearchServerless::Collection', {
        Name: Match.stringLikeRegexp('blea-test-opensearch-serverless-test'),
        Type: COLLECTION_TYPES.SEARCH,
      });

      // Check for VPC Endpoint
      template.hasResourceProperties('AWS::OpenSearchServerless::VpcEndpoint', {
        Name: Match.stringLikeRegexp('blea-test-opensearch-serverless-test'),
        VpcId: Match.anyValue(),
        SubnetIds: Match.anyValue(),
        SecurityGroupIds: Match.anyValue(),
      });
    });

    test('should create OpenSearchServerless with TIMESERIES collection type when specified', () => {
      // Create the OpenSearch stack for testing
      const stack = new cdk.Stack(app, `${pjPrefix}-OpenSearch-TimeSeries`, {
        env: getProcEnv(),
      });

      // Create resources within the stack
      const openSearchVpc = createTestVpc(stack, 'OpenSearchVpc');

      // Create the OpenSearch stack as a nested stack within our test stack
      const openSearchStack = new OpenSearchStack(app, `${pjPrefix}-OpenSearch-TimeSeries-Test`, {
        vpc: openSearchVpc,
        openSearchType: { openSearchType: 'SERVERLESS' },
        openSearchProps: getDefaultOpenSearchProps({
          collectionType: COLLECTION_TYPES.TIMESERIES,
        }),
        env: getProcEnv(),
      });

      // Get the template from the OpenSearch stack
      const template = Template.fromStack(openSearchStack);

      // Assert
      template.resourceCountIs('AWS::OpenSearchServerless::Collection', 1);
      template.hasResourceProperties('AWS::OpenSearchServerless::Collection', {
        Name: Match.stringLikeRegexp('blea-test-opensearch-timeseries-test'),
        Type: COLLECTION_TYPES.TIMESERIES,
      });

      // Check for VPC Endpoint
      template.hasResourceProperties('AWS::OpenSearchServerless::VpcEndpoint', {
        Name: Match.stringLikeRegexp('blea-test-opensearch-timeseries-test'),
        VpcId: Match.anyValue(),
        SubnetIds: Match.anyValue(),
        SecurityGroupIds: Match.anyValue(),
      });
    });
  });
});
