import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { SendGridLogStack } from '../lib/stack/sendgrid-log-stack';
import { ISendGridLogParam, IConfig } from '../params/interface';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

describe('The SendGrid log stack', () => {
  let app: cdk.App;

  beforeEach(() => {
    app = new cdk.App();
  });

  // Assert Snapshot
  test('Should Match Expected Snapshot When SendGrid Log Stack Is Created With Basic Configuration', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackSnapshot', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  // Assert Number of Resources
  test('Should Create Correct Number Of Resources When SendGrid Configuration Is Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackResourceCount', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify exact resource counts
    template.resourceCountIs('AWS::S3::Bucket', 1);
    template.resourceCountIs('AWS::KinesisFirehose::DeliveryStream', 1);
    template.resourceCountIs('AWS::ApiGateway::RestApi', 1);
    template.resourceCountIs('AWS::Athena::WorkGroup', 1);
    template.resourceCountIs('AWS::Glue::Database', 1);
    template.resourceCountIs('AWS::Glue::Table', 1);
  });

  // Assert Correct Policy & Assert Resource Properties
  test('Should Create SendGrid Log Infrastructure With Correct Properties When Basic Configuration Is Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStack', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify S3 bucket is created
    template.hasResourceProperties('AWS::S3::Bucket', {
      BucketName: 'test-app1-sendgrid-log',
      AccessControl: 'Private',
      PublicAccessBlockConfiguration: {
        BlockPublicAcls: true,
        BlockPublicPolicy: true,
        IgnorePublicAcls: true,
        RestrictPublicBuckets: true,
      },
    });

    // Verify Kinesis Firehose delivery stream is created
    template.hasResourceProperties('AWS::KinesisFirehose::DeliveryStream', {
      DeliveryStreamName: 'test-app1-apigateway-sendgrid-stream',
      DeliveryStreamType: 'DirectPut',
      S3DestinationConfiguration: {
        CompressionFormat: 'GZIP',
        Prefix: "sendgrid/dt=!{timestamp:yyyy'-'MM'-'dd}/",
        ErrorOutputPrefix: "kinesis-error/sendgrid/!{firehose:error-output-type}/dt=!{timestamp:yyyy'-'MM'-'dd}/",
      },
    });

    // Verify API Gateway is created
    template.hasResourceProperties('AWS::ApiGateway::RestApi', {
      Name: 'test-app1-apigateway-sendgrid',
    });

    // Verify Athena workgroup is created
    template.hasResourceProperties('AWS::Athena::WorkGroup', {
      Name: 'test-app1-sendgrid-log-workgroup',
      State: 'ENABLED',
    });

    // Verify Glue database is created
    template.hasResourceProperties('AWS::Glue::Database', {
      DatabaseInput: {
        Name: 'test_app1_sendgrid_logs',
        Description: 'Athena database for SendGrid log',
      },
    });

    // Verify Glue table is created
    template.hasResourceProperties('AWS::Glue::Table', {
      DatabaseName: 'test_app1_sendgrid_logs',
      TableInput: {
        Name: 'apigateway_sendgrid',
        Description: 'Athena table for SendGrid log',
        TableType: 'EXTERNAL_TABLE',
      },
    });
  });

  test('Should Handle Multiple SendGrid Log Configurations When Multiple Parameters Are Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
      {
        athenaDateRangeFrom: '2024-02-01',
        suffix: 'app2',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackMultiple', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify multiple S3 buckets are created
    template.hasResourceProperties('AWS::S3::Bucket', {
      BucketName: 'test-app1-sendgrid-log',
    });
    template.hasResourceProperties('AWS::S3::Bucket', {
      BucketName: 'test-app2-sendgrid-log',
    });

    // Verify multiple Firehose streams are created
    template.hasResourceProperties('AWS::KinesisFirehose::DeliveryStream', {
      DeliveryStreamName: 'test-app1-apigateway-sendgrid-stream',
    });
    template.hasResourceProperties('AWS::KinesisFirehose::DeliveryStream', {
      DeliveryStreamName: 'test-app2-apigateway-sendgrid-stream',
    });

    // Verify multiple API Gateways are created
    template.hasResourceProperties('AWS::ApiGateway::RestApi', {
      Name: 'test-app1-apigateway-sendgrid',
    });
    template.hasResourceProperties('AWS::ApiGateway::RestApi', {
      Name: 'test-app2-apigateway-sendgrid',
    });

    // Verify resource counts
    template.resourceCountIs('AWS::S3::Bucket', 2);
    template.resourceCountIs('AWS::KinesisFirehose::DeliveryStream', 2);
    template.resourceCountIs('AWS::ApiGateway::RestApi', 2);
    template.resourceCountIs('AWS::Athena::WorkGroup', 2);
    template.resourceCountIs('AWS::Glue::Database', 2);
    template.resourceCountIs('AWS::Glue::Table', 2);
  });

  test('Should Use Existing S3 Bucket When S3BucketArn Is Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        s3BucketArn: 'arn:aws:s3:::existing-bucket',
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackExisting', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify no new S3 bucket is created when existing bucket ARN is provided
    template.resourceCountIs('AWS::S3::Bucket', 0);

    // Verify Firehose stream still created with existing bucket
    template.hasResourceProperties('AWS::KinesisFirehose::DeliveryStream', {
      S3DestinationConfiguration: {
        BucketARN: 'arn:aws:s3:::existing-bucket',
      },
    });
  });

  test('Should Apply Custom Removal Policy And Lifecycle Rules When Custom Policies Are Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    const lifecycleRules: s3.LifecycleRule[] = [
      {
        id: 'DeleteOldObjects',
        expiration: cdk.Duration.days(90),
        enabled: true,
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackPolicy', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      logRemovalPolicyParam: {
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        autoDeleteObjects: true,
      },
      lifecycleRules: lifecycleRules,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify S3 bucket has lifecycle configuration
    template.hasResourceProperties('AWS::S3::Bucket', {
      LifecycleConfiguration: {
        Rules: [
          {
            Id: 'DeleteOldObjects',
            Status: 'Enabled',
            ExpirationInDays: 90,
          },
        ],
      },
    });
  });

  test('Should Create Proper IAM Roles And Policies When SendGrid Configuration Is Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackIAM', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Firehose IAM role is created
    template.hasResourceProperties('AWS::IAM::Role', {
      RoleName: 'test-app1-firehose-role',
      AssumeRolePolicyDocument: {
        Statement: [
          {
            Effect: 'Allow',
            Principal: {
              Service: 'firehose.amazonaws.com',
            },
            Action: 'sts:AssumeRole',
          },
        ],
      },
    });

    // Verify API Gateway IAM role is created
    template.hasResourceProperties('AWS::IAM::Role', {
      RoleName: 'test-app1-apigateway-to-firehose-role',
      AssumeRolePolicyDocument: {
        Statement: [
          {
            Effect: 'Allow',
            Principal: {
              Service: 'apigateway.amazonaws.com',
            },
            Action: 'sts:AssumeRole',
          },
        ],
      },
    });

    // Verify CloudWatch role for API Gateway
    template.hasResourceProperties('AWS::IAM::Role', {
      RoleName: 'test-app1-apigateway-to-cloudwatch-log-role',
    });
  });

  test('Should Configure API Gateway Method With Proper Integration When SendGrid Configuration Is Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackAPI', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify API Gateway method is created
    template.hasResourceProperties('AWS::ApiGateway::Method', {
      HttpMethod: 'POST',
      Integration: {
        Type: 'AWS',
        IntegrationHttpMethod: 'POST',
        Uri: {
          'Fn::Join': Match.anyValue(),
        },
        RequestTemplates: {
          'application/json': Match.stringLikeRegexp('.*DeliveryStreamName.*'),
        },
        IntegrationResponses: [
          {
            StatusCode: '200',
            ResponseTemplates: {
              'application/json': '{"status": "OK"}',
            },
          },
          {
            StatusCode: '400',
            ResponseTemplates: {
              'application/json': '{"status": "Client Error"}',
            },
          },
          {
            StatusCode: '500',
            ResponseTemplates: {
              'application/json': '{"status": "Server Error"}',
            },
          },
        ],
      },
      MethodResponses: [
        {
          StatusCode: '200',
        },
        {
          StatusCode: '400',
        },
        {
          StatusCode: '500',
        },
      ],
    });
  });

  test('Should Configure Athena Table With Proper Partitioning When SendGrid Configuration Is Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackAthena', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Athena table has proper partitioning configuration
    template.hasResourceProperties('AWS::Glue::Table', {
      TableInput: {
        PartitionKeys: [
          {
            Name: 'date',
            Type: 'string',
          },
        ],
        Parameters: {
          'projection.date.format': 'yyyy-MM-dd',
          'projection.date.interval': '1',
          'projection.date.interval.unit': 'DAYS',
          'projection.date.range': '2024-01-01,NOW',
          'projection.date.type': 'date',
          'projection.enabled': 'true',
          classification: 'json',
          compressionType: 'none',
          typeOfData: 'file',
        },
        StorageDescriptor: {
          Columns: [
            { Name: 'email', Type: 'string' },
            { Name: 'event', Type: 'string' },
            { Name: 'send_at', Type: 'bigint' },
            { Name: 'sg_event_id', Type: 'string' },
            { Name: 'sg_message_id', Type: 'string' },
            { Name: 'smtp_id', Type: 'string' },
            { Name: 'timestamp', Type: 'bigint' },
            { Name: 'ip', Type: 'string' },
            { Name: 'response', Type: 'string' },
            { Name: 'tls', Type: 'int' },
          ],
          SerdeInfo: {
            SerializationLibrary: 'org.openx.data.jsonserde.JsonSerDe',
            Parameters: {
              'ignore.malformed.json': 'true',
            },
          },
        },
      },
    });
  });

  test('Should Create Empty Stack When No SendGridLogParams Are Provided', () => {
    // Arrange & Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackEmpty', {
      pjPrefix: 'test',
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify no resources are created when no params provided
    template.resourceCountIs('AWS::S3::Bucket', 0);
    template.resourceCountIs('AWS::KinesisFirehose::DeliveryStream', 0);
    template.resourceCountIs('AWS::ApiGateway::RestApi', 0);
    template.resourceCountIs('AWS::Athena::WorkGroup', 0);
    template.resourceCountIs('AWS::Glue::Database', 0);
    template.resourceCountIs('AWS::Glue::Table', 0);
  });

  test('Should Handle Custom Athena Date Range When Custom Date Range Is Provided', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2023-06-15',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackCustomDate', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Athena table uses custom date range
    template.hasResourceProperties('AWS::Glue::Table', {
      TableInput: {
        Parameters: {
          'projection.date.range': '2023-06-15,NOW',
        },
      },
    });
  });

  test('Should Work With Cross-Region References When Cross-Region References Are Enabled', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'app1',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackCrossRegion', {
      pjPrefix: 'test',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
      crossRegionReferences: true,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify stack is created successfully with cross-region references
    expect(stack.stackName).toBe('TestSendGridLogStackCrossRegion');

    // Verify resources are still created properly
    template.resourceCountIs('AWS::S3::Bucket', 1);
    template.resourceCountIs('AWS::KinesisFirehose::DeliveryStream', 1);
    template.resourceCountIs('AWS::ApiGateway::RestApi', 1);
  });

  test('Should Have Proper Resource Naming Consistency When Stack Is Created', () => {
    // Arrange
    const sendGridLogParams: ISendGridLogParam[] = [
      {
        athenaDateRangeFrom: '2024-01-01',
        suffix: 'test',
      },
    ];

    // Act
    const stack = new SendGridLogStack(app, 'TestSendGridLogStackNaming', {
      pjPrefix: 'proj',
      sendGridLogParams: sendGridLogParams,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify consistent naming across resources
    template.hasResourceProperties('AWS::S3::Bucket', {
      BucketName: 'proj-test-sendgrid-log',
    });

    template.hasResourceProperties('AWS::KinesisFirehose::DeliveryStream', {
      DeliveryStreamName: 'proj-test-apigateway-sendgrid-stream',
    });

    template.hasResourceProperties('AWS::ApiGateway::RestApi', {
      Name: 'proj-test-apigateway-sendgrid',
    });

    template.hasResourceProperties('AWS::Athena::WorkGroup', {
      Name: 'proj-test-sendgrid-log-workgroup',
    });

    template.hasResourceProperties('AWS::Glue::Database', {
      DatabaseInput: {
        Name: 'proj_test_sendgrid_logs',
      },
    });

    template.hasResourceProperties('AWS::IAM::Role', {
      RoleName: 'proj-test-firehose-role',
    });

    template.hasResourceProperties('AWS::IAM::Role', {
      RoleName: 'proj-test-apigateway-to-firehose-role',
    });
  });
});
