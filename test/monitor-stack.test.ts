import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { MonitorStack } from '../lib/stack/monitor-stack';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as cw from 'aws-cdk-lib/aws-cloudwatch';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

let app: cdk.App;
let mockStack: cdk.Stack;
let mockVpc: ec2.Vpc;

beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';
  cdk.Tags.of(app).add(envTagName, envKey);

  // Create shared mock infrastructure once with explicit region
  mockStack = new cdk.Stack(app, 'MockStack', {
    env: getProcEnv(),
    crossRegionReferences: true,
  });

  // Create shared mock VPC using the new ipAddresses syntax
  mockVpc = new ec2.Vpc(mockStack, 'MockVpc', {
    maxAzs: 2,
    ipAddresses: ec2.IpAddresses.cidr('10.0.0.0/16'),
  });
});

describe(`${pjPrefix} Monitor Stack`, () => {
  let monitorStack: MonitorStack;
  let mockAlb: elbv2.ApplicationLoadBalancer;
  let mockAlarms: cw.Alarm[];

  beforeEach(() => {
    // Create mock ALB using shared VPC
    mockAlb = new elbv2.ApplicationLoadBalancer(mockStack, 'MockAlb', {
      vpc: mockVpc,
      internetFacing: true,
    });

    // Create mock target group for ALB - simplified
    const mockTargetGroup = new elbv2.ApplicationTargetGroup(mockStack, 'MockTargetGroup', {
      vpc: mockVpc,
      port: 80,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.IP,
    });

    // Create simplified mock alarms
    mockAlarms = [
      new cw.Alarm(mockStack, 'MockAlarm1', {
        metric: new cw.Metric({
          namespace: 'AWS/ApplicationELB',
          metricName: 'UnHealthyHostCount',
          dimensionsMap: {
            TargetGroup: 'mock-target-group',
            LoadBalancer: 'mock-load-balancer',
          },
        }),
        threshold: 1,
        evaluationPeriods: 2,
      }),
    ];

    // Create the MonitorStack with simplified parameters
    monitorStack = new MonitorStack(app, `${pjPrefix}-Monitor`, {
      pjPrefix: pjPrefix,
      dashboardName: `${pjPrefix}-ECSApp`,
      cfDistributionId: 'E1234567890123',
      albFullName: 'mock-alb-fullname',
      appTargetGroupNames: ['mock-target-group'],
      albTgUnHealthyHostCountAlarms: mockAlarms,
      ecsClusterName: 'test-cluster',
      ecsAlbServiceNames: ['test-alb-service-1'],
      ecsInternalServiceNames: ['test-internal-service-1'],
      ecsTargetUtilizationPercent: 70,
      ecsScaleOnRequestCount: 50,
      dbClusterName: 'test-db-cluster',
      env: getProcEnv(),
    });
  });

  describe('Basic Tests', () => {
    test('Should create CloudWatch Dashboard with correct properties When Monitor Stack is deployed', () => {
      const template = Template.fromStack(monitorStack);

      // Verify that exactly one dashboard is created
      template.resourceCountIs('AWS::CloudWatch::Dashboard', 1);

      // Verify dashboard properties
      template.hasResourceProperties('AWS::CloudWatch::Dashboard', {
        DashboardName: `${pjPrefix}-ECSApp`,
        DashboardBody: Match.anyValue(),
      });
    });

    test('Should inherit from CDK Stack When MonitorStack is instantiated', () => {
      expect(monitorStack).toBeInstanceOf(cdk.Stack);
      expect(monitorStack.stackName).toBe(`${pjPrefix}-Monitor`);
      expect(monitorStack.account).toBe(getProcEnv().account);
      expect(monitorStack.region).toBe(getProcEnv().region);
    });

    // Snapshot test - uncomment this when needed
    test('Should match snapshot When Monitor Stack is created', () => {
      const template = Template.fromStack(monitorStack);
      expect(template.toJSON()).toMatchSnapshot();
    });
  });

  // Dashboard Metrics tests removed - internal AWS behavior not needed to test

  describe('Configuration Scenarios', () => {
    test('Should create dashboard with minimal configuration When only required parameters are provided', () => {
      // Create a minimal mock alarm to avoid the alarmArn issue
      const minimalMockAlarm = new cw.Alarm(mockStack, 'MinimalMockAlarm', {
        metric: new cw.Metric({
          namespace: 'AWS/ApplicationELB',
          metricName: 'UnHealthyHostCount',
          dimensionsMap: {
            TargetGroup: 'minimal-target-group',
            LoadBalancer: 'minimal-load-balancer',
          },
        }),
        threshold: 1,
        evaluationPeriods: 1,
      });

      // Test case with minimal configuration
      const minimalMonitorStack = new MonitorStack(app, `${pjPrefix}-Monitor-Minimal`, {
        pjPrefix: 'Minimal',
        dashboardName: 'Minimal-Dashboard',
        cfDistributionId: 'E1111111111111',
        albFullName: 'minimal-alb',
        appTargetGroupNames: ['minimal-tg'],
        albTgUnHealthyHostCountAlarms: [minimalMockAlarm], // Use the minimal mock alarm
        ecsClusterName: 'minimal-cluster',
        ecsAlbServiceNames: ['minimal-service'],
        ecsInternalServiceNames: [],
        ecsTargetUtilizationPercent: 70,
        ecsScaleOnRequestCount: 50,
        dbClusterName: 'minimal-db',
        env: getProcEnv(),
      });

      const minimalTemplate = Template.fromStack(minimalMonitorStack);
      minimalTemplate.resourceCountIs('AWS::CloudWatch::Dashboard', 1);
      minimalTemplate.hasResourceProperties('AWS::CloudWatch::Dashboard', {
        DashboardName: 'Minimal-Dashboard',
      });
    });

    test('Should create dashboard with multiple services When arrays contain multiple values', () => {
      // Create multiple mock alarms to avoid the alarmArn issue
      const multiServiceMockAlarms = [
        new cw.Alarm(mockStack, 'MultiServiceMockAlarm1', {
          metric: new cw.Metric({
            namespace: 'AWS/ApplicationELB',
            metricName: 'UnHealthyHostCount',
            dimensionsMap: {
              TargetGroup: 'service1-target-group',
              LoadBalancer: 'multi-service-alb',
            },
          }),
          threshold: 1,
          evaluationPeriods: 1,
        }),
        new cw.Alarm(mockStack, 'MultiServiceMockAlarm2', {
          metric: new cw.Metric({
            namespace: 'AWS/ApplicationELB',
            metricName: 'UnHealthyHostCount',
            dimensionsMap: {
              TargetGroup: 'service2-target-group',
              LoadBalancer: 'multi-service-alb',
            },
          }),
          threshold: 2,
          evaluationPeriods: 1,
        }),
        new cw.Alarm(mockStack, 'MultiServiceMockAlarm3', {
          metric: new cw.Metric({
            namespace: 'AWS/ApplicationELB',
            metricName: 'UnHealthyHostCount',
            dimensionsMap: {
              TargetGroup: 'service3-target-group',
              LoadBalancer: 'multi-service-alb',
            },
          }),
          threshold: 3,
          evaluationPeriods: 1,
        }),
      ];

      // Create test stack with multiple services
      const multiServiceMonitorStack = new MonitorStack(app, `${pjPrefix}-Monitor-MultiService`, {
        pjPrefix: pjPrefix,
        dashboardName: `${pjPrefix}-MultiService`,
        cfDistributionId: 'E2222222222222',
        albFullName: 'mock-alb-fullname',
        appTargetGroupNames: ['service1-tg', 'service2-tg', 'service3-tg'],
        albTgUnHealthyHostCountAlarms: multiServiceMockAlarms, // Use the multi service mock alarms
        ecsClusterName: 'multi-service-cluster',
        ecsAlbServiceNames: ['service-1', 'service-2', 'service-3'],
        ecsInternalServiceNames: ['internal-service-1', 'internal-service-2'],
        ecsTargetUtilizationPercent: 80,
        ecsScaleOnRequestCount: 100,
        dbClusterName: 'multi-service-db',
        env: getProcEnv(),
      });

      // Verify dashboard is created with expected properties
      const multiServiceTemplate = Template.fromStack(multiServiceMonitorStack);
      multiServiceTemplate.resourceCountIs('AWS::CloudWatch::Dashboard', 1);
      multiServiceTemplate.hasResourceProperties('AWS::CloudWatch::Dashboard', {
        DashboardName: `${pjPrefix}-MultiService`,
      });
    });

    test('Should create dashboard with empty arrays When no services are configured', () => {
      // Create mock alarm for empty arrays test
      const emptyArraysMockAlarm = new cw.Alarm(mockStack, 'EmptyArraysMockAlarm', {
        metric: new cw.Metric({
          namespace: 'AWS/ApplicationELB',
          metricName: 'UnHealthyHostCount',
          dimensionsMap: {
            TargetGroup: 'empty-arrays-target-group',
            LoadBalancer: 'empty-arrays-load-balancer',
          },
        }),
        threshold: 1,
        evaluationPeriods: 1,
      });

      // Create test stack with minimal setup for the "empty" test
      const emptyArraysMonitorStack = new MonitorStack(app, `${pjPrefix}-Monitor-EmptyArrays`, {
        pjPrefix: pjPrefix,
        dashboardName: `${pjPrefix}-EmptyArrays`,
        cfDistributionId: 'E4444444444444',
        albFullName: 'mock-alb-fullname',
        appTargetGroupNames: ['empty-array-tg'], // Provide at least one target group
        albTgUnHealthyHostCountAlarms: [emptyArraysMockAlarm], // Provide at least one alarm
        ecsClusterName: 'empty-arrays-cluster',
        ecsAlbServiceNames: ['empty-array-service'], // Provide at least one service
        ecsInternalServiceNames: [],
        ecsTargetUtilizationPercent: 70,
        ecsScaleOnRequestCount: 50,
        dbClusterName: 'empty-arrays-db',
        env: getProcEnv(),
      });

      // Verify dashboard is created with expected properties
      const emptyArraysTemplate = Template.fromStack(emptyArraysMonitorStack);
      emptyArraysTemplate.resourceCountIs('AWS::CloudWatch::Dashboard', 1);

      // Verify dashboard name
      emptyArraysTemplate.hasResourceProperties('AWS::CloudWatch::Dashboard', {
        DashboardName: `${pjPrefix}-EmptyArrays`,
      });
    });
  });

  describe('Error Handling', () => {
    test('Should handle empty string inputs When pjPrefix is empty', () => {
      // Create error mock alarm
      const errorMockAlarm = new cw.Alarm(mockStack, 'ErrorMockAlarm', {
        metric: new cw.Metric({
          namespace: 'AWS/ApplicationELB',
          metricName: 'UnHealthyHostCount',
          dimensionsMap: {
            TargetGroup: 'error-target-group',
            LoadBalancer: 'error-load-balancer',
          },
        }),
        threshold: 1,
        evaluationPeriods: 1,
      });

      expect(() => {
        new MonitorStack(app, `${pjPrefix}-Monitor-Error`, {
          pjPrefix: '',
          dashboardName: `${pjPrefix}-Error`,
          cfDistributionId: 'E5555555555555',
          albFullName: 'mock-alb-fullname',
          appTargetGroupNames: ['mock-tg'],
          albTgUnHealthyHostCountAlarms: [errorMockAlarm],
          ecsClusterName: 'error-cluster',
          ecsAlbServiceNames: ['error-service'],
          ecsInternalServiceNames: [],
          ecsTargetUtilizationPercent: 70,
          ecsScaleOnRequestCount: 50,
          dbClusterName: 'error-db',
          env: getProcEnv(),
        });
      }).not.toThrow(); // CDK doesn't validate empty strings at construct time
    });

    test('Should handle negative scaling values When invalid ECS scaling parameters are provided', () => {
      // Create negative values mock alarm
      const negativeValuesMockAlarm = new cw.Alarm(mockStack, 'NegativeValuesMockAlarm', {
        metric: new cw.Metric({
          namespace: 'AWS/ApplicationELB',
          metricName: 'UnHealthyHostCount',
          dimensionsMap: {
            TargetGroup: 'negative-values-target-group',
            LoadBalancer: 'negative-values-load-balancer',
          },
        }),
        threshold: 1,
        evaluationPeriods: 1,
      });

      expect(() => {
        new MonitorStack(app, `${pjPrefix}-Monitor-NegativeValues`, {
          pjPrefix: pjPrefix,
          dashboardName: `${pjPrefix}-NegativeValues`,
          cfDistributionId: 'E6666666666666',
          albFullName: 'mock-alb-fullname',
          appTargetGroupNames: ['mock-tg'],
          albTgUnHealthyHostCountAlarms: [negativeValuesMockAlarm],
          ecsClusterName: 'negative-values-cluster',
          ecsAlbServiceNames: ['negative-values-service'],
          ecsInternalServiceNames: [],
          ecsTargetUtilizationPercent: -10,
          ecsScaleOnRequestCount: -50,
          dbClusterName: 'negative-values-db',
          env: getProcEnv(),
        });
      }).not.toThrow(); // CDK doesn't validate negative values at construct time
    });
  });

  describe('Resource Types', () => {
    test('Should create only CloudWatch Dashboard resources with no extra resources', () => {
      const template = Template.fromStack(monitorStack);

      // Verify no other resources are created - this check is unique and not duplicated elsewhere
      const resources = template.toJSON().Resources;
      const resourceTypes = Object.values(resources).map((resource: any) => resource.Type);
      const uniqueResourceTypes = [...new Set(resourceTypes)];

      expect(uniqueResourceTypes).toEqual(['AWS::CloudWatch::Dashboard']);
    });
  });

  // Dashboard Structure tests removed - internal AWS behavior not needed to test
});
