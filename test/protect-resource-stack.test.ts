import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { ProtectResourceStack } from '../lib/stack/protect-resource-stack';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

describe('The protect resource stack', () => {
  let app: cdk.App;

  beforeEach(() => {
    app = new cdk.App();
  });

  // ✅ 1. Assert Snapshot
  test('Should Match Expected Snapshot When Protect Resource Stack Is Created With Basic Configuration', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
      DatabaseStack: false,
      BackupStack: true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackSnapshot', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  // ✅ 2. Assert Number of Resources
  test('Should Create Correct Number Of Resources When Basic Configuration Is Provided', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
      DatabaseStack: false,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackResourceCount', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify expected resource counts
    template.resourceCountIs('AWS::Lambda::Function', 3); // onEvent + provider framework
    template.resourceCountIs('AWS::IAM::Role', 3); // Lambda role + provider roles
    template.resourceCountIs('AWS::IAM::Policy', 3);
    template.resourceCountIs('AWS::CloudFormation::CustomResource', 1);
  });

  // ✅ 3. Assert Correct Policy & ✅ 4. Assert Resource Properties
  test('Should Create Protect Resource Infrastructure With Correct Properties When Basic Configuration Is Provided', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
      DatabaseStack: false,
      BackupStack: true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStack', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Lambda function is created
    template.hasResourceProperties('AWS::Lambda::Function', {
      Runtime: 'python3.12',
      Handler: 'stackPolicy.on_event',
      Timeout: 900,
      MemorySize: 256,
    });

    // Verify IAM role for Lambda is created
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: {
        Statement: [
          {
            Effect: 'Allow',
            Principal: {
              Service: 'lambda.amazonaws.com',
            },
            Action: 'sts:AssumeRole',
          },
        ],
      },
      ManagedPolicyArns: [
        {
          'Fn::Join': [
            '',
            ['arn:', { Ref: 'AWS::Partition' }, ':iam::aws:policy/service-role/AWSLambdaBasicExecutionRole'],
          ],
        },
      ],
    });

    // Verify custom resource is created
    template.hasResourceProperties('AWS::CloudFormation::CustomResource', {
      StackEnableProtection: 'VpcStack,BackupStack',
      StackDisableProtection: 'DatabaseStack',
    });
  });

  test('Should Handle All Stacks Enabled Protection When All Stacks Are Set To True', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
      DatabaseStack: true,
      BackupStack: true,
      EcsStack: true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackAllEnabled', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify custom resource has all stacks enabled
    template.hasResourceProperties('AWS::CloudFormation::CustomResource', {
      StackEnableProtection: 'VpcStack,DatabaseStack,BackupStack,EcsStack',
      StackDisableProtection: '',
    });
  });

  test('Should Handle All Stacks Disabled Protection When All Stacks Are Set To False', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: false,
      DatabaseStack: false,
      BackupStack: false,
      EcsStack: false,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackAllDisabled', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify custom resource has all stacks disabled
    template.hasResourceProperties('AWS::CloudFormation::CustomResource', {
      StackEnableProtection: '',
      StackDisableProtection: 'VpcStack,DatabaseStack,BackupStack,EcsStack',
    });
  });

  test('Should Handle Empty Stack Protection Configuration When No Stacks Are Protected', () => {
    // Arrange
    const stackResourceProtection = {};

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackEmpty', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify custom resource has empty protection lists
    template.hasResourceProperties('AWS::CloudFormation::CustomResource', {
      StackEnableProtection: '',
      StackDisableProtection: '',
    });
  });

  test('Should Create CloudFormation SetStackPolicy IAM Policy When Stack Protection Is Enabled', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
      DatabaseStack: false,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackPolicy', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify IAM policy for SetStackPolicy is created
    template.hasResourceProperties('AWS::IAM::Policy', {
      PolicyDocument: {
        Statement: [
          {
            Effect: 'Allow',
            Action: 'cloudformation:SetStackPolicy',
            Resource: '*',
          },
        ],
      },
    });
  });

  test('Should Create Custom Resource Provider When Stack Protection Is Enabled', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackProvider', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify custom resource provider is created
    template.hasResourceProperties('AWS::CloudFormation::CustomResource', {
      ServiceToken: {
        'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*Provider.*'), 'Arn']),
      },
    });

    // Verify provider Lambda function exists
    template.resourceCountIs('AWS::Lambda::Function', 3); // onEvent + provider framework
  });

  test('Should Handle Single Stack Protection When Only One Stack Is Protected', () => {
    // Arrange
    const stackResourceProtection = {
      CriticalStack: true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackSingle', {
      pjPrefix: 'prod',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify custom resource with single stack protection
    template.hasResourceProperties('AWS::CloudFormation::CustomResource', {
      StackEnableProtection: 'CriticalStack',
      StackDisableProtection: '',
    });
  });

  test('Should Handle Mixed Boolean Values When Some Stacks Are Protected And Others Are Not', () => {
    // Arrange
    const stackResourceProtection = {
      ProductionVpc: true,
      TestDatabase: false,
      StagingEcs: true,
      DevBackup: false,
      MonitoringStack: true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackMixed', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify proper separation of enabled and disabled stacks
    template.hasResourceProperties('AWS::CloudFormation::CustomResource', {
      StackEnableProtection: 'ProductionVpc,StagingEcs,MonitoringStack',
      StackDisableProtection: 'TestDatabase,DevBackup',
    });
  });

  test('Should Create Lambda Function With Correct Configuration When Stack Protection Is Enabled', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackLambda', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify Lambda function configuration
    template.hasResourceProperties('AWS::Lambda::Function', {
      Runtime: 'python3.12',
      Handler: 'stackPolicy.on_event',
      Code: {
        S3Bucket: Match.anyValue(),
        S3Key: Match.anyValue(),
      },
      Timeout: 900,
      MemorySize: 256,
      Role: {
        'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*CustomResourceRole.*'), 'Arn']),
      },
    });
  });

  test('Should Attach IAM Policy To Lambda Role When Stack Protection Is Enabled', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackRolePolicy', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify IAM role has both managed policy and inline policy
    template.hasResourceProperties('AWS::IAM::Role', {
      ManagedPolicyArns: [
        {
          'Fn::Join': Match.arrayWith([
            '',
            Match.arrayWith([
              'arn:',
              { Ref: 'AWS::Partition' },
              ':iam::aws:policy/service-role/AWSLambdaBasicExecutionRole',
            ]),
          ]),
        },
      ],
    });

    // Verify SetStackPolicy inline policy exists
    template.hasResourceProperties('AWS::IAM::Policy', {
      PolicyDocument: {
        Statement: [
          {
            Action: 'cloudformation:SetStackPolicy',
            Effect: 'Allow',
            Resource: '*',
          },
        ],
      },
      Roles: [
        {
          Ref: Match.stringLikeRegexp('.*CustomResourceRole.*'),
        },
      ],
    });
  });

  test('Should Work With Cross-Region References When Cross-Region References Are Enabled', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
      DatabaseStack: false,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackCrossRegion', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
      crossRegionReferences: true,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify stack is created successfully with cross-region references
    expect(stack.stackName).toBe('TestProtectResourceStackCrossRegion');

    // Verify resources are still created properly
    template.resourceCountIs('AWS::Lambda::Function', 3);
    template.resourceCountIs('AWS::CloudFormation::CustomResource', 1);
    template.resourceCountIs('AWS::IAM::Role', 3); // Lambda role + provider roles
    template.resourceCountIs('AWS::IAM::Policy', 3);
  });

  test('Should Handle Special Characters In Stack Names When Special Characters Are Used', () => {
    // Arrange
    const stackResourceProtection = {
      'Vpc-Stack-Prod': true,
      Database_Stack_Test: false,
      'ECS.Stack.Dev': true,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackSpecialChars', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify custom resource handles special characters in stack names
    template.hasResourceProperties('AWS::CloudFormation::CustomResource', {
      StackEnableProtection: 'Vpc-Stack-Prod,ECS.Stack.Dev',
      StackDisableProtection: 'Database_Stack_Test',
    });
  });

  test('Should Have Proper Resource Counts When Stack Protection Is Enabled', () => {
    // Arrange
    const stackResourceProtection = {
      VpcStack: true,
      DatabaseStack: false,
    };

    // Act
    const stack = new ProtectResourceStack(app, 'TestProtectResourceStackCounts', {
      pjPrefix: 'test',
      stackResourceProtection: stackResourceProtection,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify expected resource counts
    template.resourceCountIs('AWS::Lambda::Function', 3); // onEvent + provider framework
    template.resourceCountIs('AWS::IAM::Role', 3); // Lambda role + provider roles
    template.resourceCountIs('AWS::IAM::Policy', 3);
    template.resourceCountIs('AWS::CloudFormation::CustomResource', 1);
  });
});
