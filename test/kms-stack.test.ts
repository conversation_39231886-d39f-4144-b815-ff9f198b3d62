import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { KMSStack } from '../lib/stack/kms-stack';
import { IRemovalPolicyParam, IConfig } from '../params/interface';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

describe('The KMS stack', () => {
  let app: cdk.App;

  beforeEach(() => {
    app = new cdk.App();
  });

  // Assert Snapshot
  test('Should Match Expected Snapshot When KMS Stack Is Created With Basic Configuration', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackSnapshot', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  // Assert Number of Resources
  test('Should Create Correct Number Of Resources When Basic Configuration Is Provided', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackResourceCount', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify exact resource counts
    template.resourceCountIs('AWS::KMS::Key', 1);
    template.resourceCountIs('AWS::KMS::Alias', 1);
  });

  // Assert Correct Policy & Assert Resource Properties
  test('Should Create KMS Key With Correct Configuration When Basic Setup Is Provided', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStack', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify KMS key is created
    template.hasResourceProperties('AWS::KMS::Key', {
      Description: 'Custom KMS key',
      EnableKeyRotation: true,
      KeyPolicy: {
        Statement: [
          {
            Effect: 'Allow',
            Principal: {
              AWS: {
                'Fn::Join': ['', ['arn:', { Ref: 'AWS::Partition' }, ':iam::123456789012:root']],
              },
            },
            Action: 'kms:*',
            Resource: '*',
          },
        ],
        Version: '2012-10-17',
      },
    });

    // Verify KMS key resource count
    template.resourceCountIs('AWS::KMS::Key', 1);
  });

  test('Should Create KMS Alias With Correct Naming When Stack Is Created', () => {
    // Arrange & Act
    const stackId = 'TestKMSStackAlias';
    const stack = new KMSStack(app, stackId, {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify KMS alias is created with correct name
    template.hasResourceProperties('AWS::KMS::Alias', {
      AliasName: `alias/${stackId}-for-app`,
      TargetKeyId: {
        'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*Key.*'), 'Arn']),
      },
    });

    // Verify KMS alias resource count
    template.resourceCountIs('AWS::KMS::Alias', 1);
  });

  test('Should Expose KmsKey As Public Property When Stack Is Created', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackProperty', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    expect(stack.kmsKey).toBeDefined();
    expect(stack.kmsKey.keyId).toBeDefined();
    expect(stack.kmsKey.keyArn).toBeDefined();
  });

  test('Should Use Custom Removal Policy When Custom Removal Policy Is Provided', () => {
    // Arrange
    const customRemovalPolicy: IRemovalPolicyParam = {
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: true,
      emptyOnDelete: true,
    };

    // Act
    const stack = new KMSStack(app, 'TestKMSStackRemovalPolicy', {
      pjPrefix,
      removalPolicyParam: customRemovalPolicy,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify KMS key has correct deletion policy
    template.hasResource('AWS::KMS::Key', {
      DeletionPolicy: 'Delete',
    });
  });

  test('Should Use Default RETAIN Removal Policy When No Custom Removal Policy Is Provided', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackDefaultPolicy', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify KMS key has default RETAIN deletion policy
    template.hasResource('AWS::KMS::Key', {
      DeletionPolicy: 'Retain',
    });
  });

  test('Should Use Custom KMS Pending Window When Custom Pending Window Is Provided', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackPendingWindow', {
      pjPrefix,
      kmsPendingWindow: cdk.Duration.days(30),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify KMS key has custom pending window
    template.hasResourceProperties('AWS::KMS::Key', {
      PendingWindowInDays: 30,
    });
  });

  test('Should Use Default Pending Window When No Custom Pending Window Is Provided', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackDefaultWindow', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify KMS key has default 7-day pending window
    template.hasResourceProperties('AWS::KMS::Key', {
      PendingWindowInDays: 7,
    });
  });

  test('Should Enable Key Rotation By Default When Stack Is Created', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackRotation', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify key rotation is enabled
    template.hasResourceProperties('AWS::KMS::Key', {
      EnableKeyRotation: true,
    });
  });

  test('Should Work With Different Stack Names When Multiple Stacks Are Created', () => {
    // Arrange & Act
    const stack1 = new KMSStack(app, 'KMSStack1', {
      pjPrefix,
      env: getProcEnv(),
    });

    const stack2 = new KMSStack(app, 'KMSStack2', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    const template1 = Template.fromStack(stack1);
    const template2 = Template.fromStack(stack2);

    // Verify both stacks have KMS keys
    template1.resourceCountIs('AWS::KMS::Key', 1);
    template2.resourceCountIs('AWS::KMS::Key', 1);

    // Verify different aliases
    template1.hasResourceProperties('AWS::KMS::Alias', {
      AliasName: 'alias/KMSStack1-for-app',
    });
    template2.hasResourceProperties('AWS::KMS::Alias', {
      AliasName: 'alias/KMSStack2-for-app',
    });
  });

  test('Should Work With Cross-Region References When Cross-Region References Are Enabled', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackCrossRegion', {
      pjPrefix,
      env: getProcEnv(),
      crossRegionReferences: true,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify KMS key is still created with cross-region references enabled
    template.hasResourceProperties('AWS::KMS::Key', {
      Description: 'Custom KMS key',
      EnableKeyRotation: true,
    });

    // Verify stack is created successfully
    expect(stack.stackName).toBe('TestKMSStackCrossRegion');
  });

  test('Should Be Accessible For Other Constructs When KmsKey Property Is Used', () => {
    // Arrange & Act
    const stack = new KMSStack(app, 'TestKMSStackAccess', {
      pjPrefix,
      env: getProcEnv(),
    });

    // Assert
    // Verify the kmsKey can be used by other constructs
    expect(stack.kmsKey).toBeDefined();
    expect(typeof stack.kmsKey.keyId).toBe('string');
    expect(typeof stack.kmsKey.keyArn).toBe('string');

    // Verify key has basic CDK construct properties
    expect(stack.kmsKey.env).toBeDefined();
    expect(stack.kmsKey.node).toBeDefined();

    // Verify key has KMS-specific methods
    expect(stack.kmsKey.grant).toBeDefined();
    expect(stack.kmsKey.grantDecrypt).toBeDefined();
    expect(stack.kmsKey.grantEncrypt).toBeDefined();
  });

  test('Should Use Both Custom Removal Policy And Pending Window When Both Are Provided', () => {
    // Arrange
    const customRemovalPolicy: IRemovalPolicyParam = {
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: true,
      emptyOnDelete: true,
    };

    // Act
    const stack = new KMSStack(app, 'TestKMSStackBothCustom', {
      pjPrefix,
      removalPolicyParam: customRemovalPolicy,
      kmsPendingWindow: cdk.Duration.days(14),
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify both custom configurations are applied
    template.hasResource('AWS::KMS::Key', {
      DeletionPolicy: 'Delete',
    });

    template.hasResourceProperties('AWS::KMS::Key', {
      PendingWindowInDays: 14,
      EnableKeyRotation: true,
      Description: 'Custom KMS key',
    });
  });
});
