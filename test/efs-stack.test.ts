import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as events from 'aws-cdk-lib/aws-events';
import { EfsStack } from '../lib/stack/efs-stack';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

describe('The EFS stack', () => {
  let app: cdk.App;
  let helperStack: cdk.Stack;
  let vpc: ec2.Vpc;
  let appKey: kms.IKey;
  let appServerSecurityGroup: ec2.SecurityGroup;
  let bastionSecurityGroup: ec2.SecurityGroup;

  beforeEach(() => {
    app = new cdk.App();

    // Create helper stack for mock resources
    helperStack = new cdk.Stack(app, 'HelperStack', { env: getProcEnv() });

    // Create mock VPC with ipAddresses instead of cidr (deprecated)
    vpc = new ec2.Vpc(helperStack, 'TestVpc', {
      maxAzs: 2,
      ipAddresses: ec2.IpAddresses.cidr('10.0.0.0/16'),
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'Public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'Private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 24,
          name: 'Protected',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
      ],
    });

    // Create mock KMS key
    appKey = new kms.Key(helperStack, 'TestAppKey', {
      description: 'Test application key',
    });

    // Create mock security groups
    appServerSecurityGroup = new ec2.SecurityGroup(helperStack, 'TestAppServerSG', {
      vpc: vpc,
      description: 'Test App Server Security Group',
    });

    bastionSecurityGroup = new ec2.SecurityGroup(helperStack, 'TestBastionSG', {
      vpc: vpc,
      description: 'Test Bastion Security Group',
    });
  });

  // Assert Snapshot
  test('Should Match Expected Snapshot When EFS Stack Is Created With Basic Configuration', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackSnapshot', {
      pjPrefix,
      vpc,
      appKey,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  // Assert Number of Resources
  test('Should Create Correct Number Of Resources When Basic Configuration Is Provided', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackResourceCount', {
      pjPrefix,
      vpc,
      appKey,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify exact resource counts
    template.resourceCountIs('AWS::EFS::FileSystem', 1);
    template.resourceCountIs('AWS::EC2::SecurityGroup', 1);
    template.resourceCountIs('AWS::EFS::MountTarget', 2); // One per AZ
  });

  // Assert Resource Properties
  test('Should Create EFS FileSystem With Correct Properties When Basic Configuration Is Provided', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackProperties', {
      pjPrefix,
      vpc,
      appKey,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify EFS file system properties
    template.hasResourceProperties('AWS::EFS::FileSystem', {
      FileSystemTags: Match.arrayWith([
        Match.objectLike({
          Key: 'Name',
          Value: `${pjPrefix}-efs`,
        }),
      ]),
      Encrypted: true,
      ThroughputMode: 'bursting',
      PerformanceMode: 'generalPurpose',
      KmsKeyId: Match.anyValue(),
    });

    // Verify security group for EFS is created with correct description
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      GroupDescription: 'EFS Security Group',
      VpcId: Match.anyValue(),
    });

    // Verify mount target properties
    template.hasResourceProperties('AWS::EFS::MountTarget', {
      FileSystemId: {
        Ref: Match.stringLikeRegexp('.*EfsFileSystem.*'),
      },
      SecurityGroups: Match.anyValue(),
      SubnetId: Match.anyValue(),
    });
  });

  // Assert Correct Policy
  test('Should Create Correct Security Group Policy When App Server And Bastion Security Groups Are Provided', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackPolicy', {
      pjPrefix,
      vpc,
      appKey,
      appServerSecurityGroup,
      bastionSecurityGroup,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.MAX_IO,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify security group ingress rules for NFS access
    template.hasResourceProperties('AWS::EC2::SecurityGroupIngress', {
      IpProtocol: 'tcp',
      FromPort: 2049,
      ToPort: 2049,
      SourceSecurityGroupId: Match.anyValue(),
    });

    // Verify we have exactly 2 ingress rules (one for app server, one for bastion)
    template.resourceCountIs('AWS::EC2::SecurityGroupIngress', 2);

    // Verify file system properties
    template.hasResourceProperties('AWS::EFS::FileSystem', {
      ThroughputMode: 'bursting',
      PerformanceMode: 'maxIO',
    });
  });

  test('Should Create Lifecycle Policies When Lifecycle Configuration Is Provided', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackWithLifecycle', {
      pjPrefix,
      vpc,
      appKey,
      lifecyclePolicy: efs.LifecyclePolicy.AFTER_30_DAYS,
      outOfInfrequentAccessPolicy: efs.OutOfInfrequentAccessPolicy.AFTER_1_ACCESS,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify lifecycle policies are created correctly (CDK creates 2 separate objects)
    template.hasResourceProperties('AWS::EFS::FileSystem', {
      LifecyclePolicies: Match.arrayWith([
        {
          TransitionToIA: 'AFTER_30_DAYS',
        },
        {
          TransitionToPrimaryStorageClass: 'AFTER_1_ACCESS',
        },
      ]),
    });
  });

  test('Should Create Backup Resources When Backup Is Enabled', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackWithBackup', {
      pjPrefix,
      vpc,
      appKey,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      hasBackup: true,
      backupParams: {
        schedule: events.Schedule.cron({ minute: '0', hour: '2' }),
        retentionPeriod: cdk.Duration.days(30),
        removalPolicy: cdk.RemovalPolicy.DESTROY,
      },
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup vault is created
    template.hasResourceProperties('AWS::Backup::BackupVault', {
      BackupVaultName: `${pjPrefix}-efs-backup-vault`,
    });

    // Verify backup plan is created
    template.hasResourceProperties('AWS::Backup::BackupPlan', {
      BackupPlan: {
        BackupPlanName: `${pjPrefix}-efs-backup-plan`,
      },
    });

    // Verify backup selection is created with correct pattern
    template.hasResourceProperties('AWS::Backup::BackupSelection', {
      BackupPlanId: {
        'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*EfsBackupPlan.*'), 'BackupPlanId']),
      },
    });
  });

  test('Should Create Shared Transfer Family Configuration When Transfer Family Is Enabled', () => {
    // Arrange & Act
    const sharedAccountId = '************';
    const stack = new EfsStack(app, 'TestEfsStackWithTransferFamily', {
      pjPrefix,
      vpc,
      appKey,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      isUseSharedTransferFamily: true,
      sharedTransferFamilyAccountID: sharedAccountId,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify that stack is created successfully and has EFS file system
    template.hasResourceProperties('AWS::EFS::FileSystem', {
      FileSystemTags: [
        {
          Key: 'Name',
          Value: `${pjPrefix}-efs`,
        },
      ],
      Encrypted: true,
    });

    // Verify stack instance properties
    expect(stack.stackName).toContain('TestEfsStackWithTransferFamily');
  });

  test('Should Use Custom Removal Policy When Removal Policy Is Specified', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackWithRemovalPolicy', {
      pjPrefix,
      vpc,
      appKey,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify file system has correct deletion policy
    template.hasResource('AWS::EFS::FileSystem', {
      DeletionPolicy: 'Delete',
    });
  });

  test('Should Create Correct Outputs When Stack Is Created', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackOutputs', {
      pjPrefix,
      vpc,
      appKey,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify output is created with correct pattern
    template.hasOutput('*', {
      Export: {
        Name: 'FileSystemId',
      },
    });
  });

  test('Should Not Create Security Group Ingress Rules When No External Security Groups Are Provided', () => {
    // Arrange & Act
    const stack = new EfsStack(app, 'TestEfsStackNoSG', {
      pjPrefix,
      vpc,
      appKey,
      throughputMode: efs.ThroughputMode.BURSTING,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // EFS security group should still be created
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      GroupDescription: 'EFS Security Group',
    });

    // No security group ingress rules for app/bastion
    template.resourceCountIs('AWS::EC2::SecurityGroupIngress', 0);
  });
});
