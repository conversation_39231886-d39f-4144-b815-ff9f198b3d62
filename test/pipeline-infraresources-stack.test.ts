import * as cdk from 'aws-cdk-lib';
import { Match, Template } from 'aws-cdk-lib/assertions';
import { InfraResourcesPipelineStack } from '../lib/stack/pipeline-infraresources-stack';
import { IConfig } from '../params/interface';
import * as codebuild from 'aws-cdk-lib/aws-codebuild';
import * as kms from 'aws-cdk-lib/aws-kms';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

let mockAppKey: kms.IKey;
let app: cdk.App;
beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';
  cdk.Tags.of(app).add(envTagName, envKey);

  // Create a mock KMS key
  mockAppKey = kms.Key.fromKeyArn(
    new cdk.Stack(app, 'MockKeyStack'),
    'MockAppKey',
    'arn:aws:kms:ap-northeast-1:123456789012:key/mock-key-id',
  );
});

describe(`${pjPrefix} InfraResourcesPipeline Stack`, () => {
  test('should match snapshot when stack is created', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-InfraResourcesPipeline`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
      env: getProcEnv(),
    });

    const template = Template.fromStack(infraResourcesPipeline);
    expect(template).toMatchSnapshot();
  });

  test('should create source bucket with versioning enabled when stack is initialized', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-InfraResourcesPipeline-Bucket`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
      env: getProcEnv(),
    });

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResourceProperties('AWS::S3::Bucket', {
      VersioningConfiguration: {
        Status: 'Enabled',
      },
    });
  });

  test('should create CodePipeline with Source stage when stack is initialized', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-InfraResourcesPipeline-Source`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
      env: getProcEnv(),
    });

    const template = Template.fromStack(infraResourcesPipeline);
    template.resourceCountIs('AWS::CodePipeline::Pipeline', 1);

    template.hasResourceProperties('AWS::CodePipeline::Pipeline', {
      Stages: Match.arrayWith([
        Match.objectLike({
          Name: 'Source',
          Actions: Match.arrayWith([
            Match.objectLike({
              ActionTypeId: Match.objectLike({
                Category: 'Source',
                Provider: 'S3',
              }),
              Configuration: Match.objectLike({
                S3ObjectKey: 'image.zip',
              }),
            }),
          ]),
        }),
      ]),
    });
  });

  test('should create CodeBuild project with correct timeout when stack is initialized', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-InfraResourcesPipeline-Timeout`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
      env: getProcEnv(),
    });

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResourceProperties('AWS::CodeBuild::Project', {
      TimeoutInMinutes: 180,
      Environment: {
        ComputeType: codebuild.ComputeType.SMALL,
        Image: codebuild.LinuxBuildImage.STANDARD_7_0.imageId,
        Type: codebuild.EnvironmentType.LINUX_CONTAINER,
      },
    });
  });

  test('should create CloudWatch log group with one month retention when stack is initialized', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-InfraResourcesPipeline-LogGroup`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
      env: getProcEnv(),
    });

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      RetentionInDays: 30,
    });
  });

  test('should create IAM policy allowing AssumeRole for CDK roles when build project role exists', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-InfraResourcesPipeline-IAM`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
      env: getProcEnv(),
    });

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResourceProperties('AWS::IAM::Policy', {
      PolicyDocument: {
        Statement: Match.arrayWith([
          Match.objectLike({
            Action: 'sts:AssumeRole',
            Effect: 'Allow',
            Resource: 'arn:aws:iam::*:role/cdk-*',
          }),
        ]),
      },
    });
  });

  test('should create EventBridge rule with correct pattern when stack is initialized', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(
      app,
      `${pjPrefix}-InfraResourcesPipeline-EventBridge`,
      {
        slackChannelName: 'test-channel-name',
        slackWorkspaceId: 'test-workspace-id',
        slackChannelId: 'test-channel-id',
        envKey: envKey,
        appKey: mockAppKey,
        pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
        env: getProcEnv(),
      },
    );

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResourceProperties('AWS::Events::Rule', {
      EventPattern: {
        source: ['aws.s3'],
        'detail-type': ['Object Created'],
        detail: {
          object: {
            key: ['image.zip'],
          },
        },
      },
    });
  });

  test('should create Slack channel configuration with provided IDs when stack is initialized', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-InfraResourcesPipeline-Slack`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
      env: getProcEnv(),
    });

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResourceProperties('AWS::Chatbot::SlackChannelConfiguration', {
      SlackChannelId: 'test-channel-id',
      SlackWorkspaceId: 'test-workspace-id',
      ConfigurationName: 'test-channel-name',
    });
  });

  test('should create notification rules for build events when stack is initialized', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(
      app,
      `${pjPrefix}-InfraResourcesPipeline-Notification`,
      {
        slackChannelName: 'test-channel-name',
        slackWorkspaceId: 'test-workspace-id',
        slackChannelId: 'test-channel-id',
        envKey: envKey,
        appKey: mockAppKey,
        pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
        env: getProcEnv(),
      },
    );

    const template = Template.fromStack(infraResourcesPipeline);
    template.resourceCountIs('AWS::CodeStarNotifications::NotificationRule', 2);

    // Check for success notification rule
    template.hasResourceProperties('AWS::CodeStarNotifications::NotificationRule', {
      DetailType: 'FULL',
      EventTypeIds: Match.arrayWith(['codebuild-project-build-state-succeeded']),
    });

    // Check for failure notification rule
    template.hasResourceProperties('AWS::CodeStarNotifications::NotificationRule', {
      DetailType: 'FULL',
      EventTypeIds: Match.arrayWith(['codebuild-project-build-state-failed']),
    });
  });

  test('should output source bucket name when stack is created', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-InfraResourcesPipeline-Output`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
      env: getProcEnv(),
    });

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasOutput('SourceBucketName', {
      Value: Match.anyValue(),
    });
  });

  test('should create source bucket with RETAIN policy when pipelineBucketRemovalPolicyParam is undefined', () => {
    const pipelineWithDefaultPolicy = new InfraResourcesPipelineStack(app, `${pjPrefix}-PipelineDefaultPolicy`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      // pipelineBucketRemovalPolicyParam not provided
      env: getProcEnv(),
    });

    const defaultPolicyTemplate = Template.fromStack(pipelineWithDefaultPolicy);

    // DeletionPolicy should be Retain (default)
    defaultPolicyTemplate.hasResource('AWS::S3::Bucket', {
      DeletionPolicy: 'Retain',
      UpdateReplacePolicy: 'Retain',
    });
  });

  test('should create source bucket with DESTROY policy when pipelineBucketRemovalPolicyParam specifies DESTROY', () => {
    const pipelineWithCustomPolicy = new InfraResourcesPipelineStack(app, `${pjPrefix}-PipelineCustomPolicy`, {
      slackChannelName: 'test-channel-name',
      slackWorkspaceId: 'test-workspace-id',
      slackChannelId: 'test-channel-id',
      envKey: envKey,
      appKey: mockAppKey,
      pipelineBucketRemovalPolicyParam: {
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        autoDeleteObjects: true,
        emptyOnDelete: true,
      },
      env: getProcEnv(),
    });

    const customPolicyTemplate = Template.fromStack(pipelineWithCustomPolicy);

    customPolicyTemplate.hasResource('AWS::S3::Bucket', {
      DeletionPolicy: 'Delete',
      UpdateReplacePolicy: 'Delete',
    });
  });

  test('should create BuildSpec with correct environment configuration when stack is created', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(
      app,
      `${pjPrefix}-InfraResourcesPipeline-BuildSpec`,
      {
        slackChannelName: 'test-channel-name',
        slackWorkspaceId: 'test-workspace-id',
        slackChannelId: 'test-channel-id',
        envKey: envKey,
        appKey: mockAppKey,
        pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
        env: getProcEnv(),
      },
    );

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResourceProperties('AWS::CodeBuild::Project', {
      Source: {
        BuildSpec: Match.stringLikeRegexp(`-c environment=${envKey}`),
        Type: 'CODEPIPELINE',
      },
    });
  });

  test('should apply DESTROY policy to artifact bucket when build project role exists', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(
      app,
      `${pjPrefix}-InfraResourcesPipeline-ArtifactBucket`,
      {
        slackChannelName: 'test-channel-name',
        slackWorkspaceId: 'test-workspace-id',
        slackChannelId: 'test-channel-id',
        envKey: envKey,
        appKey: mockAppKey,
        pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
        env: getProcEnv(),
      },
    );

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResource('AWS::S3::Bucket', {
      DeletionPolicy: 'Delete',
      UpdateReplacePolicy: 'Delete',
    });
  });

  test('should create CodePipeline with Build stage when build project role exists', () => {
    const infraResourcesPipeline = new InfraResourcesPipelineStack(
      app,
      `${pjPrefix}-InfraResourcesPipeline-BuildStage`,
      {
        slackChannelName: 'test-channel-name',
        slackWorkspaceId: 'test-workspace-id',
        slackChannelId: 'test-channel-id',
        envKey: envKey,
        appKey: mockAppKey,
        pipelineBucketRemovalPolicyParam: config.LogRemovalPolicyParam,
        env: getProcEnv(),
      },
    );

    const template = Template.fromStack(infraResourcesPipeline);
    template.hasResourceProperties('AWS::CodePipeline::Pipeline', {
      Stages: Match.arrayWith([
        Match.objectLike({
          Name: 'Build',
          Actions: Match.arrayWith([
            Match.objectLike({
              ActionTypeId: Match.objectLike({
                Category: 'Build',
                Provider: 'CodeBuild',
              }),
            }),
          ]),
        }),
      ]),
    });
  });
});
