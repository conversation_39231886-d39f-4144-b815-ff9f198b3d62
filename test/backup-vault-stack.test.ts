import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import * as kms from 'aws-cdk-lib/aws-kms';
import { BackupVaultStack } from '../lib/stack/backup-vault-stack';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

describe('The backup vault stack', () => {
  let app: cdk.App;
  let helperStack: cdk.Stack;
  let appKey: kms.IKey;

  beforeEach(() => {
    app = new cdk.App();

    // Create helper stack for mock resources
    helperStack = new cdk.Stack(app, 'HelperStack', { env: getProcEnv() });

    // Create mock KMS key
    appKey = new kms.Key(helperStack, 'TestAppKey', {
      description: 'Test application key for backup vault',
    });
  });

  // Assert Snapshot
  test('Should Match Expected Snapshot When Backup Vault Stack Is Created With Basic Configuration', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStackSnapshot', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  // Assert Number of Resources
  test('Should Create Correct Number Of Resources When Basic Configuration Is Provided', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStackResourceCount', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify exact resource counts
    template.resourceCountIs('AWS::Backup::BackupVault', 1);
  });

  // Assert Correct Policy & Assert Resource Properties
  test('Should Create Backup Vault With Correct Configuration When Basic Setup Is Provided', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStack', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup vault is created
    template.hasResourceProperties('AWS::Backup::BackupVault', {
      EncryptionKeyArn: {
        'Fn::ImportValue': Match.stringLikeRegexp('.*TestAppKey.*'),
      },
    });

    // Verify backup vault resource count
    template.resourceCountIs('AWS::Backup::BackupVault', 1);
  });

  test('Should Expose Vault As Public Property When Stack Is Created', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStackWithVault', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    expect(stack.vault).toBeDefined();
    expect(stack.vault.backupVaultName).toBeDefined();
    expect(stack.vault.backupVaultArn).toBeDefined();
  });

  test('Should Use Provided KMS Key For Encryption When KMS Key Is Provided', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStackWithKMS', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify the backup vault uses the provided KMS key
    template.hasResourceProperties('AWS::Backup::BackupVault', {
      EncryptionKeyArn: {
        'Fn::ImportValue': Match.anyValue(),
      },
    });
  });

  test('Should Create Vault With Correct Construct ID When Stack Is Created', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStackConstructId', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup vault logical ID contains the construct ID 'Vault'
    const backupVaultLogicalIds = Object.keys(template.toJSON().Resources).filter(
      (key) => template.toJSON().Resources[key].Type === 'AWS::Backup::BackupVault',
    );

    expect(backupVaultLogicalIds).toHaveLength(1);
    expect(backupVaultLogicalIds[0]).toContain('Vault');
  });

  test('Should Work With Different Stack Names When Multiple Stacks Are Created', () => {
    // Arrange & Act
    const stack1 = new BackupVaultStack(app, 'BackupVault1', {
      appKey,
      env: getProcEnv(),
    });

    const stack2 = new BackupVaultStack(app, 'BackupVault2', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    const template1 = Template.fromStack(stack1);
    const template2 = Template.fromStack(stack2);

    // Verify both stacks have backup vaults
    template1.resourceCountIs('AWS::Backup::BackupVault', 1);
    template2.resourceCountIs('AWS::Backup::BackupVault', 1);

    // Verify stack names are different
    expect(stack1.stackName).toBe('BackupVault1');
    expect(stack2.stackName).toBe('BackupVault2');
  });

  test('Should Work With Cross-Region References When Cross-Region References Are Enabled', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStackCrossRegion', {
      appKey,
      env: getProcEnv(),
      crossRegionReferences: true,
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup vault is still created with cross-region references enabled
    template.hasResourceProperties('AWS::Backup::BackupVault', {
      EncryptionKeyArn: {
        'Fn::ImportValue': Match.anyValue(),
      },
    });

    // Verify stack is created successfully
    expect(stack.stackName).toBe('TestBackupVaultStackCrossRegion');
  });

  test('Should Have Correct Resource Metadata When Stack Is Created', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStackMetadata', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    const template = Template.fromStack(stack);

    // Verify backup vault has correct type
    template.hasResource('AWS::Backup::BackupVault', {
      Type: 'AWS::Backup::BackupVault',
    });
  });

  test('Should Be Accessible For Other Constructs When Vault Property Is Used', () => {
    // Arrange & Act
    const stack = new BackupVaultStack(app, 'TestBackupVaultStackAccess', {
      appKey,
      env: getProcEnv(),
    });

    // Assert
    // Verify the vault can be used by other constructs
    expect(stack.vault).toBeDefined();
    expect(typeof stack.vault.backupVaultName).toBe('string');
    expect(typeof stack.vault.backupVaultArn).toBe('string');

    // Verify vault has basic properties
    expect(stack.vault.env).toBeDefined();
    expect(stack.vault.node).toBeDefined();
  });
});
