import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { WafConstruct } from '../lib/construct/waf-construct';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import * as s3 from 'aws-cdk-lib/aws-s3';

describe('WafConstruct', () => {
  let app: cdk.App;
  let stack: cdk.Stack;
  let template: Template;

  const basicProps = {
    scope: 'CLOUDFRONT',
    wafLogBucketLifecycleRules: [
      {
        id: 'test-lifecycle-rule',
        status: s3.LifecycleRuleStatus.ENABLED,
        expiration: cdk.Duration.days(30),
      },
    ],
  };

  beforeEach(() => {
    app = new cdk.App();
    stack = new cdk.Stack(app, 'TestStack', {
      env: { account: '************', region: 'us-east-1' },
    });
  });

  describe('Snapshot Tests', () => {
    test('Should Create Basic WAF Construct When Minimal Props Provided', () => {
      // Arrange & Act
      new WafConstruct(stack, 'TestWafConstruct', basicProps);

      // Assert
      template = Template.fromStack(stack);
      expect(template).toMatchSnapshot('Basic WAF Construct');
    });

    test('Should Create WAF Construct With All Features When All Props Provided', () => {
      // Arrange
      const fullProps = {
        ...basicProps,
        defaultAction: { block: {} },
        allowIPList: ['***********/24', '10.0.0.0/8'],
        preSharedKey: 'test-pre-shared-key',
        basicAuthUserName: 'admin',
        basicAuthUserPass: 'password123',
        associations: ['arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-alb/************3456'],
        additionalRules: [
          {
            name: 'CustomRule',
            priority: 50,
            action: { allow: {} },
            statement: {
              byteMatchStatement: {
                searchString: 'test',
                fieldToMatch: { singleHeader: { name: 'custom-header' } },
                textTransformations: [{ priority: 0, type: 'NONE' }],
                positionalConstraint: 'CONTAINS',
              },
            },
            visibilityConfig: {
              sampledRequestsEnabled: true,
              cloudWatchMetricsEnabled: true,
              metricName: 'CustomRule',
            },
          },
        ],
        isUseAwsManageRules: true,
        overrideAction_CommonRuleSet: { count: {} },
        overrideAction_KnownBadInputsRuleSet: { block: {} },
        overrideAction_AmazonIpReputationList: { count: {} },
        overrideAction_LinuxRuleSet: { count: {} },
        overrideAction_SQLiRuleSet: { count: {} },
        ruleAction_IPsetRuleSet: { allow: {} },
        ruleAction_BasicRuleSet: {
          block: {
            customResponse: {
              responseCode: 401,
              responseHeaders: [{ name: 'www-authenticate', value: 'Basic' }],
            },
          },
        },
        logRemovalPolicyParam: {
          removalPolicy: cdk.RemovalPolicy.DESTROY,
          autoDeleteObjects: true,
        },
        csirtWAFParam: {
          isUseCSIRTManageRules: true,
          CSIRTManagerRules: {
            overrideAction: { count: {} },
            ruleGroupArn: 'arn:aws:wafv2:us-east-1:************:regional/rulegroup/test-rule-group/12345678-1234-1234-1234-************',
          },
          CSIRTIpSetArn: 'arn:aws:wafv2:us-east-1:************:regional/ipset/test-ip-set/12345678-1234-1234-1234-************',
        },
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', fullProps);

      // Assert
      template = Template.fromStack(stack);
      expect(template).toMatchSnapshot('Full Featured WAF Construct');
    });

    test('Should Create WAF Construct With CSIRT Rules When CSIRT Param Provided', () => {
      // Arrange
      const csirtProps = {
        ...basicProps,
        csirtWAFParam: {
          isUseCSIRTManageRules: true,
          CSIRTManagerRules: {
            overrideAction: { count: {} },
            ruleGroupArn: 'arn:aws:wafv2:us-east-1:************:regional/rulegroup/csirt-rule-group/12345678-1234-1234-1234-************',
          },
          CSIRTIpSetArn: 'arn:aws:wafv2:us-east-1:************:regional/ipset/csirt-ip-set/12345678-1234-1234-1234-************',
        },
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', csirtProps);

      // Assert
      template = Template.fromStack(stack);
      expect(template).toMatchSnapshot('WAF Construct With CSIRT Rules');
    });
  });

  describe('Resource Count Tests', () => {
    test('Should Create Correct Number Of Resources When Basic Props Provided', () => {
      // Arrange & Act
      new WafConstruct(stack, 'TestWafConstruct', basicProps);

      // Assert
      template = Template.fromStack(stack);
      template.resourceCountIs('AWS::WAFv2::WebACL', 1);
      template.resourceCountIs('AWS::S3::Bucket', 1);
      template.resourceCountIs('AWS::WAFv2::LoggingConfiguration', 1);
      template.resourceCountIs('AWS::WAFv2::IPSet', 0);
      template.resourceCountIs('AWS::SSM::Parameter', 0);
      template.resourceCountIs('AWS::WAFv2::WebACLAssociation', 0);
    });

    test('Should Create IP Set When Allow IP List Provided', () => {
      // Arrange
      const propsWithIPList = {
        ...basicProps,
        allowIPList: ['***********/24', '10.0.0.0/8'],
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithIPList);

      // Assert
      template = Template.fromStack(stack);
      template.resourceCountIs('AWS::WAFv2::IPSet', 1);
    });

    test('Should Create SSM Parameters When Basic Auth Provided', () => {
      // Arrange
      const propsWithBasicAuth = {
        ...basicProps,
        basicAuthUserName: 'admin',
        basicAuthUserPass: 'password123',
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithBasicAuth);

      // Assert
      template = Template.fromStack(stack);
      template.resourceCountIs('AWS::SSM::Parameter', 2);
    });

    test('Should Create WebACL Association When Regional Scope And Associations Provided', () => {
      // Arrange
      const propsWithAssociations = {
        ...basicProps,
        scope: 'REGIONAL',
        associations: [
          'arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-alb-1/************3456',
          'arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-alb-2/************3457',
        ],
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithAssociations);

      // Assert
      template = Template.fromStack(stack);
      template.resourceCountIs('AWS::WAFv2::WebACLAssociation', 2);
    });

    test('Should Not Create WebACL Association When CloudFront Scope And Associations Provided', () => {
      // Arrange
      const propsWithAssociations = {
        ...basicProps,
        scope: 'CLOUDFRONT',
        associations: ['arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-alb/************3456'],
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithAssociations);

      // Assert
      template = Template.fromStack(stack);
      template.resourceCountIs('AWS::WAFv2::WebACLAssociation', 0);
    });
  });

  describe('Policy Validation Tests', () => {
    test('Should Create Correct WebACL Properties When Default Props Provided', () => {
      // Arrange & Act
      new WafConstruct(stack, 'TestWafConstruct', basicProps);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        DefaultAction: { Allow: {} },
        Scope: 'CLOUDFRONT',
        VisibilityConfig: {
          CloudWatchMetricsEnabled: true,
          MetricName: 'WafAcl',
          SampledRequestsEnabled: true,
        },
      });
    });

    test('Should Create Correct WebACL Properties When Custom Default Action Provided', () => {
      // Arrange
      const propsWithCustomAction = {
        ...basicProps,
        defaultAction: { block: {} },
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithCustomAction);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        DefaultAction: { Block: {} },
        Scope: 'CLOUDFRONT',
      });
    });

    test('Should Create Correct IP Set Properties When Allow IP List Provided', () => {
      // Arrange
      const propsWithIPList = {
        ...basicProps,
        allowIPList: ['***********/24', '10.0.0.0/8'],
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithIPList);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::IPSet', {
        Name: 'IPset',
        IPAddressVersion: 'IPV4',
        Scope: 'CLOUDFRONT',
        Addresses: ['***********/24', '10.0.0.0/8'],
      });
    });

    test('Should Create Correct S3 Bucket Properties When Basic Props Provided', () => {
      // Arrange & Act
      new WafConstruct(stack, 'TestWafConstruct', basicProps);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::S3::Bucket', {
        BucketName: Match.stringLikeRegexp('aws-waf-logs-************-teststack-bucket'),
        BucketEncryption: {
          ServerSideEncryptionConfiguration: [
            {
              ServerSideEncryptionByDefault: {
                SSEAlgorithm: 'AES256',
              },
            },
          ],
        },
        PublicAccessBlockConfiguration: {
          BlockPublicAcls: true,
          BlockPublicPolicy: true,
          IgnorePublicAcls: true,
          RestrictPublicBuckets: true,
        },
        LifecycleConfiguration: {
          Rules: [
            {
              Id: 'test-lifecycle-rule',
              Status: 'Enabled',
              ExpirationInDays: 30,
            },
          ],
        },
      });
    });

    test('Should Create Correct Logging Configuration Properties When Basic Props Provided', () => {
      // Arrange & Act
      new WafConstruct(stack, 'TestWafConstruct', basicProps);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::LoggingConfiguration', {
        LogDestinationConfigs: [
          Match.objectLike({
            'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*WafLogsBucket.*'), 'Arn']),
          }),
        ],
        ResourceArn: Match.objectLike({
          'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*WebAcl.*'), 'Arn']),
        }),
      });
    });

    test('Should Not Create Duplicate S3 Bucket Policies When Multiple WAF Constructs Created', () => {
      // Arrange & Act
      new WafConstruct(stack, 'TestWafConstruct1', basicProps);
      new WafConstruct(stack, 'TestWafConstruct2', basicProps);

      // Assert
      template = Template.fromStack(stack);
      template.resourceCountIs('AWS::S3::Bucket', 2);
      template.resourceCountIs('AWS::S3::BucketPolicy', 0); // Should not create duplicate policies
    });
  });

  describe('Rule Configuration Tests', () => {
    test('Should Create AWS Managed Rules When isUseAwsManageRules Is True', () => {
      // Arrange
      const propsWithManagedRules = {
        ...basicProps,
        isUseAwsManageRules: true,
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithManagedRules);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'AWSManagedRulesCommonRuleSet',
            Priority: 2,
            OverrideAction: { Count: {} },
            Statement: {
              ManagedRuleGroupStatement: {
                VendorName: 'AWS',
                Name: 'AWSManagedRulesCommonRuleSet',
              },
            },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesKnownBadInputsRuleSet',
            Priority: 3,
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesAmazonIpReputationList',
            Priority: 4,
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesLinuxRuleSet',
            Priority: 5,
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesSQLiRuleSet',
            Priority: 6,
          }),
        ]),
      });
    });

    test('Should Not Create AWS Managed Rules When isUseAwsManageRules Is False', () => {
      // Arrange
      const propsWithoutManagedRules = {
        ...basicProps,
        isUseAwsManageRules: false,
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithoutManagedRules);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.not(
          Match.arrayWith([
            Match.objectLike({
              Name: 'AWSManagedRulesCommonRuleSet',
            }),
          ])
        ),
      });
    });

    test('Should Create IP Set Rule When Allow IP List Provided', () => {
      // Arrange
      const propsWithIPList = {
        ...basicProps,
        allowIPList: ['***********/24'],
        ruleAction_IPsetRuleSet: { allow: {} },
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithIPList);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'IPset',
            Priority: 11,
            Action: { Allow: {} },
            Statement: {
              IPSetReferenceStatement: {
                Arn: Match.objectLike({
                  'Fn::GetAtt': Match.arrayWith([Match.stringLikeRegexp('.*IPset.*'), 'Arn']),
                }),
              },
            },
          }),
        ]),
      });
    });

    test('Should Create Pre-Shared Key Rule When Pre-Shared Key Provided', () => {
      // Arrange
      const propsWithPreSharedKey = {
        ...basicProps,
        preSharedKey: 'test-pre-shared-key',
      };

      // Act
      const construct = new WafConstruct(stack, 'TestWafConstruct', propsWithPreSharedKey);

      // Assert
      template = Template.fromStack(stack);
      expect(construct.preSharedKeyValue).toBeDefined();
      expect(construct.preSharedKeyValue).toHaveLength(8);

      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'preSharedKey',
            Priority: 12,
            Action: { Allow: {} },
            Statement: {
              ByteMatchStatement: {
                SearchString: construct.preSharedKeyValue,
                FieldToMatch: {
                  SingleHeader: {
                    Name: 'x-pre-shared-key',
                  },
                },
                PositionalConstraint: 'EXACTLY',
                TextTransformations: [
                  {
                    Priority: 0,
                    Type: 'NONE',
                  },
                ],
              },
            },
          }),
        ]),
      });
    });

    test('Should Create Basic Auth Rule When Basic Auth Credentials Provided', () => {
      // Arrange
      const propsWithBasicAuth = {
        ...basicProps,
        basicAuthUserName: 'admin',
        basicAuthUserPass: 'password123',
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithBasicAuth);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'BasicAuth',
            Priority: 13,
            Action: {
              Block: {
                CustomResponse: {
                  ResponseCode: 401,
                  ResponseHeaders: [
                    {
                      Name: 'www-authenticate',
                      Value: 'Basic',
                    },
                  ],
                },
              },
            },
            Statement: {
              NotStatement: {
                Statement: {
                  ByteMatchStatement: {
                    FieldToMatch: {
                      SingleHeader: {
                        Name: 'authorization',
                      },
                    },
                    PositionalConstraint: 'EXACTLY',
                    TextTransformations: [
                      {
                        Priority: 0,
                        Type: 'NONE',
                      },
                    ],
                    SearchString: Match.anyValue(),
                  },
                },
              },
            },
          }),
        ]),
      });
    });
  });

  describe('CSIRT Rules Tests', () => {
    test('Should Create CSIRT Rules When CSIRT Param Provided', () => {
      // Arrange
      const propsWithCSIRT = {
        ...basicProps,
        csirtWAFParam: {
          isUseCSIRTManageRules: true,
          CSIRTManagerRules: {
            overrideAction: { count: {} },
            ruleGroupArn: 'arn:aws:wafv2:us-east-1:************:regional/rulegroup/csirt-rule-group/12345678-1234-1234-1234-************',
          },
          CSIRTIpSetArn: 'arn:aws:wafv2:us-east-1:************:regional/ipset/csirt-ip-set/12345678-1234-1234-1234-************',
        },
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithCSIRT);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'CSIRTBlockSpecificIPs',
            Priority: 0,
            Action: { Block: {} },
            Statement: {
              IPSetReferenceStatement: {
                Arn: 'arn:aws:wafv2:us-east-1:************:regional/ipset/csirt-ip-set/12345678-1234-1234-1234-************',
              },
            },
          }),
          Match.objectLike({
            Name: 'CSIRTManagerRules',
            Priority: 1,
            OverrideAction: { Count: {} },
            Statement: {
              RuleGroupReferenceStatement: {
                Arn: 'arn:aws:wafv2:us-east-1:************:regional/rulegroup/csirt-rule-group/12345678-1234-1234-1234-************',
              },
            },
          }),
        ]),
      });
    });

    test('Should Not Create CSIRT Rules When CSIRT Param Not Provided', () => {
      // Arrange & Act
      new WafConstruct(stack, 'TestWafConstruct', basicProps);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.not(
          Match.arrayWith([
            Match.objectLike({
              Name: 'CSIRTBlockSpecificIPs',
            }),
          ])
        ),
      });
    });
  });

  describe('Override Action Tests', () => {
    test('Should Apply Custom Override Actions When Provided', () => {
      // Arrange
      const propsWithCustomOverrides = {
        ...basicProps,
        isUseAwsManageRules: true,
        overrideAction_CommonRuleSet: { block: {} } as wafv2.CfnWebACL.OverrideActionProperty,
        overrideAction_KnownBadInputsRuleSet: { none: {} } as wafv2.CfnWebACL.OverrideActionProperty,
        overrideAction_AmazonIpReputationList: { count: {} } as wafv2.CfnWebACL.OverrideActionProperty,
        overrideAction_LinuxRuleSet: { block: {} } as wafv2.CfnWebACL.OverrideActionProperty,
        overrideAction_SQLiRuleSet: { none: {} } as wafv2.CfnWebACL.OverrideActionProperty,
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithCustomOverrides);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'AWSManagedRulesCommonRuleSet',
            OverrideAction: { Block: {} },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesKnownBadInputsRuleSet',
            OverrideAction: { None: {} },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesAmazonIpReputationList',
            OverrideAction: { Count: {} },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesLinuxRuleSet',
            OverrideAction: { Block: {} },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesSQLiRuleSet',
            OverrideAction: { None: {} },
          }),
        ]),
      });
    });

    test('Should Apply Default Override Actions When Not Provided', () => {
      // Arrange
      const propsWithDefaultOverrides = {
        ...basicProps,
        isUseAwsManageRules: true,
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithDefaultOverrides);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'AWSManagedRulesCommonRuleSet',
            OverrideAction: { Count: {} },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesKnownBadInputsRuleSet',
            OverrideAction: { Count: {} },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesAmazonIpReputationList',
            OverrideAction: { Count: {} },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesLinuxRuleSet',
            OverrideAction: { Count: {} },
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesSQLiRuleSet',
            OverrideAction: { Count: {} },
          }),
        ]),
      });
    });
  });

  describe('Additional Rules Tests', () => {
    test('Should Include Additional Rules When Provided', () => {
      // Arrange
      const additionalRule: wafv2.CfnWebACL.RuleProperty = {
        name: 'CustomAdditionalRule',
        priority: 50,
        action: { allow: {} },
        statement: {
          byteMatchStatement: {
            searchString: 'custom-value',
            fieldToMatch: { singleHeader: { name: 'custom-header' } },
            textTransformations: [{ priority: 0, type: 'NONE' }],
            positionalConstraint: 'CONTAINS',
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'CustomAdditionalRule',
        },
      };

      const propsWithAdditionalRules = {
        ...basicProps,
        additionalRules: [additionalRule],
      };

      // Act
      new WafConstruct(stack, 'TestWafConstruct', propsWithAdditionalRules);

      // Assert
      template = Template.fromStack(stack);
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'CustomAdditionalRule',
            Priority: 50,
            Action: { Allow: {} },
            Statement: {
              ByteMatchStatement: {
                SearchString: 'custom-value',
                FieldToMatch: { SingleHeader: { Name: 'custom-header' } },
                TextTransformations: [{ Priority: 0, Type: 'NONE' }],
                PositionalConstraint: 'CONTAINS',
              },
            },
          }),
        ]),
      });
    });
  });

  describe('Error Handling Tests', () => {
    test('Should Throw Exception When Basic Auth Username Provided Without Password', () => {
      // Arrange
      const propsWithIncompleteBasicAuth = {
        ...basicProps,
        basicAuthUserName: 'admin',
        // basicAuthUserPass is missing
      };

      // Act & Assert
      expect(() => {
        new WafConstruct(stack, 'TestWafConstruct', propsWithIncompleteBasicAuth);
      }).not.toThrow(); // The construct doesn't throw, it just doesn't create the rule
    });

    test('Should Throw Exception When Basic Auth Password Provided Without Username', () => {
      // Arrange
      const propsWithIncompleteBasicAuth = {
        ...basicProps,
        basicAuthUserPass: 'password123',
        // basicAuthUserName is missing
      };

      // Act & Assert
      expect(() => {
        new WafConstruct(stack, 'TestWafConstruct', propsWithIncompleteBasicAuth);
      }).not.toThrow(); // The construct doesn't throw, it just doesn't create the rule
    });
  });
});
