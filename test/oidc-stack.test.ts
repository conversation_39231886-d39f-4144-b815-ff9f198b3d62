import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { OidcStack } from '../lib/stack/oidc-stack';
import { config, envKey, getProcEnv, pjPrefix } from '../test';
// Helper function to create test stacks with specific configurations
function createTestStack(id: string, props: Partial<ConstructorParameters<typeof OidcStack>[2]> = {}) {
  const testApp = new cdk.App();

  // Add environment tag if needed
  const envTagName = 'Environment';
  cdk.Tags.of(testApp).add(envTagName, envKey);

  // Merge default props with the provided props
  const stackProps = {
    OrganizationName: 'test-org',
    RepositoryNames: {
      WafRepositoryName: 'waf-repo',
      InfraRepositoryName: 'infra-repo',
      EcsDeployRepositoryName: 'ecs-deploy-repo',
    },
    env: getProcEnv(),
    ...props,
  };

  const stack = new OidcStack(testApp, id, stackProps);
  return { stack, template: Template.fromStack(stack) };
}

describe(`${pjPrefix} OIDC Stack`, () => {
  // Base OIDC stack for most tests
  let oidcStack: OidcStack;
  let template: Template;

  beforeEach(() => {
    const { stack, template: templateResult } = createTestStack(`${pjPrefix}-OIDC`);
    oidcStack = stack;
    template = templateResult;
  });

  test('should match snapshot when stack is created', () => {
    expect(template).toMatchSnapshot();
  });

  test('should create three IAM roles when stack is initialized with default repositories', () => {
    template.resourceCountIs('AWS::IAM::Role', 4); // 3 roles + 1 OIDC provider role
  });

  test('should create WAF role with correct permissions when WafRepositoryName is provided', () => {
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Action: 'sts:AssumeRoleWithWebIdentity',
            Condition: Match.objectLike({
              StringLike: Match.objectLike({
                'token.actions.githubusercontent.com:sub': Match.stringLikeRegexp('test-org/waf-repo:*'),
              }),
            }),
          }),
        ]),
      }),
      ManagedPolicyArns: Match.absent(),
    });

    template.hasResourceProperties('AWS::IAM::Policy', {
      PolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Action: ['wafv2:ListWebACLs', 'wafv2:GetWebACL', 'wafv2:UpdateWebACL'],
            Resource: '*',
          }),
        ]),
      }),
    });
  });

  test('should create InfraResources role with correct permissions when InfraRepositoryName is provided', () => {
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Action: 'sts:AssumeRoleWithWebIdentity',
            Condition: Match.objectLike({
              StringLike: Match.objectLike({
                'token.actions.githubusercontent.com:sub': Match.stringLikeRegexp('test-org/infra-repo:*'),
              }),
            }),
          }),
        ]),
      }),
    });

    template.hasResourceProperties('AWS::IAM::Policy', {
      PolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Action: ['cloudformation:DescribeStacks', 's3:PutObject'],
            Resource: '*',
          }),
        ]),
      }),
    });
  });

  test('should create ECS Deploy role with correct permissions when EcsDeployRepositoryName is provided', () => {
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Action: 'sts:AssumeRoleWithWebIdentity',
            Condition: Match.objectLike({
              StringLike: Match.objectLike({
                'token.actions.githubusercontent.com:sub': Match.stringLikeRegexp('test-org/ecs-deploy-repo:*'),
              }),
            }),
          }),
        ]),
      }),
    });

    template.hasResourceProperties('AWS::IAM::Policy', {
      PolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Action: Match.arrayWith([
              'cloudformation:DescribeStacks',
              's3:PutObject',
              'ecr:GetAuthorizationToken',
              'ecr:InitiateLayerUpload',
              'ecr:UploadLayerPart',
              'ecr:CompleteLayerUpload',
              'ecr:BatchCheckLayerAvailability',
              'ecr:PutImage',
            ]),
            Resource: '*',
          }),
        ]),
      }),
    });
  });

  test('should set termination protection when terminationProtection is true', () => {
    // Arrange
    const { stack: stackWithProtection } = createTestStack(`${pjPrefix}-OIDC-TP`, {
      terminationProtection: true,
    });

    expect(stackWithProtection.terminationProtection).toBe(true);
  });

  test('should apply correct tags to resources', () => {
    // Check tags on IAM roles instead of OIDC provider
    template.hasResourceProperties('AWS::IAM::Role', {
      Tags: Match.arrayWith([
        Match.objectLike({
          Key: 'Environment',
          Value: envKey,
        }),
      ]),
    });
  });

  test('should create roles with custom repository names when specified', () => {
    // Arrange
    const customRepoNames = {
      WafRepositoryName: 'custom-waf-repo',
      InfraRepositoryName: 'custom-infra-repo',
      EcsDeployRepositoryName: 'custom-ecs-deploy-repo',
    };

    const { template: customTemplate } = createTestStack(`${pjPrefix}-OIDC-Custom`, {
      RepositoryNames: customRepoNames,
    });

    // Assert
    customTemplate.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Condition: Match.objectLike({
              StringLike: Match.objectLike({
                'token.actions.githubusercontent.com:sub': Match.stringLikeRegexp('test-org/custom-waf-repo:*'),
              }),
            }),
          }),
        ]),
      }),
    });

    customTemplate.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Condition: Match.objectLike({
              StringLike: Match.objectLike({
                'token.actions.githubusercontent.com:sub': Match.stringLikeRegexp('test-org/custom-infra-repo:*'),
              }),
            }),
          }),
        ]),
      }),
    });

    customTemplate.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Condition: Match.objectLike({
              StringLike: Match.objectLike({
                'token.actions.githubusercontent.com:sub': Match.stringLikeRegexp('test-org/custom-ecs-deploy-repo:*'),
              }),
            }),
          }),
        ]),
      }),
    });
  });

  test('should create roles with custom organization name when specified', () => {
    // Arrange
    const customOrgName = 'custom-org';

    const { template: customTemplate } = createTestStack(`${pjPrefix}-OIDC-CustomOrg`, {
      OrganizationName: customOrgName,
    });

    // Assert
    customTemplate.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Condition: Match.objectLike({
              StringLike: Match.objectLike({
                'token.actions.githubusercontent.com:sub': Match.stringLikeRegexp('custom-org/waf-repo:*'),
              }),
            }),
          }),
        ]),
      }),
    });
  });

  test('should create OIDC provider with correct URL and client ID list', () => {
    // Assert that the OIDC provider is created with the correct properties
    template.hasResourceProperties('Custom::AWSCDKOpenIdConnectProvider', {
      Url: 'https://token.actions.githubusercontent.com',
      ClientIDList: ['sts.amazonaws.com'],
    });
  });

  test('should create OIDC provider with correct deletion policy', () => {
    template.hasResource('Custom::AWSCDKOpenIdConnectProvider', {
      DeletionPolicy: 'Delete',
      UpdateReplacePolicy: 'Delete',
    });
  });

  test('should include StringEquals condition in role trust policies', () => {
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Condition: Match.objectLike({
              StringEquals: {
                'token.actions.githubusercontent.com:aud': 'sts.amazonaws.com',
              },
            }),
          }),
        ]),
      }),
    });
  });
  test('should create Lambda function with correct configuration for OIDC provider', () => {
    template.hasResourceProperties('AWS::Lambda::Function', {
      Handler: '__entrypoint__.handler',
      Runtime: 'nodejs22.x',
      Timeout: 900,
      MemorySize: 128,
    });
  });
  test('should create OIDC provider with RejectUnauthorized set to false', () => {
    template.hasResourceProperties('Custom::AWSCDKOpenIdConnectProvider', {
      RejectUnauthorized: false,
    });
  });
  test('should create IAM role for custom resource provider with correct permissions', () => {
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Action: 'sts:AssumeRole',
            Principal: {
              Service: 'lambda.amazonaws.com',
            },
          }),
        ]),
      }),
      Policies: Match.arrayWith([
        Match.objectLike({
          PolicyDocument: Match.objectLike({
            Statement: Match.arrayWith([
              Match.objectLike({
                Action: Match.arrayWith([
                  'iam:CreateOpenIDConnectProvider',
                  'iam:DeleteOpenIDConnectProvider',
                  'iam:UpdateOpenIDConnectProviderThumbprint',
                ]),
                Effect: 'Allow',
              }),
            ]),
          }),
        }),
      ]),
    });
  });
  test('should create OIDC provider with appropriate properties', () => {
    template.hasResourceProperties('Custom::AWSCDKOpenIdConnectProvider', {
      Url: 'https://token.actions.githubusercontent.com',
      ClientIDList: ['sts.amazonaws.com'],
      RejectUnauthorized: false,
    });
  });
  test('should create IAM roles for GitHub repositories', () => {
    // Verify role for WAF repo exists
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: Match.objectLike({
        Statement: Match.arrayWith([
          Match.objectLike({
            Condition: Match.objectLike({
              StringLike: Match.objectLike({
                'token.actions.githubusercontent.com:sub': Match.stringLikeRegexp('.*waf-repo:.*'),
              }),
            }),
          }),
        ]),
      }),
    });
  });
});
