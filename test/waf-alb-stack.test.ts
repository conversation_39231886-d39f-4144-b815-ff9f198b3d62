import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { WafAlbStack } from '../lib/stack/waf-alb-stack';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

let app: any;

// Helper function to create test stacks with specific configurations
function createTestStack(id: string, props: Partial<ConstructorParameters<typeof WafAlbStack>[2]> = {}) {
  const testApp = new cdk.App();

  // Add environment tag if needed
  const envTagName = 'Environment';
  cdk.Tags.of(testApp).add(envTagName, envKey);

  // Merge default props with the provided props
  const stackProps = {
    scope: 'REGIONAL',
    wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
    env: getProcEnv(),
    ...props,
  };

  const stack = new WafAlbStack(testApp, id, stackProps);
  return { stack, template: Template.fromStack(stack) };
}

beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';
  cdk.Tags.of(app).add(envTagName, envKey);
});

describe(`${pjPrefix} WAF ALB Stack`, () => {
  let wafAlbStack: WafAlbStack;

  beforeEach(() => {
    wafAlbStack = new WafAlbStack(app, `${pjPrefix}-WafAlb`, {
      scope: 'REGIONAL',
      associations: [
        'arn:aws:elasticloadbalancing:ap-northeast-1:123456789012:loadbalancer/app/test-alb/1234567890123456',
      ],
      env: getProcEnv(),
      crossRegionReferences: true,
      ...config.WafAlbParam,
      wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      csirtWAFParam: config.CSIRTWAFParamALB,
    });
  });

  test('Should snapshot matching When WAF ALB Stack is created', () => {
    const template = Template.fromStack(wafAlbStack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  test('Should define webAcl and preSharedKey properties When WAF ALB Stack is instantiated', () => {
    expect(wafAlbStack.webAcl).toBeDefined();
    expect(wafAlbStack.preSharedKey).toBeDefined();
  });

  test('Should create WebACL with REGIONAL scope When WAF ALB Stack is deployed', () => {
    const template = Template.fromStack(wafAlbStack);

    template.hasResourceProperties('AWS::WAFv2::WebACL', {
      Scope: 'REGIONAL',
      DefaultAction: {
        Allow: {},
      },
      VisibilityConfig: {
        CloudWatchMetricsEnabled: true,
        MetricName: 'WafAcl',
        SampledRequestsEnabled: true,
      },
    });

    template.resourceCountIs('AWS::WAFv2::WebACL', 1);
  });

  test('Should create IP Set for allowlist When allowIPList is configured', () => {
    const template = Template.fromStack(wafAlbStack);

    template.hasResourceProperties('AWS::WAFv2::IPSet', {
      Name: 'IPset',
      IPAddressVersion: 'IPV4',
      Scope: 'REGIONAL',
      Addresses: config.WafAlbParam.allowIPList,
    });

    template.resourceCountIs('AWS::WAFv2::IPSet', 1);
  });

  test('Should create AWS managed rules When isUseAwsManageRules is enabled', () => {
    const template = Template.fromStack(wafAlbStack);

    // Check for AWS managed rules in WebACL rules
    template.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: 'AWSManagedRulesCommonRuleSet',
          Priority: 2,
          OverrideAction: { Count: {} },
          Statement: {
            ManagedRuleGroupStatement: {
              VendorName: 'AWS',
              Name: 'AWSManagedRulesCommonRuleSet',
            },
          },
          VisibilityConfig: {
            CloudWatchMetricsEnabled: true,
            MetricName: 'AWS-AWSManagedRulesCommonRuleSet',
            SampledRequestsEnabled: true,
          },
        }),
        Match.objectLike({
          Name: 'AWSManagedRulesKnownBadInputsRuleSet',
          Priority: 3,
          OverrideAction: { Count: {} },
          Statement: {
            ManagedRuleGroupStatement: {
              VendorName: 'AWS',
              Name: 'AWSManagedRulesKnownBadInputsRuleSet',
            },
          },
          VisibilityConfig: {
            CloudWatchMetricsEnabled: true,
            MetricName: 'AWS-AWSManagedRulesKnownBadInputsRuleSet',
            SampledRequestsEnabled: true,
          },
        }),
        Match.objectLike({
          Name: 'AWSManagedRulesAmazonIpReputationList',
          Priority: 4,
          OverrideAction: { Count: {} },
          Statement: {
            ManagedRuleGroupStatement: {
              VendorName: 'AWS',
              Name: 'AWSManagedRulesAmazonIpReputationList',
            },
          },
          VisibilityConfig: {
            CloudWatchMetricsEnabled: true,
            MetricName: 'AWS-AWSManagedRulesAmazonIpReputationList',
            SampledRequestsEnabled: true,
          },
        }),
        Match.objectLike({
          Name: 'AWSManagedRulesLinuxRuleSet',
          Priority: 5,
          OverrideAction: { Count: {} },
          Statement: {
            ManagedRuleGroupStatement: {
              VendorName: 'AWS',
              Name: 'AWSManagedRulesLinuxRuleSet',
            },
          },
          VisibilityConfig: {
            CloudWatchMetricsEnabled: true,
            MetricName: 'AWS-AWSManagedRulesLinuxRuleSet',
            SampledRequestsEnabled: true,
          },
        }),
        Match.objectLike({
          Name: 'AWSManagedRulesSQLiRuleSet',
          Priority: 6,
          OverrideAction: { Count: {} },
          Statement: {
            ManagedRuleGroupStatement: {
              VendorName: 'AWS',
              Name: 'AWSManagedRulesSQLiRuleSet',
            },
          },
          VisibilityConfig: {
            CloudWatchMetricsEnabled: true,
            MetricName: 'AWS-AWSManagedRulesSQLiRuleSet',
            SampledRequestsEnabled: true,
          },
        }),
      ]),
    });
  });

  test('Should create IP Set rule with correct priority When allowIPList is provided', () => {
    const template = Template.fromStack(wafAlbStack);

    template.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: 'IPset',
          Priority: 11,
          Action: { Allow: {} },
          Statement: {
            IPSetReferenceStatement: {
              Arn: Match.anyValue(),
            },
          },
          VisibilityConfig: {
            CloudWatchMetricsEnabled: true,
            MetricName: 'IPset',
            SampledRequestsEnabled: true,
          },
        }),
      ]),
    });
  });

  test('Should create pre-shared key rule When preSharedKey is configured', () => {
    const template = Template.fromStack(wafAlbStack);

    template.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith([
        Match.objectLike({
          Name: 'preSharedKey',
          Priority: 12,
          Action: { Allow: {} },
          Statement: {
            ByteMatchStatement: {
              SearchString: Match.anyValue(), // SHA256 hash of pre-shared key
              FieldToMatch: {
                SingleHeader: {
                  name: 'x-pre-shared-key',
                },
              },
              PositionalConstraint: 'EXACTLY',
              TextTransformations: [
                {
                  Priority: 0,
                  Type: 'NONE',
                },
              ],
            },
          },
          VisibilityConfig: {
            CloudWatchMetricsEnabled: true,
            MetricName: 'IPset',
            SampledRequestsEnabled: true,
          },
        }),
      ]),
    });
  });

  test('Should create CSIRT rules When CSIRT WAF parameters are enabled', () => {
    const template = Template.fromStack(wafAlbStack);

    if (config.CSIRTWAFParamALB?.isUseCSIRTManageRules) {
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'CSIRTBlockSpecificIPs',
            Priority: 0,
            Action: { Block: {} },
            Statement: {
              IPSetReferenceStatement: {
                Arn: config.CSIRTWAFParamALB.CSIRTIpSetArn,
              },
            },
            VisibilityConfig: {
              CloudWatchMetricsEnabled: true,
              MetricName: 'CSIRTBlockSpecificIPs',
              SampledRequestsEnabled: true,
            },
          }),
          Match.objectLike({
            Name: 'CSIRTManagerRules',
            Priority: 1,
            OverrideAction: { None: {} }, // CDK converts 'none' to 'None'
            Statement: {
              RuleGroupReferenceStatement: {
                Arn: config.CSIRTWAFParamALB.CSIRTManagerRules.ruleGroupArn,
              },
            },
            VisibilityConfig: {
              CloudWatchMetricsEnabled: true,
              MetricName: 'CSIRTManagerRules',
              SampledRequestsEnabled: true,
            },
          }),
        ]),
      });
    }
  });

  test('Should create S3 bucket for WAF logs When WAF ALB Stack is deployed', () => {
    const template = Template.fromStack(wafAlbStack);

    template.hasResourceProperties('AWS::S3::Bucket', {
      BucketName: Match.stringLikeRegexp('^aws-waf-logs-.*-bucket$'),
      PublicAccessBlockConfiguration: {
        BlockPublicAcls: true,
        BlockPublicPolicy: true,
        IgnorePublicAcls: true,
        RestrictPublicBuckets: true,
      },
      BucketEncryption: {
        ServerSideEncryptionConfiguration: [
          {
            ServerSideEncryptionByDefault: {
              SSEAlgorithm: 'AES256',
            },
          },
        ],
      },
      LifecycleConfiguration: {
        Rules: config.s3AuditLogLifecycleRules.map((rule) => {
          const cfRule: any = { Status: 'Enabled' };

          if (rule.expiration) {
            cfRule.ExpirationInDays = rule.expiration.toDays();
          }

          if (rule.abortIncompleteMultipartUploadAfter) {
            cfRule.AbortIncompleteMultipartUpload = {
              DaysAfterInitiation: rule.abortIncompleteMultipartUploadAfter.toDays(),
            };
          }

          return cfRule;
        }),
      },
    });

    template.resourceCountIs('AWS::S3::Bucket', 1);
  });

  test('Should create WAF logging configuration When WAF ALB Stack is deployed', () => {
    const template = Template.fromStack(wafAlbStack);

    template.hasResourceProperties('AWS::WAFv2::LoggingConfiguration', {
      LogDestinationConfigs: [
        Match.anyValue(), // S3 bucket ARN
      ],
      ResourceArn: Match.anyValue(), // WebACL ARN
    });

    template.resourceCountIs('AWS::WAFv2::LoggingConfiguration', 1);
  });

  test('Should create WebACL Association When associations are provided', () => {
    const template = Template.fromStack(wafAlbStack);

    template.hasResourceProperties('AWS::WAFv2::WebACLAssociation', {
      ResourceArn:
        'arn:aws:elasticloadbalancing:ap-northeast-1:123456789012:loadbalancer/app/test-alb/1234567890123456',
      WebACLArn: Match.anyValue(),
    });

    template.resourceCountIs('AWS::WAFv2::WebACLAssociation', 1);
  });

  test('Should create correct number of rules When all rule types are enabled', () => {
    const template = Template.fromStack(wafAlbStack);

    // Expected rules:
    // - CSIRT rules: 2 (if enabled)
    // - AWS managed rules: 5
    // - IP Set rule: 1 (if allowIPList provided)
    // - Pre-shared key rule: 1 (if preSharedKey provided)
    let expectedRuleCount = 5; // AWS managed rules

    if (config.CSIRTWAFParamALB?.isUseCSIRTManageRules) {
      expectedRuleCount += 2; // CSIRT rules
    }

    if (config.WafAlbParam.allowIPList && config.WafAlbParam.allowIPList.length > 0) {
      expectedRuleCount += 1; // IP Set rule
    }

    if (config.WafAlbParam.preSharedKey) {
      expectedRuleCount += 1; // Pre-shared key rule
    }

    template.hasResourceProperties('AWS::WAFv2::WebACL', {
      Rules: Match.arrayWith(
        Array(expectedRuleCount).fill(
          Match.objectLike({
            Name: Match.anyValue(),
            Priority: Match.anyValue(),
            VisibilityConfig: Match.objectLike({
              CloudWatchMetricsEnabled: Match.anyValue(),
              SampledRequestsEnabled: Match.anyValue(),
            }),
          }),
        ),
      ),
    });
  });

  test('Should apply removal policy and lifecycle rules When log removal policy parameters are configured', () => {
    const template = Template.fromStack(wafAlbStack);

    const expectedRemovalPolicy =
      config.LogRemovalPolicyParam?.removalPolicy === cdk.RemovalPolicy.DESTROY ? 'Delete' : 'Retain';

    template.hasResource('AWS::S3::Bucket', {
      DeletionPolicy: expectedRemovalPolicy,
      Properties: {
        LifecycleConfiguration: {
          Rules: Match.arrayWith([
            Match.objectLike({
              Status: 'Enabled',
            }),
          ]),
        },
      },
    });
  });

  // Test different configuration scenarios
  describe('Configuration Scenarios', () => {
    test('Should create minimal WebACL When only basic configuration is provided', () => {
      const minimalStack = new WafAlbStack(app, `${pjPrefix}-WafAlb-Minimal`, {
        scope: 'REGIONAL',
        wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
        env: getProcEnv(),
      });

      const template = Template.fromStack(minimalStack);

      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Scope: 'REGIONAL',
        DefaultAction: { Allow: {} },
      });

      // Should still have AWS managed rules by default
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'AWSManagedRulesCommonRuleSet',
          }),
        ]),
      });
    });

    test('Should create WebACL without associations When associations are not provided', () => {
      const stackWithoutAssociations = new WafAlbStack(app, `${pjPrefix}-WafAlb-NoAssoc`, {
        scope: 'REGIONAL',
        wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
        env: getProcEnv(),
      });

      const template = Template.fromStack(stackWithoutAssociations);

      template.resourceCountIs('AWS::WAFv2::WebACLAssociation', 0);
    });

    test('Should create multiple WebACL associations When multiple ALB ARNs are provided', () => {
      const multiAssociationStack = new WafAlbStack(app, `${pjPrefix}-WafAlb-MultiAssoc`, {
        scope: 'REGIONAL',
        associations: [
          'arn:aws:elasticloadbalancing:ap-northeast-1:123456789012:loadbalancer/app/test-alb-1/1234567890123456',
          'arn:aws:elasticloadbalancing:ap-northeast-1:123456789012:loadbalancer/app/test-alb-2/1234567890123457',
        ],
        wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
        env: getProcEnv(),
      });

      const template = Template.fromStack(multiAssociationStack);

      template.resourceCountIs('AWS::WAFv2::WebACLAssociation', 2);
    });
  });

  // Test security and compliance
  describe('Security and Compliance', () => {
    test('Should block public access to WAF log bucket When WAF ALB Stack is deployed', () => {
      const template = Template.fromStack(wafAlbStack);

      template.hasResourceProperties('AWS::S3::Bucket', {
        PublicAccessBlockConfiguration: {
          BlockPublicAcls: true,
          BlockPublicPolicy: true,
          IgnorePublicAcls: true,
          RestrictPublicBuckets: true,
        },
      });
    });

    test('Should encrypt WAF log bucket When WAF ALB Stack is deployed', () => {
      const template = Template.fromStack(wafAlbStack);

      template.hasResourceProperties('AWS::S3::Bucket', {
        BucketEncryption: {
          ServerSideEncryptionConfiguration: [
            {
              ServerSideEncryptionByDefault: {
                SSEAlgorithm: 'AES256',
              },
            },
          ],
        },
      });
    });

    test('Should enable CloudWatch metrics for all rules When WAF ALB Stack is deployed', () => {
      const template = Template.fromStack(wafAlbStack);

      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            VisibilityConfig: {
              CloudWatchMetricsEnabled: true,
              SampledRequestsEnabled: true,
            },
          }),
        ]),
        VisibilityConfig: {
          CloudWatchMetricsEnabled: true,
          SampledRequestsEnabled: true,
        },
      });
    });
  });

  // Test rule priorities
  describe('Rule Priorities', () => {
    test('Should assign correct priorities to different rule types When all rules are enabled', () => {
      const template = Template.fromStack(wafAlbStack);

      // CSIRT rules should have highest priority (0, 1)
      if (config.CSIRTWAFParamALB?.isUseCSIRTManageRules) {
        template.hasResourceProperties('AWS::WAFv2::WebACL', {
          Rules: Match.arrayWith([
            Match.objectLike({
              Name: 'CSIRTBlockSpecificIPs',
              Priority: 0,
            }),
            Match.objectLike({
              Name: 'CSIRTManagerRules',
              Priority: 1,
            }),
          ]),
        });
      }

      // AWS managed rules should have priorities 2-6
      template.hasResourceProperties('AWS::WAFv2::WebACL', {
        Rules: Match.arrayWith([
          Match.objectLike({
            Name: 'AWSManagedRulesCommonRuleSet',
            Priority: 2,
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesKnownBadInputsRuleSet',
            Priority: 3,
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesAmazonIpReputationList',
            Priority: 4,
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesLinuxRuleSet',
            Priority: 5,
          }),
          Match.objectLike({
            Name: 'AWSManagedRulesSQLiRuleSet',
            Priority: 6,
          }),
        ]),
      });

      // Custom rules should have priorities 11-12
      if (config.WafAlbParam.allowIPList) {
        template.hasResourceProperties('AWS::WAFv2::WebACL', {
          Rules: Match.arrayWith([
            Match.objectLike({
              Name: 'IPset',
              Priority: 11,
            }),
          ]),
        });
      }

      if (config.WafAlbParam.preSharedKey) {
        template.hasResourceProperties('AWS::WAFv2::WebACL', {
          Rules: Match.arrayWith([
            Match.objectLike({
              Name: 'preSharedKey',
              Priority: 12,
            }),
          ]),
        });
      }
    });
  });

  // Test S3 policy creation and duplication prevention
  describe('S3 Policy Management', () => {
    test('Should create exactly one S3 bucket policy When WAF construct is instantiated', () => {
      const template = Template.fromStack(wafAlbStack);

      // Should create exactly one S3 bucket policy
      template.resourceCountIs('AWS::S3::BucketPolicy', 1);

      // Should create exactly one S3 bucket
      template.resourceCountIs('AWS::S3::Bucket', 1);
    });

    test('Should create S3 bucket policy with correct structure When WAF construct is created', () => {
      const template = Template.fromStack(wafAlbStack);

      template.hasResourceProperties('AWS::S3::BucketPolicy', {
        PolicyDocument: {
          Statement: Match.arrayWith([
            Match.objectLike({
              Effect: 'Allow',
              Action: Match.arrayWith([
                's3:PutBucketPolicy',
                's3:GetBucket*',
                's3:List*',
                's3:DeleteObject*',
              ]),
              Principal: {
                AWS: Match.objectLike({
                  'Fn::GetAtt': Match.arrayWith([
                    Match.stringLikeRegexp('.*CustomS3AutoDeleteObjectsCustomResourceProviderRole.*'),
                    'Arn',
                  ]),
                }),
              },
              Resource: Match.arrayWith([
                Match.objectLike({
                  'Fn::GetAtt': Match.arrayWith([
                    Match.stringLikeRegexp('.*WafLogsBucket.*'),
                    'Arn',
                  ]),
                }),
                Match.objectLike({
                  'Fn::Join': Match.arrayWith([
                    '',
                    Match.arrayWith([
                      Match.objectLike({
                        'Fn::GetAtt': Match.arrayWith([
                          Match.stringLikeRegexp('.*WafLogsBucket.*'),
                          'Arn',
                        ]),
                      }),
                      '/*',
                    ]),
                  ]),
                }),
              ]),
            }),
          ]),
          Version: '2012-10-17',
        },
      });
    });

    test('Should create logging configuration with dependency on bucket policy When bucket policy exists', () => {
      const template = Template.fromStack(wafAlbStack);

      // Should create logging configuration
      template.resourceCountIs('AWS::WAFv2::LoggingConfiguration', 1);

      // Logging configuration should reference the bucket
      template.hasResourceProperties('AWS::WAFv2::LoggingConfiguration', {
        LogDestinationConfigs: [
          Match.objectLike({
            'Fn::GetAtt': Match.arrayWith([
              Match.stringLikeRegexp('.*WafLogsBucket.*'),
              'Arn',
            ]),
          }),
        ],
        ResourceArn: Match.objectLike({
          'Fn::GetAtt': Match.arrayWith([
            Match.stringLikeRegexp('.*WebAcl.*'),
            'Arn',
          ]),
        }),
      });
    });

    test('Should not create duplicate S3 bucket policies When multiple WAF constructs exist in different stacks', () => {
      // Create first WAF stack
      const firstStack = new WafAlbStack(app, `${pjPrefix}-WafAlb-First`, {
        scope: 'REGIONAL',
        associations: ['arn:aws:elasticloadbalancing:ap-northeast-1:123456789012:loadbalancer/app/test-alb-1/1234567890123456'],
        env: getProcEnv(),
        crossRegionReferences: true,
        ...config.WafAlbParam,
        wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
        csirtWAFParam: config.CSIRTWAFParamALB,
      });

      // Create second WAF stack
      const secondStack = new WafAlbStack(app, `${pjPrefix}-WafAlb-Second`, {
        scope: 'REGIONAL',
        associations: ['arn:aws:elasticloadbalancing:ap-northeast-1:123456789012:loadbalancer/app/test-alb-2/1234567890123457'],
        env: getProcEnv(),
        crossRegionReferences: true,
        ...config.WafAlbParam,
        wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
        csirtWAFParam: config.CSIRTWAFParamALB,
      });

      const template1 = Template.fromStack(firstStack);
      const template2 = Template.fromStack(secondStack);

      // Each stack should have exactly one bucket and one bucket policy
      template1.resourceCountIs('AWS::S3::Bucket', 1);
      template1.resourceCountIs('AWS::S3::BucketPolicy', 1);

      template2.resourceCountIs('AWS::S3::Bucket', 1);
      template2.resourceCountIs('AWS::S3::BucketPolicy', 1);
    });

    test('Should create S3 bucket with unique name When multiple WAF stacks are deployed', () => {
      const firstStack = new WafAlbStack(app, `${pjPrefix}-WafAlb-Unique1`, {
        scope: 'REGIONAL',
        env: getProcEnv(),
        wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
      });

      const secondStack = new WafAlbStack(app, `${pjPrefix}-WafAlb-Unique2`, {
        scope: 'REGIONAL',
        env: getProcEnv(),
        wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
      });

      const template1 = Template.fromStack(firstStack);
      const template2 = Template.fromStack(secondStack);

      // Both should have buckets with different names based on stack name
      template1.hasResourceProperties('AWS::S3::Bucket', {
        BucketName: Match.stringLikeRegexp('.*-wafalb-unique1-.*'),
      });

      template2.hasResourceProperties('AWS::S3::Bucket', {
        BucketName: Match.stringLikeRegexp('.*-wafalb-unique2-.*'),
      });
    });
  });
});
