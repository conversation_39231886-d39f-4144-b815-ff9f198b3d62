import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { CloudfrontStack } from '../lib/stack/cloudfront-stack';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

let app: any;
let mockStack: cdk.Stack;
let mockVpc: ec2.Vpc;

beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';
  cdk.Tags.of(app).add(envTagName, envKey);

  // Create shared mock infrastructure once with explicit region
  mockStack = new cdk.Stack(app, 'MockStack', {
    env: getProcEnv(),
    crossRegionReferences: true,
  });

  // Create shared mock VPC using the new ipAddresses syntax
  mockVpc = new ec2.Vpc(mockStack, 'MockVpc', {
    maxAzs: 2,
    ipAddresses: ec2.IpAddresses.cidr('10.0.0.0/16'),
  });
});

describe(`${pjPrefix} CloudFront Stack`, () => {
  let cloudfrontStack: CloudfrontStack;
  let mockAlb: elbv2.ApplicationLoadBalancer;
  let mockWebAcl: wafv2.CfnWebACL;

  beforeEach(() => {
    // Create mock ALB using shared VPC
    mockAlb = new elbv2.ApplicationLoadBalancer(mockStack, 'MockAlb', {
      vpc: mockVpc,
      internetFacing: true,
    });

    // Create mock WebACL
    mockWebAcl = new wafv2.CfnWebACL(mockStack, 'MockWebAcl', {
      scope: 'CLOUDFRONT',
      defaultAction: { allow: {} },
      visibilityConfig: {
        cloudWatchMetricsEnabled: true,
        metricName: 'MockWebAcl',
        sampledRequestsEnabled: true,
      },
      rules: [],
    });

    cloudfrontStack = new CloudfrontStack(app, `${pjPrefix}-Cloudfront`, {
      pjPrefix: pjPrefix,
      webAcl: mockWebAcl,
      cloudFrontParam: config.CloudFrontParam,
      CertificateIdentifier: config.CertificateIdentifier,
      appAlbs: [mockAlb],
      preSharedKey: 'test-pre-shared-key',
      accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
      env: getProcEnv(),
      crossRegionReferences: true,
    });
  });

  test('Should snapshot matching When CloudFront Stack is created', () => {
    const template = Template.fromStack(cloudfrontStack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  test('Should define cfDistributionId property When CloudFront Stack is instantiated', () => {
    expect(cloudfrontStack.cfDistributionId).toBeDefined();
    expect(typeof cloudfrontStack.cfDistributionId).toBe('string');
  });

  test('Should create CloudFront Distribution When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        Enabled: true,
        DefaultRootObject: '/',
        HttpVersion: 'http2',
      },
    });

    template.resourceCountIs('AWS::CloudFront::Distribution', 1);
  });

  test('Should create ALB origin When createClosedBucket is false', () => {
    const template = Template.fromStack(cloudfrontStack);

    // Check if CloudFrontParam has valid FQDN and CertificateIdentifier
    // to determine the expected OriginProtocolPolicy
    const hasValidFqdn = config.CloudFrontParam.fqdn !== '';
    const hasValidCertificate = config.CertificateIdentifier.identifier !== '';
    const sslEnabled = hasValidFqdn && hasValidCertificate;
    const expectedProtocolPolicy = sslEnabled ? 'https-only' : 'http-only';

    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        Origins: Match.arrayWith([
          Match.objectLike({
            DomainName: Match.anyValue(),
            // OriginPath is optional and may be missing in the real template
            CustomOriginConfig: Match.objectLike({
              // HTTPPort and HTTPSPort may be undefined in the template but we expect OriginProtocolPolicy
              OriginProtocolPolicy: expectedProtocolPolicy,
            }),
            OriginCustomHeaders: [
              {
                HeaderName: 'x-pre-shared-key',
                HeaderValue: 'test-pre-shared-key',
              },
            ],
          }),
        ]),
      },
    });
  });

  test('Should create S3 web content bucket When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    const resources = template.findResources('AWS::S3::Bucket');
    const webBuckets = Object.keys(resources).filter((id) => /WebBucket/.test(id));
    expect(webBuckets.length).toBe(1);
  });

  test('Should create S3 closed bucket When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    const resources = template.findResources('AWS::S3::Bucket');
    const closedBuckets = Object.keys(resources).filter((id) => /ClosedBucket/.test(id));
    expect(closedBuckets.length).toBe(1);
    // Should have multiple S3 buckets including closed bucket
    template.resourcePropertiesCountIs(
      'AWS::S3::Bucket',
      {
        AccessControl: 'Private',
        BucketEncryption: {
          ServerSideEncryptionConfiguration: [
            {
              ServerSideEncryptionByDefault: {
                SSEAlgorithm: 'AES256',
              },
            },
          ],
        },
        PublicAccessBlockConfiguration: {
          BlockPublicAcls: true,
          BlockPublicPolicy: true,
          IgnorePublicAcls: true,
          RestrictPublicBuckets: true,
        },
        VersioningConfiguration: {
          Status: 'Enabled',
        },
      },
      2,
    ); // Web content bucket and closed bucket
  });

  test('Should create CloudFront access logs bucket When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    const resources = template.findResources('AWS::S3::Bucket');
    const cloudFrontLogBuckets = Object.keys(resources).filter((id) => /CloudFrontLogBucket/.test(id));
    expect(cloudFrontLogBuckets.length).toBe(1);

    // Verify the CloudFront log bucket has the correct properties
    const cloudFrontLogBucketId = cloudFrontLogBuckets[0];
    expect(resources[cloudFrontLogBucketId].Properties).toEqual(
      expect.objectContaining({
        AccessControl: 'Private',
        BucketEncryption: {
          ServerSideEncryptionConfiguration: [
            {
              ServerSideEncryptionByDefault: {
                SSEAlgorithm: 'AES256',
              },
            },
          ],
        },
        PublicAccessBlockConfiguration: {
          BlockPublicAcls: true,
          BlockPublicPolicy: true,
          IgnorePublicAcls: true,
          RestrictPublicBuckets: true,
        },
        OwnershipControls: {
          Rules: [
            {
              ObjectOwnership: 'ObjectWriter',
            },
          ],
        },
      }),
    );
  });

  test('Should configure CloudFront logging When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        Logging: {
          Bucket: Match.anyValue(),
          IncludeCookies: true,
          Prefix: 'CloudFrontAccessLogs/',
        },
      },
    });
  });

  test('Should attach WebACL When webAcl is provided', () => {
    const template = Template.fromStack(cloudfrontStack);

    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        WebACLId: Match.anyValue(),
      },
    });
  });

  test('Should configure static content behavior When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        CacheBehaviors: Match.arrayWith([
          Match.objectLike({
            PathPattern: '/static/*',
            ViewerProtocolPolicy: 'redirect-to-https',
            CachePolicyId: Match.anyValue(),
            TargetOriginId: Match.anyValue(),
          }),
        ]),
      },
    });
  });

  test('Should configure error responses When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        CustomErrorResponses: [
          {
            ErrorCode: 403,
            ResponseCode: 403,
            ResponsePagePath: '/static/sorry.html',
            ErrorCachingMinTTL: 20,
          },
        ],
      },
    });
  });

  test('Should configure cache policies for different content types When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        DefaultCacheBehavior: {
          CachePolicyId: Match.anyValue(),
          OriginRequestPolicyId: Match.anyValue(),
          ViewerProtocolPolicy: 'redirect-to-https',
          AllowedMethods: ['GET', 'HEAD', 'OPTIONS', 'PUT', 'PATCH', 'POST', 'DELETE'],
        },
      },
    });
  });

  test('Should create Origin Access Control for S3 buckets When CloudFront Stack is deployed', () => {
    const template = Template.fromStack(cloudfrontStack);

    template.hasResourceProperties('AWS::CloudFront::OriginAccessControl', {
      OriginAccessControlConfig: {
        Name: Match.anyValue(),
        OriginAccessControlOriginType: 's3',
        SigningBehavior: 'always',
        SigningProtocol: 'sigv4',
      },
    });
  });

  test('Should apply removal policies to S3 buckets When removal policy parameters are configured', () => {
    const template = Template.fromStack(cloudfrontStack);

    const expectedRemovalPolicy =
      config.LogRemovalPolicyParam?.removalPolicy === cdk.RemovalPolicy.DESTROY ? 'Delete' : 'Retain';

    // Check that at least one S3 bucket has the expected removal policy
    template.hasResource('AWS::S3::Bucket', {
      DeletionPolicy: expectedRemovalPolicy,
    });
  });

  test('Should apply lifecycle rules to access logs bucket When lifecycle rules are configured', () => {
    const template = Template.fromStack(cloudfrontStack);

    const expectedRules = config.s3AuditLogLifecycleRules.map((rule) => {
      const cfRule: any = { Status: 'Enabled' };

      if (rule.expiration) {
        cfRule.ExpirationInDays = rule.expiration.toDays();
      }

      if (rule.abortIncompleteMultipartUploadAfter) {
        cfRule.AbortIncompleteMultipartUpload = {
          DaysAfterInitiation: rule.abortIncompleteMultipartUploadAfter.toDays(),
        };
      }

      return cfRule;
    });

    // Check if any S3 bucket has lifecycle configuration with expected rules
    template.hasResourceProperties('AWS::S3::Bucket', {
      LifecycleConfiguration: {
        Rules: expectedRules,
      },
    });
  });

  // Test different configuration scenarios
  describe('Configuration Scenarios', () => {
    test('Should create closed bucket origin When createClosedBucket is true', () => {
      const closedBucketStack = new CloudfrontStack(app, `${pjPrefix}-Cloudfront-Closed`, {
        pjPrefix: pjPrefix,
        webAcl: mockWebAcl,
        cloudFrontParam: {
          ...config.CloudFrontParam,
          createClosedBucket: true,
        },
        CertificateIdentifier: config.CertificateIdentifier,
        appAlbs: [mockAlb],
        preSharedKey: 'test-pre-shared-key',
        accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
        webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
        env: getProcEnv(),
      });

      const template = Template.fromStack(closedBucketStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          Origins: Match.arrayWith([
            Match.objectLike({
              S3OriginConfig: Match.anyValue(),
              OriginAccessControlId: Match.anyValue(),
            }),
          ]),
        },
      });
    });

    test('Should configure HTTPS origin When SSL is enabled', () => {
      const sslStack = new CloudfrontStack(app, `${pjPrefix}-Cloudfront-SSL`, {
        pjPrefix: pjPrefix,
        webAcl: mockWebAcl,
        cloudFrontParam: {
          ...config.CloudFrontParam,
          fqdn: 'example.com',
        },
        CertificateIdentifier: {
          identifier: '12345678-1234-1234-1234-123456789012',
        },
        appAlbs: [mockAlb],
        preSharedKey: 'test-pre-shared-key',
        accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
        webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
        env: getProcEnv(),
      });

      const template = Template.fromStack(sslStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          Aliases: ['example.com'],
          ViewerCertificate: {
            AcmCertificateArn: Match.anyValue(),
            MinimumProtocolVersion: 'TLSv1.2_2021',
            SslSupportMethod: 'sni-only',
          },
          Origins: Match.arrayWith([
            Match.objectLike({
              CustomOriginConfig: {
                OriginProtocolPolicy: 'https-only',
              },
            }),
          ]),
        },
      });
    });

    test('Should create CloudFront without WebACL When webAcl is not provided', () => {
      const noWafStack = new CloudfrontStack(app, `${pjPrefix}-Cloudfront-NoWAF`, {
        pjPrefix: pjPrefix,
        cloudFrontParam: config.CloudFrontParam,
        CertificateIdentifier: config.CertificateIdentifier,
        appAlbs: [mockAlb],
        preSharedKey: 'test-pre-shared-key',
        accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
        webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
        env: getProcEnv(),
      });

      const template = Template.fromStack(noWafStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          WebACLId: Match.absent(),
        },
      });
    });

    test('Should create CloudFront without pre-shared key headers When preSharedKey is not provided', () => {
      const noKeyStack = new CloudfrontStack(app, `${pjPrefix}-Cloudfront-NoKey`, {
        pjPrefix: pjPrefix,
        webAcl: mockWebAcl,
        cloudFrontParam: config.CloudFrontParam,
        CertificateIdentifier: config.CertificateIdentifier,
        appAlbs: [mockAlb],
        accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
        webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
        env: getProcEnv(),
      });

      const template = Template.fromStack(noKeyStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          Origins: Match.arrayWith([
            Match.objectLike({
              OriginCustomHeaders: Match.absent(),
            }),
          ]),
        },
      });
    });

    test('Should create minimal CloudFront When only required parameters are provided', () => {
      const minimalStack = new CloudfrontStack(app, `${pjPrefix}-Cloudfront-Minimal`, {
        pjPrefix: pjPrefix,
        cloudFrontParam: {
          fqdn: '',
          createClosedBucket: false,
        },
        CertificateIdentifier: {
          identifier: '',
        },
        appAlbs: [mockAlb],
        accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
        env: getProcEnv(),
      });

      const template = Template.fromStack(minimalStack);

      // Check only for Enabled property since ViewerCertificate may be set differently than expected
      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          Enabled: true,
        },
      });

      // Then verify specific properties separately
      const cfDistributionResources = template.findResources('AWS::CloudFront::Distribution');
      const hasDistributionWithoutAlias = Object.values(cfDistributionResources).some((resource) => {
        const distConfig = resource.Properties.DistributionConfig;
        return !distConfig.Aliases;
      });
      expect(hasDistributionWithoutAlias).toBe(true);
    });
  });

  // Test security and compliance
  describe('Security and Compliance', () => {
    test('Should block public access to all S3 buckets When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      // All S3 buckets should have public access blocked
      template.hasResourceProperties('AWS::S3::Bucket', {
        PublicAccessBlockConfiguration: {
          BlockPublicAcls: true,
          BlockPublicPolicy: true,
          IgnorePublicAcls: true,
          RestrictPublicBuckets: true,
        },
      });
    });

    test('Should encrypt all S3 buckets When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      template.hasResourceProperties('AWS::S3::Bucket', {
        BucketEncryption: {
          ServerSideEncryptionConfiguration: [
            {
              ServerSideEncryptionByDefault: {
                SSEAlgorithm: 'AES256',
              },
            },
          ],
        },
      });
    });

    test('Should enforce SSL for all S3 buckets When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      // Should create bucket policies that enforce SSL
      template.hasResourceProperties('AWS::S3::BucketPolicy', {
        PolicyDocument: {
          Statement: Match.arrayWith([
            Match.objectLike({
              Effect: 'Deny',
              Condition: {
                Bool: {
                  'aws:SecureTransport': 'false',
                },
              },
            }),
          ]),
        },
      });
    });

    test('Should enable versioning for web content buckets When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      template.hasResourceProperties('AWS::S3::Bucket', {
        VersioningConfiguration: {
          Status: 'Enabled',
        },
      });
    });

    test('Should redirect HTTP to HTTPS When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          DefaultCacheBehavior: {
            ViewerProtocolPolicy: 'redirect-to-https',
          },
          CacheBehaviors: Match.arrayWith([
            Match.objectLike({
              ViewerProtocolPolicy: 'redirect-to-https',
            }),
          ]),
        },
      });
    });

    test('Should configure proper object ownership for log buckets When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      template.hasResourceProperties('AWS::S3::Bucket', {
        OwnershipControls: {
          Rules: [
            {
              ObjectOwnership: 'ObjectWriter',
            },
          ],
        },
      });
    });
  });

  // Test resource counting and validation
  describe('Resource Validation', () => {
    test('Should create expected number of S3 buckets When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      // Should create:
      // 1. Web content bucket
      // 2. Closed bucket
      // 3. CloudFront access logs bucket
      // 4. S3 access logs bucket
      template.resourceCountIs('AWS::S3::Bucket', 4);
    });

    test('Should create expected number of Origin Access Controls When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      // Should create at least 1 OAC for S3 origins
      template.resourceCountIs('AWS::CloudFront::OriginAccessControl', 1);
    });

    test('Should create expected number of bucket policies When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      // Should create policies for SSL enforcement and OAC access
      template.resourcePropertiesCountIs('AWS::S3::BucketPolicy', {}, 4);
    });

    test('Should validate CloudFront distribution configuration When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          Enabled: true,
          HttpVersion: 'http2',
          Origins: Match.arrayWith([
            Match.objectLike({
              DomainName: Match.anyValue(),
            }),
          ]),
          DefaultCacheBehavior: Match.objectLike({
            TargetOriginId: Match.anyValue(),
            ViewerProtocolPolicy: 'redirect-to-https',
          }),
        },
      });
    });
  });

  // Test behavior and cache policies
  describe('Cache and Behavior Configuration', () => {
    test('Should configure different cache policies for ALB and S3 origins When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          DefaultCacheBehavior: {
            CachePolicyId: Match.anyValue(),
          },
          CacheBehaviors: Match.arrayWith([
            Match.objectLike({
              PathPattern: '/static/*',
              CachePolicyId: Match.anyValue(),
            }),
          ]),
        },
      });
    });

    test('Should configure allowed methods for ALB origin When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          DefaultCacheBehavior: {
            AllowedMethods: ['GET', 'HEAD', 'OPTIONS', 'PUT', 'PATCH', 'POST', 'DELETE'],
          },
        },
      });
    });

    test('Should configure origin request policy for ALB origin When CloudFront Stack is deployed', () => {
      const template = Template.fromStack(cloudfrontStack);

      template.hasResourceProperties('AWS::CloudFront::Distribution', {
        DistributionConfig: {
          DefaultCacheBehavior: {
            OriginRequestPolicyId: Match.anyValue(),
          },
        },
      });
    });
  });
});
