import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { EcsAppStack } from '../lib/stack/ecs-app-stack';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

let app: any;

beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';
  cdk.Tags.of(app).add(envTagName, envKey);
});

describe(`${pjPrefix} ECS App Stack`, () => {
  let shareResources: ShareResourcesStack;
  let ecsStack: EcsAppStack;

  beforeEach(() => {
    // Create shared resources first
    shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });

    // Create ECS App Stack
    ecsStack = new EcsAppStack(app, `${pjPrefix}-ECS`, {
      myVpc: shareResources.myVpc,
      appKey: shareResources.appKey,
      alarmTopic: shareResources.alarmTopic,
      prefix: pjPrefix,
      albParam: config.AlbParam,
      albBgParam: config.AlbBgParam,
      ecsFrontTasks: config.EcsFrontTasks,
      ecsBackTasks: config.EcsBackTasks,
      ecsFrontBgTasks: config.EcsFrontBgTasks,
      ecsBackBgTasks: config.EcsBackBgTasks,
      env: getProcEnv(),
      crossRegionReferences: true,
      bastionParams: config.BastionParam,
      otherRemovalPolicyParam: config.OtherRemovalPolicyParam,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      albAccessLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      pipelineSourceBucketLifeCycleRules: config.pipelineSourceBucketLifeCycleRules,
    });
  });

  test('Should snapshot matching When ECS App Stack is created', () => {
    const template = Template.fromStack(ecsStack);
    expect(template.toJSON()).toMatchSnapshot();
  });

  test('Should define ECS App property When ECS App Stack is instantiated', () => {
    expect(ecsStack.app).toBeDefined();
    expect(ecsStack.app.ecsCommon).toBeDefined();
  });

  test('Should create ECS Cluster When ECS App Stack is deployed', () => {
    const template = Template.fromStack(ecsStack);

    template.hasResourceProperties('AWS::ECS::Cluster', {});
  });

  test('Should create correct number of ECR repositories When multiple ECS tasks are configured', () => {
    const template = Template.fromStack(ecsStack);

    // Calculate expected number of repositories based on config
    const expectedRepoCount =
      config.EcsFrontTasks.length +
      config.EcsBackTasks.length +
      config.EcsFrontBgTasks.length +
      config.EcsBackBgTasks.length +
      1; // +1 for bastion if enabled

    template.resourceCountIs('AWS::ECR::Repository', expectedRepoCount);
  });

  test('Should create ECR repositories with correct lifecycle rules When ECR lifecycle rules are configured', () => {
    const template = Template.fromStack(ecsStack);

    template.hasResourceProperties('AWS::ECR::Repository', {
      ImageScanningConfiguration: {
        ScanOnPush: true,
      },
      LifecyclePolicy: {
        LifecyclePolicyText: Match.stringLikeRegexp('.*imageCountMoreThan.*'),
      },
    });
  });

  test('Should create Application Load Balancer When frontend tasks are configured', () => {
    const template = Template.fromStack(ecsStack);

    template.hasResourceProperties('AWS::ElasticLoadBalancingV2::LoadBalancer', {
      Type: 'application',
      Scheme: 'internet-facing',
    });
  });

  test('Should create ALB Target Groups for each frontend task When frontend tasks are configured', () => {
    const template = Template.fromStack(ecsStack);

    // Assert - Check if frontend rolling deployment target groups exist
    if (config.EcsFrontTasks.length > 0) {
      // Frontend rolling deployment creates target groups with specific naming pattern
      const expectedTargetGroupCount = config.EcsFrontTasks.length;

      // Find all target groups in the template
      const targetGroups = template.findResources('AWS::ElasticLoadBalancingV2::TargetGroup');
      const frontendTargetGroups = Object.entries(targetGroups).filter(([logicalId, resource]) => {
        // Frontend rolling target groups follow the pattern: {prefix}FrontAlb{appName}TGTargetGroup{random}
        // Exclude Blue/Green target groups (they contain "Blue" or "Green" in their names)
        const isFrontendRolling =
          logicalId.includes('FrontAlb') &&
          logicalId.includes('TGTargetGroup') &&
          !logicalId.includes('Blue') &&
          !logicalId.includes('Green');
        return isFrontendRolling;
      });

      expect(frontendTargetGroups.length).toBe(expectedTargetGroupCount);

      // Verify target group properties for frontend tasks
      frontendTargetGroups.forEach(([logicalId, resource]) => {
        expect(resource.Properties).toMatchObject({
          TargetType: 'ip',
          Protocol: 'HTTP',
          Port: 80,
          TargetGroupAttributes: [
            {
              Key: 'deregistration_delay.timeout_seconds',
              Value: '30',
            },
            {
              Key: 'stickiness.enabled',
              Value: 'false',
            },
          ],
        });
      });
    }
  });

  test('Should create ALB Listener Rules with correct paths When custom paths are configured', () => {
    const template = Template.fromStack(ecsStack);

    // Check for listener rules with path patterns
    const tasksWithPaths = config.EcsFrontTasks.filter((task) => 'path' in task && task.path);
    if (tasksWithPaths.length > 0) {
      template.hasResourceProperties('AWS::ElasticLoadBalancingV2::ListenerRule', {
        Conditions: [
          {
            Field: 'path-pattern',
            PathPatternConfig: {
              Values: Match.anyValue(),
            },
          },
        ],
      });
    }
  });

  test('Should create CloudWatch Log Groups for ECS services When ECS tasks are configured', () => {
    const template = Template.fromStack(ecsStack);

    // Log groups are created without explicit names, so just check basic properties
    template.hasResourceProperties('AWS::Logs::LogGroup', {
      RetentionInDays: Match.anyValue(),
    });
  });

  test('Should create Security Groups with correct ingress rules When ALB and ECS are configured', () => {
    const template = Template.fromStack(ecsStack);

    // Check ALB security group - based on actual description pattern from error
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      GroupDescription: Match.stringLikeRegexp('.*SgAlb.*'),
      SecurityGroupIngress: [
        {
          IpProtocol: 'tcp',
          FromPort: 80,
          ToPort: 80,
          CidrIp: '0.0.0.0/0',
        },
      ],
    });

    // Check that security groups exist (some may not have ingress rules)
    template.hasResourceProperties('AWS::EC2::SecurityGroup', {
      GroupDescription: Match.anyValue(),
    });
  });

  test('Should create IAM roles with correct policies When ECS tasks require execution roles', () => {
    const template = Template.fromStack(ecsStack);

    // ECS Task Execution Role
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: {
        Statement: [
          {
            Effect: 'Allow',
            Principal: {
              Service: 'ecs-tasks.amazonaws.com',
            },
            Action: 'sts:AssumeRole',
          },
        ],
      },
      ManagedPolicyArns: [
        {
          'Fn::Join': [
            '',
            [
              'arn:',
              {
                Ref: 'AWS::Partition',
              },
              ':iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy',
            ],
          ],
        },
      ],
    });
  });

  test('Should create CodePipeline S3 buckets for frontend and backend When pipeline is configured', () => {
    const template = Template.fromStack(ecsStack);

    // Calculate expected number of pipeline buckets (source + artifacts)
    // Each ECS app gets 2 buckets: source bucket + artifacts bucket
    const totalEcsApps =
      config.EcsFrontTasks.length +
      config.EcsBackTasks.length +
      config.EcsFrontBgTasks.length +
      config.EcsBackBgTasks.length;

    const expectedPipelineBuckets = totalEcsApps * 2; // source + artifacts for each app

    // Source buckets have versioning enabled and lifecycle rules
    template.hasResourceProperties('AWS::S3::Bucket', {
      VersioningConfiguration: {
        Status: 'Enabled',
      },
      LifecycleConfiguration: {
        Rules: Match.arrayWith([
          Match.objectLike({
            Status: 'Enabled',
            NoncurrentVersionExpiration: Match.objectLike({
              NewerNoncurrentVersions: 20,
              NoncurrentDays: 30,
            }),
          }),
        ]),
      },
    });

    // Artifacts buckets have KMS encryption
    template.hasResourceProperties('AWS::S3::Bucket', {
      BucketEncryption: {
        ServerSideEncryptionConfiguration: [
          {
            ServerSideEncryptionByDefault: {
              SSEAlgorithm: 'aws:kms',
              KMSMasterKeyID: Match.anyValue(),
            },
          },
        ],
      },
    });
  });

  test('Should create ALB access log S3 buckets When ALB is configured for frontend and backend', () => {
    const template = Template.fromStack(ecsStack);

    // ALB access log buckets are created for:
    // 1. Frontend Rolling ALB (if frontend tasks exist)
    // 2. Frontend Blue/Green ALB (if frontend BG tasks exist)
    // 3. Backend Blue/Green ALB (if backend BG tasks exist)

    let expectedAlbLogBuckets = 0;
    if (config.EcsFrontTasks.length > 0) expectedAlbLogBuckets++; // Frontend Rolling ALB
    if (config.EcsFrontBgTasks.length > 0) expectedAlbLogBuckets++; // Frontend Blue/Green ALB
    if (config.EcsBackBgTasks.length > 0) expectedAlbLogBuckets++; // Backend Blue/Green ALB

    if (expectedAlbLogBuckets > 0) {
      // ALB access log buckets have specific configuration
      template.hasResourceProperties('AWS::S3::Bucket', {
        AccessControl: 'Private',
        BucketEncryption: {
          ServerSideEncryptionConfiguration: [
            {
              ServerSideEncryptionByDefault: {
                SSEAlgorithm: 'AES256',
              },
            },
          ],
        },
        LifecycleConfiguration: {
          Rules: Match.arrayWith([
            Match.objectLike({
              ExpirationInDays: 1825, // 5 years retention
              Status: 'Enabled',
            }),
            Match.objectLike({
              AbortIncompleteMultipartUpload: {
                DaysAfterInitiation: 30,
              },
              Status: 'Enabled',
            }),
          ]),
        },
      });
    }
  });

  test('Should create CloudWatch Alarms for target group health When ALB target groups are created', () => {
    const template = Template.fromStack(ecsStack);

    template.hasResourceProperties('AWS::CloudWatch::Alarm', {
      ComparisonOperator: 'LessThanThreshold',
      EvaluationPeriods: 3,
      MetricName: 'HealthyHostCount',
      Namespace: 'AWS/ApplicationELB',
      Statistic: 'Average',
      Threshold: 1,
    });
  });

  test('Should create Bastion ECS resources When bastion tasks are enabled', () => {
    const template = Template.fromStack(ecsStack);

    // Check if bastion is enabled in config
    if (ecsStack.app.bastionApp) {
      // ECR repositories don't have explicit names, just check that bastion repo exists
      template.hasResourceProperties('AWS::ECR::Repository', {
        ImageScanningConfiguration: {
          ScanOnPush: true,
        },
      });
    }
  });

  test('Should configure Blue/Green deployment resources When Blue/Green tasks are configured', () => {
    const template = Template.fromStack(ecsStack);

    if (config.EcsFrontBgTasks.length > 0) {
      // Should create CodeDeploy application
      template.hasResourceProperties('AWS::CodeDeploy::Application', {
        ComputePlatform: 'ECS',
      });
    }
  });

  test('Should create resources with correct removal policy When removal policy is configured', () => {
    const template = Template.fromStack(ecsStack);

    if (config.LogRemovalPolicyParam?.removalPolicy === cdk.RemovalPolicy.DESTROY) {
      // Check that resources have DeletionPolicy set appropriately
      const resources = template.findResources('AWS::Logs::LogGroup');
      Object.values(resources).forEach((resource) => {
        expect(resource.DeletionPolicy).toBeDefined();
      });
    }
  });

  test('Should create correct number of frontend ECS apps When frontend tasks are configured', () => {
    expect(ecsStack.app.frontEcsApps).toBeDefined();
    expect(ecsStack.app.frontEcsApps.length).toBe(config.EcsFrontTasks.length);
  });

  test('Should create correct number of backend ECS apps When backend tasks are configured', () => {
    expect(ecsStack.app.backEcsApps).toBeDefined();
    expect(ecsStack.app.backEcsApps.length).toBe(config.EcsBackTasks.length);
  });

  test('Should configure ALB listener with HTTPS When certificate identifier is provided', () => {
    const template = Template.fromStack(ecsStack);

    if (config.AlbParam.identifier) {
      template.hasResourceProperties('AWS::ElasticLoadBalancingV2::Listener', {
        Protocol: 'HTTPS',
        Port: 443,
      });
    } else {
      template.hasResourceProperties('AWS::ElasticLoadBalancingV2::Listener', {
        Protocol: 'HTTP',
        Port: 80,
      });
    }
  });

  test('Should create ECS Service Connect namespace When backend services use service connect', () => {
    const template = Template.fromStack(ecsStack);

    if (config.EcsBackTasks.length > 0) {
      template.hasResourceProperties('AWS::ServiceDiscovery::PrivateDnsNamespace', {
        Name: Match.anyValue(),
      });
    }
  });

  test('Should enforce conditional branches correctly When different task configurations are provided', () => {
    // Test that conditional logic works correctly based on task configuration
    if (config.EcsFrontTasks.length > 0) {
      expect(ecsStack.app.frontAlb).toBeDefined();
    }

    if (config.EcsFrontBgTasks.length > 0) {
      expect(ecsStack.app.frontAlbBg).toBeDefined();
    }

    if (config.EcsBackBgTasks.length > 0) {
      expect(ecsStack.app.backendAlbBg).toBeDefined();
    }
  });

  test('Should validate that property overrides work correctly When custom parameters are passed', () => {
    // Verify that props are correctly passed to child constructs
    expect(ecsStack.app.ecsCommon.ecsCluster).toBeDefined();
    expect(ecsStack.app.ecsCommon.ecsTaskExecutionRole).toBeDefined();
    expect(ecsStack.app.ecsCommon.ecsNameSpace).toBeDefined();
  });

  test('Should create CloudWatch Alarms for monitoring When ECS services are configured', () => {
    const template = Template.fromStack(ecsStack);

    template.hasResourceProperties('AWS::CloudWatch::Alarm', {
      ComparisonOperator: Match.anyValue(),
      MetricName: Match.anyValue(),
    });
  });

  // test case property logRemovalPolicyParam undefined
  test('Should handle undefined logRemovalPolicyParam gracefully When not provided', () => {
    // Create ECS App Stack
    const ecsStack = new EcsAppStack(app, `${pjPrefix}-ECS-2`, {
      myVpc: shareResources.myVpc,
      appKey: shareResources.appKey,
      alarmTopic: shareResources.alarmTopic,
      prefix: pjPrefix,
      albParam: config.AlbParam,
      albBgParam: config.AlbBgParam,
      ecsFrontTasks: config.EcsFrontTasks,
      ecsBackTasks: config.EcsBackTasks,
      ecsFrontBgTasks: config.EcsFrontBgTasks,
      ecsBackBgTasks: config.EcsBackBgTasks,
      env: getProcEnv(),
      crossRegionReferences: true,
      // bastionParams: config.BastionParam,
      otherRemovalPolicyParam: config.OtherRemovalPolicyParam,
      // logRemovalPolicyParam: config.LogRemovalPolicyParam,
      albAccessLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      pipelineSourceBucketLifeCycleRules: config.pipelineSourceBucketLifeCycleRules,
    });
    const template = Template.fromStack(ecsStack);

    template.hasResource('AWS::S3::Bucket', {
      DeletionPolicy: "Retain",
    })
  });

  // test property httpFlag is false and albBgParam.identifier not empty
  test('Should create ALB listener with HTTP protocol When httpFlag is false and albBgParam.identifier is not empty', () => {

    const albBgParam = { ...config.AlbBgParam };
    albBgParam.identifier = 'test-alb-bg-identifier'; // Simulate a non-empty identifier

    const ecsStackWithHttpFalse = new EcsAppStack(app, `${pjPrefix}-ECS-HttpFalse`, {
      myVpc: shareResources.myVpc,
      appKey: shareResources.appKey,
      alarmTopic: shareResources.alarmTopic,
      prefix: pjPrefix,
      albParam: config.AlbParam,
      albBgParam: albBgParam,
      ecsFrontTasks: config.EcsFrontTasks,
      ecsBackTasks: config.EcsBackTasks,
      ecsFrontBgTasks: config.EcsFrontBgTasks,
      ecsBackBgTasks: config.EcsBackBgTasks,
      env: getProcEnv(),
      crossRegionReferences: true,
      // bastionParams: config.BastionParam,
      // otherRemovalPolicyParam: ,
      // logRemovalPolicyParam: config.LogRemovalPolicyParam,
      albAccessLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      pipelineSourceBucketLifeCycleRules: config.pipelineSourceBucketLifeCycleRules,
    });

    const template = Template.fromStack(ecsStackWithHttpFalse);

    template.hasResourceProperties('AWS::ElasticLoadBalancingV2::Listener', {
      Protocol: 'HTTP',
    });
  });

});
