name: InfraResources_RunTest

env:
  envkey: prod #-c environmentにて指定した環境名。
on:
  pull_request:
    branches: ['マージ先ブランチ'] #branchは環境に合わせて変更が必要
    paths: ['lib/**', 'bin/**', 'params/**'] #トリガー対象ファイル指定箇所
jobs:
  aws_cdk:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: install
        run: npm install

      - name: cdk ls
        run: npx cdk ls -c environment=${{env.envkey}}

      - name: Run Test
        run: npm test
