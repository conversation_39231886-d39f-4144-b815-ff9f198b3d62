# csys-infra-baseline-environment-on-aws-change-homemade

CDK 汎用テンプレート（blea をマイナビ用に改修）  
全体概要については、[docs](./docs/README.md)配下で説明する。こちらから読むことを推奨する。

## ディレクトリ構成概要

ルート配下のディレクトリ構成は以下の通り。

```
/
├─ .github/workflows/
├─ bin/
├─ container/
├─ docs/
├─ lib/
├─ params/
├─ test/
└─ README.md

```

- `.github/workflows/`

  GitHubAction で実行されるコードを管理

- `bin/`

  CDK のエントリーポイントのコードを管理

- `container/`

  踏み台コンテナや Docker ビルドのコードを管理

- `docs/`

  全体概要や各スタックの概要ドキュメントを管理

- `lib/`

  スタックとコンストラクトのコードを管理

- `params/`

  環境設定ファイルを管理

- `test/`

  テストコードを管理
