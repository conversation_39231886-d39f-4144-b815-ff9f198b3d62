<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1121px" height="1088px" viewBox="-0.5 -0.5 1121 1088" content="&lt;mxfile host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36&quot; version=&quot;24.8.4&quot; pages=&quot;5&quot;&gt;&#10;  &lt;diagram name=&quot;241113&quot; id=&quot;Y_9fSLMXNc_KC4PtQxbz&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1539&quot; dy=&quot;818&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-2&quot; value=&quot;index&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;810&quot; width=&quot;1080&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-3&quot; value=&quot;ecs-app-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;glass=0;fillColor=none;strokeColor=default;verticalAlign=bottom;labelPosition=center;verticalLabelPosition=top;align=center;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;780&quot; width=&quot;1120&quot; height=&quot;310&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-4&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;glass=0;dashed=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;950&quot; y=&quot;590&quot; width=&quot;190&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-5&quot; value=&quot;backup-resources&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;dashed=1;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;590&quot; width=&quot;520&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-6&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-7&quot; value=&quot;share-resources-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;20&quot; width=&quot;520&quot; height=&quot;340&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-8&quot; value=&quot;waf-cloudfront-stack and waf-alb-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;220&quot; width=&quot;360&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-9&quot; value=&quot;elasticache-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;220&quot; width=&quot;180&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-10&quot; value=&quot;monitor-stack&quot; style=&quot;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;990&quot; y=&quot;400&quot; width=&quot;150&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-11&quot; value=&quot;kms-key-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;230&quot; width=&quot;120&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-12&quot; value=&quot;cognito-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;230&quot; width=&quot;180&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-13&quot; value=&quot;chatbot-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;60&quot; width=&quot;100&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-14&quot; value=&quot;vpc-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;60&quot; width=&quot;360&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-15&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-16&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-17&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-18&quot; value=&quot;VPC&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.vpc;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-19&quot; value=&quot;ChatBot&amp;lt;br&amp;gt;Slack Client&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;450&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-20&quot; value=&quot;sns-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;230&quot; width=&quot;140&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-21&quot; value=&quot;Topic&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAABqElEQVRYR+1Yy5KEIBATxBcozP9/7SYZwLF2LytzsEpzIQZIWd1N++gePPgLtm+FzU7d9GrHnL1ivm5B3L1SE45eLtNzcI/Xv/B4/Q/38wpLC/zBqxnFK+TrFqzZy8z5XhtQm/RV4YaBgx0A3SxJT9KTGRBDojksfi+vc1hea2pB7KihTF6viQozu5HMlOhqSRZKK0giGSnJFaPnCHBnq9dHfW306jdgpBJAAslIiV6GRA/BBUQV4CjJCwbFa/ZewTkN671u+cJwzim1p2GcK0FC6C7Zv9YY27z6GFWNNwFir1OI1Dr37gWAsmzJWDD7XE92mONL6kcev3a2N/BWr5JHF2OkF1Ibo/oEqiSqF0yU6GVI1Cc8iHYOlN59Im+7CSa8oLfGvsTrmzXxTa8tJUmdBWjaGbJKJJHsEsm+fEipeN0BMSWG9zxcSuU8ImuXfHbg012pPQ98vWd2VfgQ2m6xD+HXuxy/6XXgUSVJfXWmxBNjSdRXVxBFeqSkpKHP1PNYvHhYG882N7R6aYpAUWisf2b0g4fEkKhgSCTVuX15ER48OKDrfgCPPT8Qh4k7owAAAABJRU5ErkJggg==;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;290&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-22&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;rounded=1;shadow=0;glass=0;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-23&quot; value=&quot;SecretManager&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-24&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-25&quot; value=&quot;pipeline-infraresources-stack&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#000000;strokeWidth=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;20&quot; width=&quot;560&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-26&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1060&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-27&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-28&quot; value=&quot;EventBridge Rule&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.rule_3;labelBorderColor=none;strokeWidth=1;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-29&quot; value=&quot;CodePipeline&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-30&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#4D72F3;strokeWidth=1;fillColor=none;dashed=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;220&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-31&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceArtifact)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-32&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-33&quot; value=&quot;waf-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;320&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-34&quot; value=&quot;WAF&amp;lt;br&amp;gt;(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;640&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-35&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(WafLogBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-36&quot; value=&quot;SSM&amp;lt;br&amp;gt;(Parameter Store)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.systems_manager;rounded=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-37&quot; value=&quot;cloudfront-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;590&quot; width=&quot;330&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-38&quot; value=&quot;S3&amp;lt;br&amp;gt;(静的Webページ)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-39&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelPosition=center;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;630&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-40&quot; value=&quot;S3&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;830&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-41&quot; value=&quot;db-aurora-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;400&quot; width=&quot;400&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-42&quot; value=&quot;aurora-alarm-construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;430&quot; width=&quot;140&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-43&quot; value=&quot;aurora-mysql-constructs&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#4D72F3;fillColor=none;dashed=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;430&quot; width=&quot;200&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-44&quot; value=&quot;Postgre SQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-45&quot; value=&quot;MySQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-46&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;455&quot; width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-47&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.topic;rounded=1;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;310&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-48&quot; value=&quot;CloudWatch Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1045&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-49&quot; value=&quot;openserch-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;400&quot; width=&quot;390&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-50&quot; value=&quot;opensearch-serverless-construct&quot; style=&quot;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-51&quot; value=&quot;OpenSearch Serverless&amp;lt;br&amp;gt;(Collection)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;labelBackgroundColor=default;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;660&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-52&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;475&quot; width=&quot;30&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-53&quot; value=&quot;opensearch-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-54&quot; value=&quot;OpenSearch&amp;lt;br&amp;gt;(Domain)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;810&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-55&quot; value=&quot;EBS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_block_store;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;890&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-57&quot; value=&quot;efs-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;400&quot; width=&quot;100&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-58&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;rounded=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-59&quot; value=&quot;&quot; style=&quot;group&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-60&quot; value=&quot;backup-vault-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-59&quot;&gt;&#10;          &lt;mxGeometry width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-61&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;rounded=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-59&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-62&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-63&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;220&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-64&quot; value=&quot;Backup Plan&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.backup_plan;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-65&quot; value=&quot;Stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;610&quot; width=&quot;150&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-66&quot; value=&quot;Construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;680&quot; width=&quot;150&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-67&quot; value=&quot;&quot; style=&quot;group;strokeColor=none;rounded=0;&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;840&quot; width=&quot;220&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-68&quot; value=&quot;Bastion Container&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-67&quot;&gt;&#10;          &lt;mxGeometry width=&quot;220&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-69&quot; value=&quot;bastion-ecs-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-67&quot;&gt;&#10;          &lt;mxGeometry x=&quot;18.34&quot; y=&quot;89.42999999999999&quot; width=&quot;183.33&quot; height=&quot;31.15&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-70&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: right;&amp;quot;&amp;gt;BlueGreen Deploy&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;840&quot; width=&quot;240&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-71&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;950&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-72&quot; value=&quot;ecs-service-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;855&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-73&quot; value=&quot;pipeline-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;1000&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-74&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;900&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-75&quot; value=&quot;common construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;840&quot; width=&quot;240&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-76&quot; value=&quot;ecs-commmon-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;855&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-77&quot; value=&quot;ecs-app-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;950&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-78&quot; value=&quot;ecs-task-role-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;1000&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-79&quot; value=&quot;alb-target-group-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;900&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-80&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: left;&amp;quot;&amp;gt;Ecspresso (Rolling)&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;840&quot; width=&quot;220&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-81&quot; value=&quot;alb-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;855&quot; width=&quot;180&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;l7UZtRPf9y5K1fjJFRUz-82&quot; value=&quot;pipeline-ecspresso-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;900&quot; width=&quot;180&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7N-v3ZshiFUB6YnmFjTH-0&quot; value=&quot;ElastiCache&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticache;&quot; vertex=&quot;1&quot; parent=&quot;l7UZtRPf9y5K1fjJFRUz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1031&quot; y=&quot;261&quot; width=&quot;38&quot; height=&quot;38&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;CgUVZCBrnEe4v-NpkoFL&quot; name=&quot;241003&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1539&quot; dy=&quot;818&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-1&quot; value=&quot;index&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;810&quot; width=&quot;1080&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-2&quot; value=&quot;ecs-app-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;glass=0;fillColor=none;strokeColor=default;verticalAlign=bottom;labelPosition=center;verticalLabelPosition=top;align=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;780&quot; width=&quot;1120&quot; height=&quot;310&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-3&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;glass=0;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;950&quot; y=&quot;590&quot; width=&quot;190&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-4&quot; value=&quot;backup-resources&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;dashed=1;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;590&quot; width=&quot;520&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-5&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-6&quot; value=&quot;share-resources-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;20&quot; width=&quot;520&quot; height=&quot;340&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-7&quot; value=&quot;waf-cloudfront-stack and waf-alb-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;220&quot; width=&quot;360&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-8&quot; value=&quot;elasticache-redis-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;220&quot; width=&quot;180&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-9&quot; value=&quot;monitor-stack&quot; style=&quot;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;990&quot; y=&quot;400&quot; width=&quot;150&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-10&quot; value=&quot;kms-key-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;230&quot; width=&quot;120&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-11&quot; value=&quot;cognito-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;230&quot; width=&quot;180&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-12&quot; value=&quot;chatbot-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;60&quot; width=&quot;100&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-13&quot; value=&quot;vpc-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;60&quot; width=&quot;360&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-14&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-15&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-16&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-17&quot; value=&quot;VPC&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.vpc;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-18&quot; value=&quot;ChatBot&amp;lt;br&amp;gt;Slack Client&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;450&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-19&quot; value=&quot;sns-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;230&quot; width=&quot;140&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-20&quot; value=&quot;Topic&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAABqElEQVRYR+1Yy5KEIBATxBcozP9/7SYZwLF2LytzsEpzIQZIWd1N++gePPgLtm+FzU7d9GrHnL1ivm5B3L1SE45eLtNzcI/Xv/B4/Q/38wpLC/zBqxnFK+TrFqzZy8z5XhtQm/RV4YaBgx0A3SxJT9KTGRBDojksfi+vc1hea2pB7KihTF6viQozu5HMlOhqSRZKK0giGSnJFaPnCHBnq9dHfW306jdgpBJAAslIiV6GRA/BBUQV4CjJCwbFa/ZewTkN671u+cJwzim1p2GcK0FC6C7Zv9YY27z6GFWNNwFir1OI1Dr37gWAsmzJWDD7XE92mONL6kcev3a2N/BWr5JHF2OkF1Ibo/oEqiSqF0yU6GVI1Cc8iHYOlN59Im+7CSa8oLfGvsTrmzXxTa8tJUmdBWjaGbJKJJHsEsm+fEipeN0BMSWG9zxcSuU8ImuXfHbg012pPQ98vWd2VfgQ2m6xD+HXuxy/6XXgUSVJfXWmxBNjSdRXVxBFeqSkpKHP1PNYvHhYG882N7R6aYpAUWisf2b0g4fEkKhgSCTVuX15ER48OKDrfgCPPT8Qh4k7owAAAABJRU5ErkJggg==;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;290&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-21&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;rounded=1;shadow=0;glass=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-22&quot; value=&quot;SecretManager&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-23&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-24&quot; value=&quot;pipeline-infraresources-stack&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#000000;strokeWidth=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;20&quot; width=&quot;560&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-25&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1060&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-26&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-27&quot; value=&quot;EventBridge Rule&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.rule_3;labelBorderColor=none;strokeWidth=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-28&quot; value=&quot;CodePipeline&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-29&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#4D72F3;strokeWidth=1;fillColor=none;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;220&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-30&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceArtifact)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-31&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-32&quot; value=&quot;waf-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;320&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-33&quot; value=&quot;WAF&amp;lt;br&amp;gt;(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;640&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-34&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(WafLogBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-35&quot; value=&quot;SSM&amp;lt;br&amp;gt;(Parameter Store)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.systems_manager;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-36&quot; value=&quot;cloudfront-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;590&quot; width=&quot;330&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-37&quot; value=&quot;S3&amp;lt;br&amp;gt;(静的Webページ)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-38&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;630&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-39&quot; value=&quot;S3&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;830&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-40&quot; value=&quot;db-aurora-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;400&quot; width=&quot;400&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-41&quot; value=&quot;aurora-alarm-construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;430&quot; width=&quot;140&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-42&quot; value=&quot;aurora-mysql-constructs&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#4D72F3;fillColor=none;dashed=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;430&quot; width=&quot;200&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-43&quot; value=&quot;Postgre SQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-44&quot; value=&quot;MySQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-45&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;455&quot; width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-46&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.topic;rounded=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;310&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-47&quot; value=&quot;CloudWatch Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1045&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-48&quot; value=&quot;openserch-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;400&quot; width=&quot;390&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-49&quot; value=&quot;opensearch-serverless-construct&quot; style=&quot;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-50&quot; value=&quot;OpenSearch Serverless&amp;lt;br&amp;gt;(Collection)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;labelBackgroundColor=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;660&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-51&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;475&quot; width=&quot;30&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-52&quot; value=&quot;opensearch-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-53&quot; value=&quot;OpenSearch&amp;lt;br&amp;gt;(Domain)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;810&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-54&quot; value=&quot;EBS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_block_store;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;890&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-55&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1030&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-56&quot; value=&quot;efs-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;400&quot; width=&quot;100&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-57&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-58&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-59&quot; value=&quot;backup-vault-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;DEDEXJI9tsqX11ivmkCv-58&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-60&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;rounded=1;&quot; parent=&quot;DEDEXJI9tsqX11ivmkCv-58&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-61&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-62&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;220&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-63&quot; value=&quot;Backup Plan&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.backup_plan;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-64&quot; value=&quot;Stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;610&quot; width=&quot;150&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-65&quot; value=&quot;Construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;680&quot; width=&quot;150&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-66&quot; value=&quot;&quot; style=&quot;group;strokeColor=none;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;840&quot; width=&quot;220&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-67&quot; value=&quot;Bastion Container&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;DEDEXJI9tsqX11ivmkCv-66&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;220&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-68&quot; value=&quot;bastion-ecs-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;DEDEXJI9tsqX11ivmkCv-66&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;18.34&quot; y=&quot;89.42999999999999&quot; width=&quot;183.33&quot; height=&quot;31.15&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-69&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: right;&amp;quot;&amp;gt;BlueGreen Deploy&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;840&quot; width=&quot;240&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-70&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;950&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-71&quot; value=&quot;ecs-service-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;855&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-72&quot; value=&quot;pipeline-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;1000&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-73&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;900&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-74&quot; value=&quot;common construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;840&quot; width=&quot;240&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-75&quot; value=&quot;ecs-commmon-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;855&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-76&quot; value=&quot;ecs-app-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;950&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-77&quot; value=&quot;ecs-task-role-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;1000&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-78&quot; value=&quot;alb-target-group-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;900&quot; width=&quot;200&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-79&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: left;&amp;quot;&amp;gt;Ecspresso (Rolling)&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;840&quot; width=&quot;220&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-80&quot; value=&quot;alb-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;855&quot; width=&quot;180&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;DEDEXJI9tsqX11ivmkCv-81&quot; value=&quot;pipeline-ecspresso-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;900&quot; width=&quot;180&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram name=&quot;240903&quot; id=&quot;VPox2ViZbSH5ekP2EALR&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1250&quot; dy=&quot;646&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-1&quot; parent=&quot;cWrA636gBAvGh26CeUxP-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-2&quot; value=&quot;ecs-app-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;glass=0;fillColor=none;strokeColor=default;verticalAlign=bottom;labelPosition=center;verticalLabelPosition=top;align=center;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;780&quot; width=&quot;1120&quot; height=&quot;310&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-3&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;glass=0;dashed=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;950&quot; y=&quot;590&quot; width=&quot;190&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-4&quot; value=&quot;backup-resources&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;dashed=1;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;590&quot; width=&quot;520&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-5&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-6&quot; value=&quot;share-resources-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;20&quot; width=&quot;520&quot; height=&quot;340&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-7&quot; value=&quot;waf-cloudfront-stack and waf-alb-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;220&quot; width=&quot;360&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-8&quot; value=&quot;elasticache-redis-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;220&quot; width=&quot;180&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-9&quot; value=&quot;monitor-stack&quot; style=&quot;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;990&quot; y=&quot;400&quot; width=&quot;150&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-10&quot; value=&quot;kms-key-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;230&quot; width=&quot;120&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-11&quot; value=&quot;cognito-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;230&quot; width=&quot;180&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-12&quot; value=&quot;chatbot-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;60&quot; width=&quot;100&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-13&quot; value=&quot;vpc-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;60&quot; width=&quot;360&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-14&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-15&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-16&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-17&quot; value=&quot;VPC&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.vpc;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-18&quot; value=&quot;ChatBot&amp;lt;br&amp;gt;Slack Client&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;450&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-19&quot; value=&quot;sns-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;230&quot; width=&quot;140&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-20&quot; value=&quot;Topic&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAABqElEQVRYR+1Yy5KEIBATxBcozP9/7SYZwLF2LytzsEpzIQZIWd1N++gePPgLtm+FzU7d9GrHnL1ivm5B3L1SE45eLtNzcI/Xv/B4/Q/38wpLC/zBqxnFK+TrFqzZy8z5XhtQm/RV4YaBgx0A3SxJT9KTGRBDojksfi+vc1hea2pB7KihTF6viQozu5HMlOhqSRZKK0giGSnJFaPnCHBnq9dHfW306jdgpBJAAslIiV6GRA/BBUQV4CjJCwbFa/ZewTkN671u+cJwzim1p2GcK0FC6C7Zv9YY27z6GFWNNwFir1OI1Dr37gWAsmzJWDD7XE92mONL6kcev3a2N/BWr5JHF2OkF1Ibo/oEqiSqF0yU6GVI1Cc8iHYOlN59Im+7CSa8oLfGvsTrmzXxTa8tJUmdBWjaGbJKJJHsEsm+fEipeN0BMSWG9zxcSuU8ImuXfHbg012pPQ98vWd2VfgQ2m6xD+HXuxy/6XXgUSVJfXWmxBNjSdRXVxBFeqSkpKHP1PNYvHhYG882N7R6aYpAUWisf2b0g4fEkKhgSCTVuX15ER48OKDrfgCPPT8Qh4k7owAAAABJRU5ErkJggg==;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;290&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-21&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;rounded=1;shadow=0;glass=0;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-22&quot; value=&quot;SecretManager&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-23&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-24&quot; value=&quot;pipeline-infraresources-stack&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#000000;strokeWidth=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;20&quot; width=&quot;560&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-25&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1060&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-26&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-27&quot; value=&quot;EventBridge Rule&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.rule_3;labelBorderColor=none;strokeWidth=1;aspect=fixed;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-28&quot; value=&quot;CodePipeline&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-29&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#4D72F3;strokeWidth=1;fillColor=none;dashed=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;220&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-30&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceArtifact)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-31&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-32&quot; value=&quot;waf-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;320&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-33&quot; value=&quot;WAF&amp;lt;br&amp;gt;(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;640&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-34&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(WafLogBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-35&quot; value=&quot;SSM&amp;lt;br&amp;gt;(Parameter Store)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.systems_manager;rounded=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-36&quot; value=&quot;cloudfront-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;590&quot; width=&quot;330&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-37&quot; value=&quot;S3&amp;lt;br&amp;gt;(静的Webページ)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=none;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-38&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelPosition=center;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;630&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-39&quot; value=&quot;S3&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;830&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-40&quot; value=&quot;db-aurora-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;400&quot; width=&quot;400&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-41&quot; value=&quot;aurora-alarm-construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;430&quot; width=&quot;140&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-42&quot; value=&quot;aurora-mysql-constructs&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#4D72F3;fillColor=none;dashed=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;430&quot; width=&quot;200&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-43&quot; value=&quot;Postgre SQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-44&quot; value=&quot;MySQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-45&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;455&quot; width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-46&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.topic;rounded=1;aspect=fixed;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;310&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-47&quot; value=&quot;CloudWatch Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1045&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-48&quot; value=&quot;openserch-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;400&quot; width=&quot;390&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-49&quot; value=&quot;opensearch-serverless-construct&quot; style=&quot;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-50&quot; value=&quot;OpenSearch Serverless&amp;lt;br&amp;gt;(Collection)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;labelBackgroundColor=default;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;660&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-51&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;475&quot; width=&quot;30&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-52&quot; value=&quot;opensearch-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-53&quot; value=&quot;OpenSearch&amp;lt;br&amp;gt;(Domain)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;810&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-54&quot; value=&quot;EBS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_block_store;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;890&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-55&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;aspect=fixed;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1030&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-56&quot; value=&quot;efs-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;400&quot; width=&quot;100&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-57&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;rounded=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-58&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-59&quot; value=&quot;backup-vault-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-58&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-60&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;rounded=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-58&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-79&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-80&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;220&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-81&quot; value=&quot;Backup Plan&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.backup_plan;aspect=fixed;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-82&quot; value=&quot;Stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;610&quot; width=&quot;150&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-83&quot; value=&quot;Construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;680&quot; width=&quot;150&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Yj0bgSpV8ueiHmLJIPwU-3&quot; value=&quot;&quot; style=&quot;group;strokeColor=none;rounded=0;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;810&quot; width=&quot;240&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-62&quot; value=&quot;Bastion Container&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;Yj0bgSpV8ueiHmLJIPwU-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;240&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-75&quot; value=&quot;bastion-ecs-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;Yj0bgSpV8ueiHmLJIPwU-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;110&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Yj0bgSpV8ueiHmLJIPwU-1&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: right;&amp;quot;&amp;gt;BlueGreen Deploy&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;810&quot; width=&quot;240&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-70&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;950&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-72&quot; value=&quot;ecs-service-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;830&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-73&quot; value=&quot;pipeline-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;1010&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-74&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;890&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Yj0bgSpV8ueiHmLJIPwU-2&quot; value=&quot;common construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;810&quot; width=&quot;240&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-65&quot; value=&quot;ecs-commmon-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;830&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-67&quot; value=&quot;ecs-app-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;950&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-68&quot; value=&quot;ecs-task-role-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;1010&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-71&quot; value=&quot;alb-target-group-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;890&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;Yj0bgSpV8ueiHmLJIPwU-0&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: left;&amp;quot;&amp;gt;Ecspresso (Rolling)&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;810&quot; width=&quot;240&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-66&quot; value=&quot;alb-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;830&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;cWrA636gBAvGh26CeUxP-69&quot; value=&quot;pipeline-ecspresso-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;cWrA636gBAvGh26CeUxP-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;890&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;a3esTxAqLWYiV7gjBtFO&quot; name=&quot;240823&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1184&quot; dy=&quot;482&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-1&quot; value=&quot;ecs-app-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;glass=0;fillColor=none;strokeColor=default;verticalAlign=bottom;labelPosition=center;verticalLabelPosition=top;align=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;780&quot; width=&quot;1120&quot; height=&quot;310&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-2&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;glass=0;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;950&quot; y=&quot;590&quot; width=&quot;190&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-3&quot; value=&quot;backup-resources&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;dashed=1;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;590&quot; width=&quot;520&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-4&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-5&quot; value=&quot;share-resources-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;20&quot; width=&quot;520&quot; height=&quot;340&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-6&quot; value=&quot;waf-cloudfront-stack and waf-alb-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;220&quot; width=&quot;360&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-7&quot; value=&quot;elasticache-redis-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;220&quot; width=&quot;180&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-8&quot; value=&quot;monitor-stack&quot; style=&quot;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;990&quot; y=&quot;400&quot; width=&quot;150&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-9&quot; value=&quot;kms-key-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;230&quot; width=&quot;120&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-10&quot; value=&quot;cognito-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;230&quot; width=&quot;180&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-11&quot; value=&quot;chatbot-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;60&quot; width=&quot;100&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-12&quot; value=&quot;vpc-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;60&quot; width=&quot;360&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-13&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-14&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-15&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-16&quot; value=&quot;VPC&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.vpc;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-17&quot; value=&quot;ChatBot&amp;lt;br&amp;gt;Slack Client&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;450&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-18&quot; value=&quot;sns-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;230&quot; width=&quot;140&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-19&quot; value=&quot;Topic&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAABqElEQVRYR+1Yy5KEIBATxBcozP9/7SYZwLF2LytzsEpzIQZIWd1N++gePPgLtm+FzU7d9GrHnL1ivm5B3L1SE45eLtNzcI/Xv/B4/Q/38wpLC/zBqxnFK+TrFqzZy8z5XhtQm/RV4YaBgx0A3SxJT9KTGRBDojksfi+vc1hea2pB7KihTF6viQozu5HMlOhqSRZKK0giGSnJFaPnCHBnq9dHfW306jdgpBJAAslIiV6GRA/BBUQV4CjJCwbFa/ZewTkN671u+cJwzim1p2GcK0FC6C7Zv9YY27z6GFWNNwFir1OI1Dr37gWAsmzJWDD7XE92mONL6kcev3a2N/BWr5JHF2OkF1Ibo/oEqiSqF0yU6GVI1Cc8iHYOlN59Im+7CSa8oLfGvsTrmzXxTa8tJUmdBWjaGbJKJJHsEsm+fEipeN0BMSWG9zxcSuU8ImuXfHbg012pPQ98vWd2VfgQ2m6xD+HXuxy/6XXgUSVJfXWmxBNjSdRXVxBFeqSkpKHP1PNYvHhYG882N7R6aYpAUWisf2b0g4fEkKhgSCTVuX15ER48OKDrfgCPPT8Qh4k7owAAAABJRU5ErkJggg==;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;290&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-20&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;rounded=1;shadow=0;glass=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-21&quot; value=&quot;SecretManager&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-22&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-23&quot; value=&quot;pipeline-infraresources-stack&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#000000;strokeWidth=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;20&quot; width=&quot;560&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-24&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1060&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-25&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-26&quot; value=&quot;EventBridge Rule&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.rule_3;labelBorderColor=none;strokeWidth=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-27&quot; value=&quot;CodePipeline&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-28&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#4D72F3;strokeWidth=1;fillColor=none;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;220&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-29&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceArtifact)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-30&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-31&quot; value=&quot;waf-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;320&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-32&quot; value=&quot;WAF&amp;lt;br&amp;gt;(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;640&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-33&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(WafLogBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-34&quot; value=&quot;SSM&amp;lt;br&amp;gt;(Parameter Store)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.systems_manager;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-35&quot; value=&quot;cloudfront-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;590&quot; width=&quot;330&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-36&quot; value=&quot;S3&amp;lt;br&amp;gt;(静的Webページ)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-37&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;630&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-38&quot; value=&quot;S3&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;830&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-39&quot; value=&quot;db-aurora-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;400&quot; width=&quot;400&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-40&quot; value=&quot;aurora-alarm-construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;430&quot; width=&quot;140&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-41&quot; value=&quot;aurora-mysql-constructs&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#4D72F3;fillColor=none;dashed=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;430&quot; width=&quot;200&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-42&quot; value=&quot;Postgre SQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-43&quot; value=&quot;MySQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-44&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;455&quot; width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-45&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.topic;rounded=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;310&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-46&quot; value=&quot;CloudWatch Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1045&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-47&quot; value=&quot;openserch-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;400&quot; width=&quot;390&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-48&quot; value=&quot;opensearch-serverless-construct&quot; style=&quot;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-49&quot; value=&quot;OpenSearch Serverless&amp;lt;br&amp;gt;(Collection)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;labelBackgroundColor=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;660&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-50&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;475&quot; width=&quot;30&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-51&quot; value=&quot;opensearch-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-52&quot; value=&quot;OpenSearch&amp;lt;br&amp;gt;(Domain)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;810&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-53&quot; value=&quot;EBS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_block_store;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;890&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-54&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1030&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-55&quot; value=&quot;efs-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;400&quot; width=&quot;100&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-56&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-57&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-58&quot; value=&quot;backup-vault-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-57&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-59&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;rounded=1;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-57&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;790&quot; width=&quot;1000&quot; height=&quot;280&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-61&quot; value=&quot;Bastion Container&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;20&quot; width=&quot;240&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-62&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=right;verticalAlign=bottom;spacingRight=75;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;20&quot; width=&quot;500&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-63&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=left;verticalAlign=bottom;spacingLeft=75;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;20&quot; width=&quot;500&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-64&quot; value=&quot;ecs-commmon-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;40&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-65&quot; value=&quot;alb-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-66&quot; value=&quot;ecs-app-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;160&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-67&quot; value=&quot;ecs-task-role-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;220&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-68&quot; value=&quot;pipeline-ecspresso-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;100&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-69&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; y=&quot;160&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-70&quot; value=&quot;alb-target-group-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;100&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-71&quot; value=&quot;ecs-service-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; y=&quot;40&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-72&quot; value=&quot;pipeline-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; y=&quot;220&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-73&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; y=&quot;100&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-74&quot; value=&quot;bastion-ecs-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;130&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-75&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: right;&amp;quot;&amp;gt;BlueGreen Deploy&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;strokeColor=none;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; width=&quot;200&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-76&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: left;&amp;quot;&amp;gt;Ecspresso (Rolling)&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;strokeColor=none;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; width=&quot;200&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-77&quot; value=&quot;common construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;strokeColor=none;&quot; parent=&quot;qGONpFFLTG2bY7EXF1-D-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; width=&quot;200&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-78&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-79&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=0;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;220&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-80&quot; value=&quot;Backup Plan&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.backup_plan;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-81&quot; value=&quot;Stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=0;fillColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;610&quot; width=&quot;150&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;qGONpFFLTG2bY7EXF1-D-82&quot; value=&quot;Construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;970&quot; y=&quot;680&quot; width=&quot;150&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;ZrsNI20kWgCvFhXFdsRh&quot; name=&quot;240822&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1125&quot; dy=&quot;458&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;96&quot; value=&quot;backup-resources&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;dashed=1;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;590&quot; width=&quot;520&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;71&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;16&quot; value=&quot;share-resources-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;20&quot; width=&quot;520&quot; height=&quot;340&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;34&quot; value=&quot;waf-cloudfront-stack and waf-alb-stack&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;220&quot; width=&quot;360&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;56&quot; value=&quot;elasticache-redis-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;220&quot; width=&quot;180&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;47&quot; value=&quot;monitor-stack / dashboard-construct&quot; style=&quot;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;990&quot; y=&quot;400&quot; width=&quot;150&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;2&quot; value=&quot;kms-key-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;400&quot; y=&quot;230&quot; width=&quot;120&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3&quot; value=&quot;cognito-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;230&quot; width=&quot;180&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4&quot; value=&quot;chatbot-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;60&quot; width=&quot;100&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5&quot; value=&quot;vpc-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;container=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;60&quot; width=&quot;360&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;rounded=1;shadow=0;glass=0;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;9&quot; value=&quot;VPC&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.vpc;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;10&quot; value=&quot;ChatBot&amp;lt;br&amp;gt;Slack Client&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;450&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;11&quot; value=&quot;sns-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;glass=0;shadow=0;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;230&quot; width=&quot;140&quot; height=&quot;110&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;12&quot; value=&quot;Topic&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAABqElEQVRYR+1Yy5KEIBATxBcozP9/7SYZwLF2LytzsEpzIQZIWd1N++gePPgLtm+FzU7d9GrHnL1ivm5B3L1SE45eLtNzcI/Xv/B4/Q/38wpLC/zBqxnFK+TrFqzZy8z5XhtQm/RV4YaBgx0A3SxJT9KTGRBDojksfi+vc1hea2pB7KihTF6viQozu5HMlOhqSRZKK0giGSnJFaPnCHBnq9dHfW306jdgpBJAAslIiV6GRA/BBUQV4CjJCwbFa/ZewTkN671u+cJwzim1p2GcK0FC6C7Zv9YY27z6GFWNNwFir1OI1Dr37gWAsmzJWDD7XE92mONL6kcev3a2N/BWr5JHF2OkF1Ibo/oEqiSqF0yU6GVI1Cc8iHYOlN59Im+7CSa8oLfGvsTrmzXxTa8tJUmdBWjaGbJKJJHsEsm+fEipeN0BMSWG9zxcSuU8ImuXfHbg012pPQ98vWd2VfgQ2m6xD+HXuxy/6XXgUSVJfXWmxBNjSdRXVxBFeqSkpKHP1PNYvHhYG882N7R6aYpAUWisf2b0g4fEkKhgSCTVuX15ER48OKDrfgCPPT8Qh4k7owAAAABJRU5ErkJggg==;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;290&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;13&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;rounded=1;shadow=0;glass=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;14&quot; value=&quot;SecretManager&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;15&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;rounded=1;shadow=0;glass=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;17&quot; value=&quot;pipeline-infraresources-stack&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#000000;strokeWidth=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;20&quot; width=&quot;560&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;18&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1060&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;19&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;620&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;20&quot; value=&quot;EventBridge Rule&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.rule_3;labelBorderColor=none;strokeWidth=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;960&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;21&quot; value=&quot;CodePipeline&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;22&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBorderColor=none;strokeColor=#4D72F3;strokeWidth=1;fillColor=none;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;40&quot; width=&quot;220&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;23&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceArtifact)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;24&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;30&quot; value=&quot;waf-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;320&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;31&quot; value=&quot;WAF&amp;lt;br&amp;gt;(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;640&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;32&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(WafLogBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;740&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;33&quot; value=&quot;SSM&amp;lt;br&amp;gt;(Parameter Store)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.systems_manager;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;840&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;35&quot; value=&quot;cloudfront-stack / cloudfront-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;590&quot; y=&quot;590&quot; width=&quot;320&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;36&quot; value=&quot;S3&amp;lt;br&amp;gt;(静的Webページ)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;37&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelPosition=center;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;630&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;38&quot; value=&quot;S3&amp;lt;br&amp;gt;(Log)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;830&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;39&quot; value=&quot;db-aurora-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;400&quot; width=&quot;400&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;40&quot; value=&quot;aurora-alarm-construct&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#000000;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;430&quot; width=&quot;140&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;41&quot; value=&quot;aurora-mysql-constructs&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;strokeColor=#4D72F3;fillColor=none;dashed=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;430&quot; width=&quot;200&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;42&quot; value=&quot;Postgre SQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;43&quot; value=&quot;MySQL&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;180&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;44&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;455&quot; width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;45&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.topic;rounded=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;310&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;46&quot; value=&quot;CloudWatch Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1045&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;48&quot; value=&quot;openserch-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;400&quot; width=&quot;390&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;49&quot; value=&quot;opensearch-serverless-construct&quot; style=&quot;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;50&quot; value=&quot;OpenSearch Serverless&amp;lt;br&amp;gt;(Collection)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;labelBackgroundColor=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;660&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;51&quot; value=&quot;OR&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;475&quot; width=&quot;30&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;52&quot; value=&quot;opensearch-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;dashed=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;790&quot; y=&quot;430&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;53&quot; value=&quot;OpenSearch&amp;lt;br&amp;gt;(Domain)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;810&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;54&quot; value=&quot;EBS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_block_store;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;890&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;55&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1030&quot; y=&quot;260&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;57&quot; value=&quot;efs-stack / index&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;400&quot; width=&quot;100&quot; height=&quot;150&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;58&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;470&quot; y=&quot;450&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;65&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;220&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;66&quot; value=&quot;backup-plan-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;65&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;67&quot; value=&quot;Backup Plan&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.backup_plan;aspect=fixed;&quot; parent=&quot;65&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;75&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;630&quot; width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;76&quot; value=&quot;backup-vault-stack&quot; style=&quot;whiteSpace=wrap;html=1;strokeColor=default;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;75&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;120&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;77&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;rounded=1;&quot; parent=&quot;75&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;78&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;930&quot; width=&quot;1000&quot; height=&quot;280&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;79&quot; value=&quot;Bastion Container&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;20&quot; width=&quot;240&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;80&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=right;verticalAlign=bottom;spacingRight=75;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;20&quot; width=&quot;500&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;81&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=top;align=left;verticalAlign=bottom;spacingLeft=75;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;20&quot; width=&quot;500&quot; height=&quot;260&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;82&quot; value=&quot;ecs-commmon-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;40&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;83&quot; value=&quot;alb-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;84&quot; value=&quot;ecs-app-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;160&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;85&quot; value=&quot;ecs-task-role-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;220&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;86&quot; value=&quot;pipeline-ecspresso-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;100&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;87&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; y=&quot;160&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;88&quot; value=&quot;alb-target-group-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;100&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;89&quot; value=&quot;ecs-service-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; y=&quot;40&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;90&quot; value=&quot;pipeline-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; y=&quot;220&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;91&quot; value=&quot;alb-blue-green-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; y=&quot;100&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;92&quot; value=&quot;bastion-ecs-construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;130&quot; width=&quot;200&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;93&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: right;&amp;quot;&amp;gt;BlueGreen Deploy&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;strokeColor=none;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;520&quot; width=&quot;200&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;94&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: left;&amp;quot;&amp;gt;Ecspresso (Rolling)&amp;lt;/span&amp;gt;&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;strokeColor=none;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; width=&quot;200&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;95&quot; value=&quot;common construct&quot; style=&quot;whiteSpace=wrap;html=1;fillColor=none;rounded=1;labelPosition=center;verticalLabelPosition=middle;align=center;verticalAlign=middle;strokeColor=none;&quot; parent=&quot;78&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; width=&quot;200&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;70&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;650&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0"><stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0"><stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0"><stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0"><stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0"><stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/></linearGradient></defs><g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-0"><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-1"><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-2"><g><rect x="20" y="807" width="1080" height="260" rx="39" ry="39" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1078px; height: 1px; padding-top: 804px; margin-left: 21px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">index</div></div></div></foreignObject><text x="560" y="804" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">index</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-3"><g><rect x="0" y="777" width="1120" height="310" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1118px; height: 1px; padding-top: 774px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ecs-app-stack</div></div></div></foreignObject><text x="560" y="774" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ecs-app-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-4"><g><rect x="930" y="587" width="190" height="160" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-5"><g><rect x="0" y="587" width="520" height="160" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 518px; height: 1px; padding-top: 584px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">backup-resources</div></div></div></foreignObject><text x="260" y="584" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">backup-resources</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-6"><g><rect x="380" y="627" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 624px; margin-left: 381px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">backup-plan-stack</div></div></div></foreignObject><text x="440" y="624" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">backup-plan-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-7"><g><rect x="0" y="17" width="520" height="340" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 518px; height: 1px; padding-top: 14px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">share-resources-stack</div></div></div></foreignObject><text x="260" y="14" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">share-resources-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-8"><g><rect x="560" y="217" width="360" height="140" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 214px; margin-left: 561px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">waf-cloudfront-stack and waf-alb-stack</div></div></div></foreignObject><text x="740" y="214" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">waf-cloudfront-stack and waf-alb-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-9"><g><rect x="940" y="217" width="180" height="140" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 214px; margin-left: 941px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">elasticache-stack</div></div></div></foreignObject><text x="1030" y="214" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">elasticache-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-10"><g><rect x="970" y="397" width="150" height="150" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 394px; margin-left: 1045px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">monitor-stack</div></div></div></foreignObject><text x="1045" y="394" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">monitor-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-11"><g><rect x="380" y="227" width="120" height="110" rx="16.5" ry="16.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 224px; margin-left: 381px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">kms-key-construct</div></div></div></foreignObject><text x="440" y="224" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">kms-key-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-12"><g><rect x="30" y="227" width="180" height="110" rx="16.5" ry="16.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 224px; margin-left: 31px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">cognito-construct</div></div></div></foreignObject><text x="120" y="224" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">cognito-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-13"><g><rect x="400" y="57" width="100" height="110" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 54px; margin-left: 401px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">chatbot-construct</div></div></div></foreignObject><text x="450" y="54" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">chatbot-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-14"><g><rect x="20" y="57" width="360" height="110" rx="16.5" ry="16.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 54px; margin-left: 21px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">vpc-construct</div></div></div></foreignObject><text x="200" y="54" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">vpc-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-15"><g><path d="M 140 77 L 180 77 L 180 117 L 140 117 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 167.38 104.56 L 171.69 104.56 L 171.69 103.33 L 167.38 103.33 Z M 161.85 104.56 L 166.15 104.56 L 166.15 103.33 L 161.85 103.33 Z M 156.31 104.56 L 160.61 104.56 L 160.61 103.33 L 156.31 103.33 Z M 163.08 99.63 C 163.08 98.96 163.63 98.4 164.31 98.4 C 164.99 98.4 165.54 98.96 165.54 99.63 C 165.54 100.31 164.99 100.86 164.31 100.86 C 163.63 100.86 163.08 100.31 163.08 99.63 Z M 166.77 99.63 C 166.77 98.28 165.67 97.17 164.31 97.17 C 162.95 97.17 161.85 98.28 161.85 99.63 C 161.85 100.99 162.95 102.1 164.31 102.1 C 165.67 102.1 166.77 100.99 166.77 99.63 Z M 158.15 98.4 C 158.83 98.4 159.38 98.96 159.38 99.63 C 159.38 100.31 158.83 100.86 158.15 100.86 C 157.47 100.86 156.92 100.31 156.92 99.63 C 156.92 98.96 157.47 98.4 158.15 98.4 Z M 158.15 102.1 C 159.51 102.1 160.61 100.99 160.61 99.63 C 160.61 98.28 159.51 97.17 158.15 97.17 C 156.79 97.17 155.69 98.28 155.69 99.63 C 155.69 100.99 156.79 102.1 158.15 102.1 Z M 176 92.86 L 176 107.02 C 176 107.36 175.72 107.63 175.38 107.63 L 156.31 107.63 L 156.31 106.4 L 174.77 106.4 L 174.77 93.48 L 161.23 93.48 L 161.23 92.25 L 175.38 92.25 C 175.72 92.25 176 92.52 176 92.86 Z M 154.27 95.97 C 154.02 96.05 153.84 96.29 153.84 96.56 L 153.84 109.51 L 152.37 110.77 L 150.77 108.94 L 150.77 107.33 C 150.77 107.16 150.7 107.01 150.59 106.89 L 149.18 105.48 L 150.59 104.07 C 150.7 103.95 150.77 103.8 150.77 103.63 L 150.77 102.4 C 150.77 102.24 150.7 102.08 150.59 101.97 L 149.18 100.56 L 150.59 99.15 C 150.7 99.03 150.77 98.87 150.77 98.71 L 150.77 96.56 C 150.77 96.29 150.59 96.06 150.34 95.98 C 147.17 94.97 145.3 91.72 145.99 88.4 C 146.48 86.02 148.32 84.1 150.67 83.53 C 152.66 83.04 154.72 83.46 156.29 84.7 C 157.87 85.94 158.77 87.79 158.77 89.79 C 158.77 92.59 156.92 95.13 154.27 95.97 Z M 160 89.79 C 160 87.41 158.92 85.2 157.05 83.73 C 155.18 82.26 152.74 81.75 150.37 82.33 C 147.57 83.02 145.37 85.3 144.78 88.16 L 144.78 88.16 C 144 91.94 146.03 95.66 149.54 97 L 149.54 98.46 L 147.87 100.12 C 147.63 100.36 147.63 100.75 147.87 100.99 L 149.54 102.66 L 149.54 103.38 L 147.87 105.05 C 147.63 105.29 147.63 105.68 147.87 105.92 L 149.54 107.58 L 149.54 109.17 C 149.54 109.32 149.59 109.47 149.69 109.58 L 151.84 112.04 C 151.96 112.18 152.13 112.25 152.31 112.25 C 152.45 112.25 152.59 112.2 152.71 112.1 L 154.86 110.26 C 155 110.14 155.08 109.97 155.08 109.79 L 155.08 96.99 C 158 95.87 160 92.97 160 89.79 Z M 152.31 91.25 C 151.46 91.25 150.77 90.56 150.77 89.71 C 150.77 88.86 151.46 88.17 152.31 88.17 C 153.15 88.17 153.84 88.86 153.84 89.71 C 153.84 90.56 153.15 91.25 152.31 91.25 Z M 152.31 86.94 C 150.78 86.94 149.54 88.18 149.54 89.71 C 149.54 91.23 150.78 92.48 152.31 92.48 C 153.83 92.48 155.08 91.23 155.08 89.71 C 155.08 88.18 153.83 86.94 152.31 86.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 160px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">KMS Key</div></div></div></foreignObject><text x="160" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">KMS Key</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-16"><g><path d="M 220 77 L 260 77 L 260 117 L 220 117 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 251.94 98.65 L 252.16 97.11 C 254.18 98.32 254.21 98.82 254.21 98.83 C 254.2 98.84 253.86 99.13 251.94 98.65 Z M 250.83 98.34 C 247.33 97.29 242.46 95.05 240.49 94.12 C 240.49 94.11 240.49 94.11 240.49 94.1 C 240.49 93.34 239.88 92.72 239.12 92.72 C 238.36 92.72 237.75 93.34 237.75 94.1 C 237.75 94.85 238.36 95.47 239.12 95.47 C 239.45 95.47 239.75 95.35 239.99 95.15 C 242.31 96.25 247.14 98.45 250.67 99.49 L 249.27 109.32 C 249.27 109.35 249.27 109.37 249.27 109.4 C 249.27 110.27 245.43 111.86 239.17 111.86 C 232.84 111.86 228.97 110.27 228.97 109.4 C 228.97 109.37 228.97 109.35 228.97 109.32 L 226.05 88.06 C 228.57 89.8 233.99 90.71 239.18 90.71 C 244.35 90.71 249.76 89.8 252.29 88.07 Z M 225.75 85.84 C 225.79 85.09 230.11 82.14 239.18 82.14 C 248.24 82.14 252.56 85.09 252.6 85.84 L 252.6 86.1 C 252.11 87.79 246.51 89.57 239.18 89.57 C 231.83 89.57 226.23 87.78 225.75 86.09 Z M 253.75 85.86 C 253.75 83.88 248.07 81 239.18 81 C 230.28 81 224.6 83.88 224.6 85.86 L 224.66 86.29 L 227.83 109.44 C 227.9 112.03 234.81 113 239.17 113 C 244.59 113 250.34 111.76 250.41 109.45 L 251.78 99.79 C 252.54 99.97 253.17 100.07 253.67 100.07 C 254.35 100.07 254.8 99.9 255.08 99.57 C 255.31 99.3 255.4 98.97 255.33 98.62 C 255.18 97.83 254.24 96.98 252.33 95.89 L 253.69 86.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 240px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3 Bucket<br />(Log)</div></div></div></foreignObject><text x="240" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3 Buc...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-17"><g><rect x="300" y="77" width="40" height="40" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/><image x="299.5" y="76.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC" preserveAspectRatio="none"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px"><rect fill="rgb(255, 255, 255)" stroke="none" x="282" y="125" width="77" height="15" stroke-width="0"/><text x="319.5" y="134.5">VPC Endpoint</text></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-18"><g><path d="M 60 77 L 100 77 L 100 117 L 60 117 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/><path d="M 91.91 98.36 L 88.77 97.1 L 88.77 108.46 C 89.69 108.37 90.43 108.07 90.95 107.54 C 91.92 106.56 91.91 105.11 91.91 105.1 Z M 87.63 108.47 L 87.63 97.1 L 84.5 98.36 L 84.5 105.09 C 84.51 105.21 84.64 108.14 87.63 108.47 Z M 93.04 105.09 C 93.05 105.15 93.08 106.99 91.77 108.33 C 90.92 109.2 89.72 109.64 88.2 109.64 C 84.46 109.64 83.4 106.67 83.36 105.1 L 83.36 97.97 C 83.36 97.74 83.51 97.53 83.72 97.44 L 87.99 95.73 C 88.13 95.68 88.28 95.68 88.42 95.73 L 92.69 97.44 C 92.9 97.53 93.04 97.74 93.04 97.97 Z M 94.75 103.97 L 94.75 96.36 L 88.2 93.74 L 81.65 96.36 L 81.65 103.95 C 81.65 104 81.58 107.5 83.67 109.65 C 84.77 110.78 86.29 111.35 88.2 111.35 C 90.13 111.35 91.66 110.78 92.76 109.64 C 94.85 107.48 94.76 104 94.75 103.97 Z M 93.58 110.43 C 92.26 111.8 90.45 112.49 88.2 112.49 C 85.97 112.49 84.17 111.8 82.84 110.43 C 80.42 107.93 80.51 104.09 80.52 103.93 L 80.52 95.98 C 80.52 95.74 80.66 95.53 80.87 95.45 L 87.99 92.6 C 88.13 92.55 88.28 92.55 88.42 92.6 L 95.53 95.45 C 95.75 95.53 95.89 95.74 95.89 95.98 L 95.89 103.95 C 95.9 104.09 96 107.92 93.58 110.43 Z M 70.3 101.67 L 78.81 101.67 L 78.81 102.81 L 70.3 102.81 C 66.83 102.81 64.19 100.5 64.01 97.32 C 64 97.19 64 97.04 64 96.89 C 64 92.98 66.74 91.54 68.33 91.03 C 68.32 90.85 68.31 90.66 68.31 90.48 C 68.31 87.37 70.53 84.12 73.47 82.92 C 76.91 81.51 80.55 82.19 83.19 84.75 C 84.08 85.61 84.76 86.54 85.25 87.56 C 85.93 86.97 86.76 86.65 87.65 86.65 C 89.53 86.65 91.52 88.13 91.87 90.96 C 93.11 91.27 94.67 91.94 95.77 93.35 L 94.87 94.05 C 93.86 92.75 92.33 92.2 91.22 91.98 C 90.97 91.93 90.78 91.71 90.77 91.45 C 90.62 88.94 89.05 87.79 87.65 87.79 C 86.82 87.79 86.08 88.18 85.52 88.93 C 85.39 89.09 85.19 89.18 84.98 89.14 C 84.77 89.11 84.6 88.97 84.53 88.78 C 84.09 87.59 83.39 86.54 82.4 85.56 C 80.09 83.33 76.91 82.74 73.9 83.97 C 71.36 85.01 69.45 87.81 69.45 90.48 C 69.45 90.79 69.47 91.09 69.51 91.38 C 69.54 91.67 69.36 91.93 69.08 92 C 67.61 92.37 65.14 93.49 65.14 96.89 C 65.14 97.01 65.14 97.12 65.15 97.24 C 65.29 99.82 67.46 101.67 70.3 101.67 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 80px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">VPC</div></div></div></foreignObject><text x="80" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">VPC</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-19"><g><path d="M 430 77 L 470 77 L 470 117 L 430 117 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 448.6 87.18 L 450.84 87.18 L 450.84 86.05 L 448.6 86.05 Z M 444.67 87.18 L 446.91 87.18 L 446.91 86.05 L 444.67 86.05 Z M 440.74 87.18 L 442.98 87.18 L 442.98 86.05 L 440.74 86.05 Z M 464.15 103.09 C 463.98 103.49 463.62 103.79 463.19 103.93 L 463.19 100.76 C 463.53 100.88 463.83 101.1 464.04 101.41 C 464.36 101.9 464.4 102.51 464.15 103.09 Z M 435.85 103.09 C 435.6 102.51 435.64 101.9 435.96 101.41 C 436.17 101.1 436.47 100.88 436.81 100.76 L 436.81 103.93 C 436.38 103.79 436.02 103.49 435.85 103.09 Z M 464.98 100.79 C 464.56 100.16 463.92 99.74 463.19 99.59 L 463.19 98.59 C 463.19 96.63 461.68 95.04 459.82 95.04 L 449.16 95.04 L 449.16 96.16 L 459.82 96.16 C 461.06 96.16 462.07 97.25 462.07 98.59 L 462.07 106.08 C 462.07 107.42 461.06 108.51 459.82 108.51 L 440.18 108.51 C 438.94 108.51 437.93 107.42 437.93 106.08 L 437.93 98.59 C 437.93 97.25 438.94 96.16 440.18 96.16 L 442.98 96.16 L 442.98 95.04 L 440.18 95.04 C 438.32 95.04 436.81 96.63 436.81 98.59 L 436.81 99.59 C 436.08 99.74 435.44 100.16 435.02 100.79 C 434.49 101.6 434.41 102.6 434.82 103.54 C 435.17 104.35 435.92 104.92 436.81 105.09 L 436.81 106.08 C 436.81 108.04 438.32 109.63 440.18 109.63 L 446.91 109.63 L 446.91 113 L 448.04 113 L 448.04 109.63 L 451.4 109.63 L 451.4 113 L 452.53 113 L 452.53 109.63 L 459.82 109.63 C 461.68 109.63 463.19 108.04 463.19 106.08 L 463.19 105.09 C 464.08 104.92 464.83 104.35 465.18 103.54 C 465.59 102.6 465.51 101.6 464.98 100.79 Z M 445.23 102.33 C 445.23 103.26 444.47 104.02 443.54 104.02 C 442.62 104.02 441.86 103.26 441.86 102.33 C 441.86 101.4 442.62 100.65 443.54 100.65 C 444.47 100.65 445.23 101.4 445.23 102.33 Z M 440.74 102.33 C 440.74 103.88 442 105.14 443.54 105.14 C 445.09 105.14 446.35 103.88 446.35 102.33 C 446.35 100.79 445.09 99.53 443.54 99.53 C 442 99.53 440.74 100.79 440.74 102.33 Z M 454.77 102.33 C 454.77 101.4 455.53 100.65 456.46 100.65 C 457.38 100.65 458.14 101.4 458.14 102.33 C 458.14 103.26 457.38 104.02 456.46 104.02 C 455.53 104.02 454.77 103.26 454.77 102.33 Z M 459.26 102.33 C 459.26 100.79 458 99.53 456.46 99.53 C 454.91 99.53 453.65 100.79 453.65 102.33 C 453.65 103.88 454.91 105.14 456.46 105.14 C 458 105.14 459.26 103.88 459.26 102.33 Z M 438.49 82.6 C 438.49 82.34 438.71 82.12 438.97 82.12 L 453.09 82.12 L 453.09 91.17 C 453.09 91.44 452.86 91.67 452.59 91.67 L 446.91 91.67 C 446.6 91.67 446.35 91.92 446.35 92.23 L 446.35 95.14 L 443.87 91.89 C 443.76 91.75 443.6 91.67 443.42 91.67 L 438.97 91.67 C 438.71 91.67 438.49 91.45 438.49 91.19 Z M 438.97 92.79 L 443.15 92.79 L 446.47 97.14 C 446.57 97.28 446.74 97.36 446.91 97.36 C 446.97 97.36 447.03 97.35 447.09 97.33 C 447.32 97.26 447.47 97.04 447.47 96.8 L 447.47 92.79 L 452.59 92.79 C 453.48 92.79 454.21 92.06 454.21 91.17 L 454.21 81.86 C 454.21 81.38 453.83 81 453.35 81 L 438.97 81 C 438.09 81 437.37 81.72 437.37 82.6 L 437.37 91.19 C 437.37 92.07 438.09 92.79 438.97 92.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 450px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ChatBot<br />Slack Client</div></div></div></foreignObject><text x="450" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ChatBo...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-20"><g><rect x="220" y="227" width="140" height="110" rx="16.5" ry="16.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 224px; margin-left: 221px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">sns-construct</div></div></div></foreignObject><text x="290" y="224" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">sns-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-21"><g><image x="269.5" y="256.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAABqElEQVRYR+1Yy5KEIBATxBcozP9/7SYZwLF2LytzsEpzIQZIWd1N++gePPgLtm+FzU7d9GrHnL1ivm5B3L1SE45eLtNzcI/Xv/B4/Q/38wpLC/zBqxnFK+TrFqzZy8z5XhtQm/RV4YaBgx0A3SxJT9KTGRBDojksfi+vc1hea2pB7KihTF6viQozu5HMlOhqSRZKK0giGSnJFaPnCHBnq9dHfW306jdgpBJAAslIiV6GRA/BBUQV4CjJCwbFa/ZewTkN671u+cJwzim1p2GcK0FC6C7Zv9YY27z6GFWNNwFir1OI1Dr37gWAsmzJWDD7XE92mONL6kcev3a2N/BWr5JHF2OkF1Ibo/oEqiSqF0yU6GVI1Cc8iHYOlN59Im+7CSa8oLfGvsTrmzXxTa8tJUmdBWjaGbJKJJHsEsm+fEipeN0BMSWG9zxcSuU8ImuXfHbg012pPQ98vWd2VfgQ2m6xD+HXuxy/6XXgUSVJfXWmxBNjSdRXVxBFeqSkpKHP1PNYvHhYG882N7R6aYpAUWisf2b0g4fEkKhgSCTVuX15ER48OKDrfgCPPT8Qh4k7owAAAABJRU5ErkJggg==" preserveAspectRatio="none"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px"><rect fill="rgb(255, 255, 255)" stroke="none" x="276" y="305" width="30" height="15" stroke-width="0"/><text x="289.5" y="314.5">Topic</text></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-22"><g><path d="M 60 257 L 100 257 L 100 297 L 60 297 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 66.83 274.61 L 74.18 274.61 L 74.18 273.48 L 66.83 273.48 Z M 92.12 282.83 L 92.92 283.62 L 88.72 287.82 C 88.61 287.93 88.46 287.98 88.32 287.98 C 88.18 287.98 88.03 287.93 87.92 287.82 L 85.81 285.71 L 86.61 284.91 L 88.32 286.62 Z M 94.72 285.94 C 94.59 287 94.16 287.99 93.47 288.79 C 92.98 289.37 92.36 289.84 91.68 290.16 C 90.77 290.6 89.75 290.76 88.74 290.64 C 87.76 290.52 86.83 290.13 86.06 289.52 C 84.58 288.34 83.83 286.52 84.06 284.64 C 84.27 282.86 85.35 281.31 86.94 280.49 C 87.7 280.1 88.53 279.9 89.38 279.9 C 89.6 279.9 89.82 279.91 90.04 279.94 C 91.81 280.15 93.36 281.24 94.17 282.83 C 94.66 283.79 94.85 284.87 94.72 285.94 Z M 95.18 282.32 C 94.19 280.39 92.32 279.08 90.17 278.81 C 88.88 278.66 87.58 278.89 86.42 279.49 C 84.5 280.48 83.2 282.35 82.94 284.5 C 82.66 286.77 83.57 288.98 85.36 290.4 C 86.29 291.14 87.42 291.61 88.6 291.76 C 88.87 291.79 89.13 291.81 89.39 291.81 C 90.35 291.81 91.3 291.6 92.16 291.18 C 92.99 290.79 93.74 290.22 94.33 289.53 C 95.16 288.55 95.69 287.36 95.84 286.07 C 96 284.78 95.77 283.48 95.18 282.32 Z M 72.48 278 L 74.74 278 L 74.74 276.87 L 72.48 276.87 Z M 66.83 278 L 71.35 278 L 71.35 276.87 L 66.83 276.87 Z M 66.34 263.32 L 91.62 263.32 C 92.29 263.32 92.83 263.98 92.83 264.79 L 92.83 268.97 L 91.14 268.97 L 91.14 266.15 C 91.14 265.83 90.88 265.58 90.57 265.58 L 77.57 265.58 C 77.26 265.58 77 265.83 77 266.15 L 77 268.97 L 65.13 268.97 L 65.13 264.79 C 65.13 263.99 65.68 263.32 66.34 263.32 Z M 84.08 268.8 C 85.39 268.8 86.46 269.86 86.46 271.16 C 86.46 272.01 85.99 272.8 85.23 273.21 C 84.51 273.6 83.64 273.6 82.92 273.21 C 82.17 272.8 81.7 272.01 81.7 271.16 C 81.7 269.86 82.76 268.8 84.08 268.8 Z M 65.13 281.05 L 65.13 270.1 L 77 270.1 L 77 279.69 C 77 280.01 77.26 280.26 77.57 280.26 L 83.48 280.26 L 83.48 279.13 L 78.76 279.13 C 78.81 276.87 80.31 274.92 82.47 274.29 C 83.47 274.76 84.66 274.77 85.68 274.29 C 86.91 274.65 87.99 275.47 88.65 276.58 L 89.62 275.99 C 88.94 274.86 87.9 273.98 86.69 273.47 C 87.26 272.84 87.59 272.02 87.59 271.16 C 87.59 269.24 86.01 267.67 84.08 267.67 C 82.14 267.67 80.57 269.24 80.57 271.16 C 80.57 272.02 80.9 272.83 81.46 273.46 C 79.95 274.1 78.77 275.29 78.13 276.76 L 78.13 266.71 L 90.01 266.71 L 90.01 275.74 L 91.14 275.74 L 91.14 270.1 L 92.83 270.1 L 92.83 278.56 L 93.96 278.56 L 93.96 264.79 C 93.96 263.36 92.91 262.19 91.62 262.19 L 66.34 262.19 C 65.05 262.19 64 263.36 64 264.79 L 64 281.05 C 64 282.48 65.05 283.64 66.34 283.64 L 81.53 283.64 L 81.53 282.52 L 66.34 282.52 C 65.68 282.52 65.13 281.84 65.13 281.05 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 304px; margin-left: 80px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Cognito</div></div></div></foreignObject><text x="80" y="316" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Cognito</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-23"><g><image x="139.5" y="256.5" width="40" height="40" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px"><rect fill="rgb(255, 255, 255)" stroke="none" x="119" y="305" width="84" height="15" stroke-width="0"/><text x="159.5" y="314.5">SecretManager</text></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-24"><g><path d="M 420 257 L 460 257 L 460 297 L 420 297 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 447.38 284.56 L 451.69 284.56 L 451.69 283.33 L 447.38 283.33 Z M 441.85 284.56 L 446.15 284.56 L 446.15 283.33 L 441.85 283.33 Z M 436.31 284.56 L 440.61 284.56 L 440.61 283.33 L 436.31 283.33 Z M 443.08 279.63 C 443.08 278.96 443.63 278.4 444.31 278.4 C 444.99 278.4 445.54 278.96 445.54 279.63 C 445.54 280.31 444.99 280.86 444.31 280.86 C 443.63 280.86 443.08 280.31 443.08 279.63 Z M 446.77 279.63 C 446.77 278.28 445.67 277.17 444.31 277.17 C 442.95 277.17 441.85 278.28 441.85 279.63 C 441.85 280.99 442.95 282.1 444.31 282.1 C 445.67 282.1 446.77 280.99 446.77 279.63 Z M 438.15 278.4 C 438.83 278.4 439.38 278.96 439.38 279.63 C 439.38 280.31 438.83 280.86 438.15 280.86 C 437.47 280.86 436.92 280.31 436.92 279.63 C 436.92 278.96 437.47 278.4 438.15 278.4 Z M 438.15 282.1 C 439.51 282.1 440.61 280.99 440.61 279.63 C 440.61 278.28 439.51 277.17 438.15 277.17 C 436.79 277.17 435.69 278.28 435.69 279.63 C 435.69 280.99 436.79 282.1 438.15 282.1 Z M 456 272.86 L 456 287.02 C 456 287.36 455.72 287.63 455.38 287.63 L 436.31 287.63 L 436.31 286.4 L 454.77 286.4 L 454.77 273.48 L 441.23 273.48 L 441.23 272.25 L 455.38 272.25 C 455.72 272.25 456 272.52 456 272.86 Z M 434.27 275.97 C 434.02 276.05 433.84 276.29 433.84 276.56 L 433.84 289.51 L 432.37 290.77 L 430.77 288.94 L 430.77 287.33 C 430.77 287.16 430.7 287.01 430.59 286.89 L 429.18 285.48 L 430.59 284.07 C 430.7 283.95 430.77 283.8 430.77 283.63 L 430.77 282.4 C 430.77 282.24 430.7 282.08 430.59 281.97 L 429.18 280.56 L 430.59 279.15 C 430.7 279.03 430.77 278.87 430.77 278.71 L 430.77 276.56 C 430.77 276.29 430.59 276.06 430.34 275.98 C 427.17 274.97 425.3 271.72 425.99 268.4 C 426.48 266.02 428.32 264.1 430.67 263.53 C 432.66 263.04 434.72 263.46 436.29 264.7 C 437.87 265.94 438.77 267.79 438.77 269.79 C 438.77 272.59 436.92 275.13 434.27 275.97 Z M 440 269.79 C 440 267.41 438.92 265.2 437.05 263.73 C 435.18 262.26 432.74 261.75 430.37 262.33 C 427.57 263.02 425.37 265.3 424.78 268.16 L 424.78 268.16 C 424 271.94 426.03 275.66 429.54 277 L 429.54 278.46 L 427.87 280.12 C 427.63 280.36 427.63 280.75 427.87 280.99 L 429.54 282.66 L 429.54 283.38 L 427.87 285.05 C 427.63 285.29 427.63 285.68 427.87 285.92 L 429.54 287.58 L 429.54 289.17 C 429.54 289.32 429.59 289.47 429.69 289.58 L 431.84 292.04 C 431.96 292.18 432.13 292.25 432.31 292.25 C 432.45 292.25 432.59 292.2 432.71 292.1 L 434.86 290.26 C 435 290.14 435.08 289.97 435.08 289.79 L 435.08 276.99 C 438 275.87 440 272.97 440 269.79 Z M 432.31 271.25 C 431.46 271.25 430.77 270.56 430.77 269.71 C 430.77 268.86 431.46 268.17 432.31 268.17 C 433.15 268.17 433.84 268.86 433.84 269.71 C 433.84 270.56 433.15 271.25 432.31 271.25 Z M 432.31 266.94 C 430.78 266.94 429.54 268.18 429.54 269.71 C 429.54 271.23 430.78 272.48 432.31 272.48 C 433.83 272.48 435.08 271.23 435.08 269.71 C 435.08 268.18 433.83 266.94 432.31 266.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 304px; margin-left: 440px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">KMS Key</div></div></div></foreignObject><text x="440" y="316" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">KMS Key</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-25"><g><rect x="560" y="17" width="560" height="160" fill="none" stroke="#000000" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 558px; height: 1px; padding-top: 14px; margin-left: 561px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">pipeline-infraresources-stack</div></div></div></foreignObject><text x="840" y="14" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">pipeline-infraresources-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-26"><g><path d="M 1040 77 L 1080 77 L 1080 117 L 1040 117 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 1058.6 87.18 L 1060.84 87.18 L 1060.84 86.05 L 1058.6 86.05 Z M 1054.67 87.18 L 1056.91 87.18 L 1056.91 86.05 L 1054.67 86.05 Z M 1050.74 87.18 L 1052.98 87.18 L 1052.98 86.05 L 1050.74 86.05 Z M 1074.15 103.09 C 1073.98 103.49 1073.62 103.79 1073.19 103.93 L 1073.19 100.76 C 1073.53 100.88 1073.83 101.1 1074.04 101.41 C 1074.36 101.9 1074.4 102.51 1074.15 103.09 Z M 1045.85 103.09 C 1045.6 102.51 1045.64 101.9 1045.96 101.41 C 1046.17 101.1 1046.47 100.88 1046.81 100.76 L 1046.81 103.93 C 1046.38 103.79 1046.02 103.49 1045.85 103.09 Z M 1074.98 100.79 C 1074.56 100.16 1073.92 99.74 1073.19 99.59 L 1073.19 98.59 C 1073.19 96.63 1071.68 95.04 1069.82 95.04 L 1059.16 95.04 L 1059.16 96.16 L 1069.82 96.16 C 1071.06 96.16 1072.07 97.25 1072.07 98.59 L 1072.07 106.08 C 1072.07 107.42 1071.06 108.51 1069.82 108.51 L 1050.18 108.51 C 1048.94 108.51 1047.93 107.42 1047.93 106.08 L 1047.93 98.59 C 1047.93 97.25 1048.94 96.16 1050.18 96.16 L 1052.98 96.16 L 1052.98 95.04 L 1050.18 95.04 C 1048.32 95.04 1046.81 96.63 1046.81 98.59 L 1046.81 99.59 C 1046.08 99.74 1045.44 100.16 1045.02 100.79 C 1044.49 101.6 1044.41 102.6 1044.82 103.54 C 1045.17 104.35 1045.92 104.92 1046.81 105.09 L 1046.81 106.08 C 1046.81 108.04 1048.32 109.63 1050.18 109.63 L 1056.91 109.63 L 1056.91 113 L 1058.04 113 L 1058.04 109.63 L 1061.4 109.63 L 1061.4 113 L 1062.53 113 L 1062.53 109.63 L 1069.82 109.63 C 1071.68 109.63 1073.19 108.04 1073.19 106.08 L 1073.19 105.09 C 1074.08 104.92 1074.83 104.35 1075.18 103.54 C 1075.59 102.6 1075.51 101.6 1074.98 100.79 Z M 1055.23 102.33 C 1055.23 103.26 1054.47 104.02 1053.54 104.02 C 1052.62 104.02 1051.86 103.26 1051.86 102.33 C 1051.86 101.4 1052.62 100.65 1053.54 100.65 C 1054.47 100.65 1055.23 101.4 1055.23 102.33 Z M 1050.74 102.33 C 1050.74 103.88 1052 105.14 1053.54 105.14 C 1055.09 105.14 1056.35 103.88 1056.35 102.33 C 1056.35 100.79 1055.09 99.53 1053.54 99.53 C 1052 99.53 1050.74 100.79 1050.74 102.33 Z M 1064.77 102.33 C 1064.77 101.4 1065.53 100.65 1066.46 100.65 C 1067.38 100.65 1068.14 101.4 1068.14 102.33 C 1068.14 103.26 1067.38 104.02 1066.46 104.02 C 1065.53 104.02 1064.77 103.26 1064.77 102.33 Z M 1069.26 102.33 C 1069.26 100.79 1068 99.53 1066.46 99.53 C 1064.91 99.53 1063.65 100.79 1063.65 102.33 C 1063.65 103.88 1064.91 105.14 1066.46 105.14 C 1068 105.14 1069.26 103.88 1069.26 102.33 Z M 1048.49 82.6 C 1048.49 82.34 1048.71 82.12 1048.97 82.12 L 1063.09 82.12 L 1063.09 91.17 C 1063.09 91.44 1062.86 91.67 1062.59 91.67 L 1056.91 91.67 C 1056.6 91.67 1056.35 91.92 1056.35 92.23 L 1056.35 95.14 L 1053.87 91.89 C 1053.76 91.75 1053.6 91.67 1053.42 91.67 L 1048.97 91.67 C 1048.71 91.67 1048.49 91.45 1048.49 91.19 Z M 1048.97 92.79 L 1053.15 92.79 L 1056.47 97.14 C 1056.57 97.28 1056.74 97.36 1056.91 97.36 C 1056.97 97.36 1057.03 97.35 1057.09 97.33 C 1057.32 97.26 1057.47 97.04 1057.47 96.8 L 1057.47 92.79 L 1062.59 92.79 C 1063.48 92.79 1064.21 92.06 1064.21 91.17 L 1064.21 81.86 C 1064.21 81.38 1063.83 81 1063.35 81 L 1048.97 81 C 1048.09 81 1047.37 81.72 1047.37 82.6 L 1047.37 91.19 C 1047.37 92.07 1048.09 92.79 1048.97 92.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 1060px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ChatBot</div></div></div></foreignObject><text x="1060" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ChatBot</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-27"><g><path d="M 600 77 L 640 77 L 640 117 L 600 117 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 631.94 98.65 L 632.16 97.11 C 634.18 98.32 634.21 98.82 634.21 98.83 C 634.2 98.84 633.86 99.13 631.94 98.65 Z M 630.83 98.34 C 627.33 97.29 622.46 95.05 620.49 94.12 C 620.49 94.11 620.49 94.11 620.49 94.1 C 620.49 93.34 619.88 92.72 619.12 92.72 C 618.36 92.72 617.75 93.34 617.75 94.1 C 617.75 94.85 618.36 95.47 619.12 95.47 C 619.45 95.47 619.75 95.35 619.99 95.15 C 622.31 96.25 627.14 98.45 630.67 99.49 L 629.27 109.32 C 629.27 109.35 629.27 109.37 629.27 109.4 C 629.27 110.27 625.43 111.86 619.17 111.86 C 612.84 111.86 608.97 110.27 608.97 109.4 C 608.97 109.37 608.97 109.35 608.97 109.32 L 606.05 88.06 C 608.57 89.8 613.99 90.71 619.18 90.71 C 624.35 90.71 629.76 89.8 632.29 88.07 Z M 605.75 85.84 C 605.79 85.09 610.11 82.14 619.18 82.14 C 628.24 82.14 632.56 85.09 632.6 85.84 L 632.6 86.1 C 632.11 87.79 626.51 89.57 619.18 89.57 C 611.83 89.57 606.23 87.78 605.75 86.09 Z M 633.75 85.86 C 633.75 83.88 628.07 81 619.18 81 C 610.28 81 604.6 83.88 604.6 85.86 L 604.66 86.29 L 607.83 109.44 C 607.9 112.03 614.81 113 619.17 113 C 624.59 113 630.34 111.76 630.41 109.45 L 631.78 99.79 C 632.54 99.97 633.17 100.07 633.67 100.07 C 634.35 100.07 634.8 99.9 635.08 99.57 C 635.31 99.3 635.4 98.97 635.33 98.62 C 635.18 97.83 634.24 96.98 632.33 95.89 L 633.69 86.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 620px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3 Bucket<br />(SourceBucket)</div></div></div></foreignObject><text x="620" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3 Buc...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-28"><g><rect x="940" y="77" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 957.04 97.82 L 957.04 96 L 976.8 96 L 972.95 92.15 L 974.24 90.86 L 979.64 96.27 C 980 96.62 980 97.2 979.64 97.55 L 979.64 97.55 L 974.24 102.96 L 972.95 101.67 L 976.8 97.82 Z M 965.19 81.36 L 963.52 80.65 L 961.28 85.89 L 962.96 86.6 Z M 972.07 87.22 L 970.98 85.76 L 966.39 89.21 L 967.49 90.66 Z M 963.93 106.83 L 967.4 111.35 L 968.85 110.24 L 965.38 105.72 Z M 958.09 108.68 L 958.8 114.31 L 960.61 114.08 L 959.9 108.45 Z M 949.87 112.64 L 951.54 113.35 L 953.78 108.11 L 952.11 107.4 Z M 942.99 106.78 L 944.08 108.24 L 948.67 104.79 L 947.57 103.34 Z M 945.71 97.58 L 940 98.3 L 940.23 100.11 L 945.93 99.39 Z M 947.01 91.66 L 941.71 89.47 L 941.01 91.15 L 946.31 93.34 Z M 951.13 87.17 L 947.66 82.65 L 946.22 83.76 L 949.69 88.28 Z M 956.97 85.32 L 956.26 79.69 L 954.45 79.92 L 955.16 85.55 Z M 964.47 101.19 L 962.39 104.8 C 962.22 105.08 961.92 105.26 961.6 105.26 L 953.55 105.26 C 953.22 105.26 952.92 105.08 952.76 104.8 L 948.73 97.83 C 948.57 97.55 948.57 97.2 948.73 96.92 L 952.76 89.95 C 952.92 89.66 953.22 89.49 953.55 89.49 L 961.6 89.49 C 961.92 89.49 962.22 89.66 962.39 89.95 L 963.76 92.33 L 962.18 93.24 L 961.07 91.31 L 954.07 91.31 L 950.57 97.37 L 954.07 103.44 L 961.07 103.44 L 962.89 100.28 Z" fill="#b0084d" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 960px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">EventBridge Rule</div></div></div></foreignObject><text x="960" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">EventB...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-29"><g><path d="M 680 37 L 700 37 L 700 57 L 680 57 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 696.41 39 L 683.59 39 C 682.71 39 682 39.71 682 40.59 L 682 44.39 C 682 45.27 682.71 45.98 683.59 45.98 L 684.04 45.98 L 684.04 54.71 C 684.04 54.87 684.17 55 684.33 55 L 695.38 55 C 695.54 55 695.67 54.87 695.67 54.71 L 695.67 45.98 L 696.41 45.98 C 697.29 45.98 698 45.27 698 44.39 L 698 40.59 C 698 39.71 697.29 39 696.41 39 Z M 684.62 54.42 L 684.62 45.98 L 695.09 45.98 L 695.09 54.42 Z M 696.41 45.4 L 683.59 45.4 C 683.03 45.4 682.58 44.95 682.58 44.39 L 682.58 44.24 L 686.07 44.24 L 686.07 43.65 L 682.58 43.65 L 682.58 40.59 C 682.58 40.03 683.03 39.58 683.59 39.58 L 696.41 39.58 C 696.97 39.58 697.42 40.03 697.42 40.59 L 697.42 43.65 L 689.27 43.65 L 689.27 44.24 L 697.42 44.24 L 697.42 44.39 C 697.42 44.95 696.97 45.4 696.41 45.4 Z M 685.64 50.07 C 685.64 49.98 685.67 49.9 685.74 49.85 L 687.58 48.22 L 687.97 48.66 L 686.37 50.06 L 687.96 51.43 L 687.58 51.87 L 685.74 50.29 C 685.68 50.23 685.64 50.15 685.64 50.07 Z M 691.48 51.45 L 693.07 50.04 L 691.48 48.66 L 691.86 48.22 L 693.71 49.82 C 693.77 49.88 693.81 49.96 693.81 50.04 C 693.81 50.12 693.77 50.2 693.71 50.26 L 691.86 51.88 Z M 688.88 53.01 L 688.35 52.79 L 690.56 47.4 L 691.09 47.62 Z M 686.95 44.24 L 688.4 44.24 L 688.4 43.65 L 686.95 43.65 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 47px; margin-left: 702px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">CodePipeline</div></div></div></foreignObject><text x="702" y="51" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">Code...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-30"><g><rect x="680" y="37" width="220" height="120" fill="none" stroke="#4d72f3" stroke-dasharray="3 3" pointer-events="all"/></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-31"><g><path d="M 720 77 L 760 77 L 760 117 L 720 117 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 751.94 98.65 L 752.16 97.11 C 754.18 98.32 754.21 98.82 754.21 98.83 C 754.2 98.84 753.86 99.13 751.94 98.65 Z M 750.83 98.34 C 747.33 97.29 742.46 95.05 740.49 94.12 C 740.49 94.11 740.49 94.11 740.49 94.1 C 740.49 93.34 739.88 92.72 739.12 92.72 C 738.36 92.72 737.75 93.34 737.75 94.1 C 737.75 94.85 738.36 95.47 739.12 95.47 C 739.45 95.47 739.75 95.35 739.99 95.15 C 742.31 96.25 747.14 98.45 750.67 99.49 L 749.27 109.32 C 749.27 109.35 749.27 109.37 749.27 109.4 C 749.27 110.27 745.43 111.86 739.17 111.86 C 732.84 111.86 728.97 110.27 728.97 109.4 C 728.97 109.37 728.97 109.35 728.97 109.32 L 726.05 88.06 C 728.57 89.8 733.99 90.71 739.18 90.71 C 744.35 90.71 749.76 89.8 752.29 88.07 Z M 725.75 85.84 C 725.79 85.09 730.11 82.14 739.18 82.14 C 748.24 82.14 752.56 85.09 752.6 85.84 L 752.6 86.1 C 752.11 87.79 746.51 89.57 739.18 89.57 C 731.83 89.57 726.23 87.78 725.75 86.09 Z M 753.75 85.86 C 753.75 83.88 748.07 81 739.18 81 C 730.28 81 724.6 83.88 724.6 85.86 L 724.66 86.29 L 727.83 109.44 C 727.9 112.03 734.81 113 739.17 113 C 744.59 113 750.34 111.76 750.41 109.45 L 751.78 99.79 C 752.54 99.97 753.17 100.07 753.67 100.07 C 754.35 100.07 754.8 99.9 755.08 99.57 C 755.31 99.3 755.4 98.97 755.33 98.62 C 755.18 97.83 754.24 96.98 752.33 95.89 L 753.69 86.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 740px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3 Bucket<br />(SourceArtifact)</div></div></div></foreignObject><text x="740" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3 Buc...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-32"><g><path d="M 820 77 L 860 77 L 860 117 L 820 117 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 853.99 87.08 L 850.71 84.08 L 847.43 87.08 Z M 849.46 83.74 L 842.8 83.75 L 846.35 86.58 Z M 846.45 93.42 L 841.22 95.35 L 851.29 95.35 Z M 838.76 106.37 L 853.53 106.37 L 853.53 96.45 L 838.76 96.45 Z M 845.22 87.08 L 841.6 84.2 L 837.99 87.08 Z M 836.84 86.6 L 840.39 83.76 L 833.32 83.77 Z M 835.69 87.08 L 832.07 84.18 L 828.45 87.08 L 833.84 87.08 Z M 833.3 88.18 L 828.87 88.18 L 833.3 92.57 Z M 833.3 94.29 L 828.1 99.02 L 833.3 104.1 Z M 833.3 105.89 L 827.83 111.24 L 827.83 111.32 L 833.3 111.32 Z M 827.83 100.29 L 827.83 109.7 L 832.64 105 Z M 827.83 97.78 L 832.61 93.43 L 827.83 88.7 Z M 827.28 86.61 L 830.82 83.77 L 827.28 83.78 Z M 826.19 82.68 L 825.09 82.68 L 825.09 88.18 L 826.19 88.18 L 826.19 87.63 L 826.19 83.23 Z M 855.92 87.83 C 855.84 88.04 855.63 88.18 855.41 88.18 L 846.97 88.18 L 846.97 92.59 L 854.28 95.39 L 854.28 95.39 C 854.48 95.47 854.62 95.67 854.62 95.9 L 854.62 106.92 C 854.62 107.22 854.38 107.47 854.08 107.47 L 838.22 107.47 C 837.92 107.47 837.67 107.22 837.67 106.92 L 837.67 95.9 C 837.67 95.66 837.82 95.46 838.03 95.39 L 838.03 95.38 L 845.87 92.59 L 845.87 88.18 L 834.39 88.18 L 834.39 111.88 C 834.39 112.18 834.15 112.43 833.84 112.43 L 827.28 112.43 C 826.98 112.43 826.73 112.18 826.73 111.88 L 826.73 89.29 L 824.55 89.29 C 824.25 89.29 824 89.04 824 88.74 L 824 82.12 C 824 81.82 824.25 81.57 824.55 81.57 L 826.73 81.57 C 827.04 81.57 827.28 81.82 827.28 82.12 L 827.28 82.67 L 850.79 82.68 L 855.78 87.23 C 855.94 87.38 856 87.62 855.92 87.83 Z M 844.57 105.43 L 847.75 98.15 L 846.75 97.7 L 843.57 104.99 Z M 847.56 103 L 848.27 103.84 L 850.63 101.82 C 850.75 101.73 850.82 101.58 850.82 101.43 C 850.83 101.28 850.77 101.13 850.67 101.02 L 848.69 99 L 847.92 99.78 L 849.47 101.37 Z M 840.46 101.4 C 840.34 101.29 840.27 101.13 840.28 100.96 C 840.29 100.8 840.37 100.65 840.5 100.55 L 843.19 98.55 L 843.84 99.43 L 841.69 101.04 L 843.49 102.7 L 842.75 103.52 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 840px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">CodeBuild</div></div></div></foreignObject><text x="840" y="136" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">CodeBu...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-33"><g><rect x="580" y="237" width="320" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 234px; margin-left: 581px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">waf-construct</div></div></div></foreignObject><text x="740" y="234" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">waf-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-34"><g><path d="M 620 257 L 660 257 L 660 297 L 620 297 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 625.37 277.57 L 624 277.57 L 624 276.43 L 625.37 276.43 C 625.49 273.54 626.42 270.79 628.12 268.44 L 629.05 269.11 C 627.49 271.26 626.63 273.78 626.51 276.43 L 628 276.43 L 628 277.57 L 626.51 277.57 C 626.62 280.24 627.48 282.77 629.05 284.93 L 628.12 285.6 C 626.41 283.24 625.48 280.48 625.37 277.57 Z M 648.58 288.9 C 646.23 290.6 643.47 291.54 640.57 291.65 L 640.57 293 L 639.43 293 L 639.43 291.65 C 636.53 291.54 633.77 290.6 631.42 288.9 L 632.09 287.98 C 634.25 289.54 636.77 290.4 639.43 290.51 L 639.43 289 L 640.57 289 L 640.57 290.51 C 643.23 290.4 645.75 289.53 647.91 287.98 Z M 631.42 265.14 C 633.77 263.44 636.53 262.5 639.43 262.39 L 639.43 261 L 640.57 261 L 640.57 262.39 C 643.47 262.5 646.23 263.44 648.58 265.14 L 647.91 266.07 C 645.75 264.51 643.23 263.64 640.57 263.53 L 640.57 265 L 639.43 265 L 639.43 263.53 C 636.77 263.64 634.25 264.51 632.09 266.07 Z M 656 276.43 L 656 277.57 L 654.63 277.57 C 654.52 280.48 653.59 283.24 651.88 285.6 L 650.95 284.93 C 652.52 282.77 653.38 280.24 653.49 277.57 L 652 277.57 L 652 276.43 L 653.49 276.43 C 653.37 273.78 652.51 271.26 650.95 269.11 L 651.88 268.44 C 653.58 270.79 654.51 273.54 654.63 276.43 Z M 648.56 267.66 L 653.04 263.18 L 653.84 263.99 L 649.36 268.47 Z M 631.44 286.39 L 626.96 290.87 L 626.16 290.06 L 630.64 285.58 Z M 632.72 270.52 L 624.24 262.04 L 625.04 261.24 L 633.52 269.72 Z M 647.26 283.45 L 655.76 291.96 L 654.96 292.76 L 646.45 284.26 Z M 635.24 277.42 C 635.28 277.34 635.33 277.27 635.37 277.19 C 636.33 275.68 636.06 273.62 635.75 272.38 C 636.58 272.92 637.32 274.04 637.57 274.51 C 637.68 274.71 637.89 274.83 638.12 274.81 C 638.35 274.79 638.54 274.64 638.62 274.43 C 639.47 272.01 639.03 270.19 638.43 269.01 C 639.16 269.44 639.73 270.05 640.1 270.82 C 640.91 272.51 640.73 274.81 639.63 276.83 C 638.13 279.59 638.43 282.49 638.77 284.05 C 637.91 283.68 637.15 283.26 636.49 282.8 C 634.76 281.6 634.21 279.23 635.24 277.42 Z M 642.91 277.23 C 642.87 277.46 642.98 277.69 643.19 277.81 C 643.39 277.93 643.65 277.91 643.83 277.76 C 643.88 277.73 644.82 276.95 645.27 275.08 C 645.82 275.88 646.39 277.48 645.63 280.43 C 644.88 283.35 641.3 284.17 640.03 284.37 C 639.72 283.27 639.1 280.2 640.63 277.37 C 641.72 275.37 642.01 273.1 641.47 271.23 C 642.5 272.44 643.37 274.36 642.91 277.23 Z M 634.24 276.86 C 632.93 279.18 633.63 282.2 635.83 283.74 C 636.83 284.44 638.05 285.04 639.43 285.54 C 639.5 285.56 639.56 285.57 639.63 285.57 C 639.64 285.57 639.65 285.57 639.66 285.56 L 639.66 285.57 C 639.9 285.55 645.61 285.05 646.73 280.72 C 648.18 275.14 645.29 273.48 645.17 273.41 C 645 273.32 644.8 273.32 644.63 273.41 C 644.46 273.5 644.34 273.66 644.32 273.85 C 644.28 274.24 644.22 274.58 644.14 274.89 C 643.75 270.51 640.27 268.72 639.57 268.41 C 638.85 267.85 637.98 267.47 636.96 267.29 C 636.71 267.25 636.45 267.38 636.35 267.62 C 636.24 267.85 636.3 268.13 636.5 268.3 C 636.58 268.37 638.36 269.91 637.85 272.85 C 637.19 271.98 636.13 270.93 634.89 270.93 C 634.7 270.93 634.53 271.02 634.42 271.18 C 634.31 271.34 634.29 271.53 634.36 271.71 C 634.37 271.74 635.54 274.81 634.41 276.58 C 634.35 276.67 634.29 276.77 634.24 276.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 304px; margin-left: 640px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">WAF<br />(Web ACL)</div></div></div></foreignObject><text x="640" y="316" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">WAF...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-35"><g><path d="M 720 257 L 760 257 L 760 297 L 720 297 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 751.94 278.65 L 752.16 277.11 C 754.18 278.32 754.21 278.82 754.21 278.83 C 754.2 278.84 753.86 279.13 751.94 278.65 Z M 750.83 278.34 C 747.33 277.29 742.46 275.05 740.49 274.12 C 740.49 274.11 740.49 274.11 740.49 274.1 C 740.49 273.34 739.88 272.72 739.12 272.72 C 738.36 272.72 737.75 273.34 737.75 274.1 C 737.75 274.85 738.36 275.47 739.12 275.47 C 739.45 275.47 739.75 275.35 739.99 275.15 C 742.31 276.25 747.14 278.45 750.67 279.49 L 749.27 289.32 C 749.27 289.35 749.27 289.37 749.27 289.4 C 749.27 290.27 745.43 291.86 739.17 291.86 C 732.84 291.86 728.97 290.27 728.97 289.4 C 728.97 289.37 728.97 289.35 728.97 289.32 L 726.05 268.06 C 728.57 269.8 733.99 270.71 739.18 270.71 C 744.35 270.71 749.76 269.8 752.29 268.07 Z M 725.75 265.84 C 725.79 265.09 730.11 262.14 739.18 262.14 C 748.24 262.14 752.56 265.09 752.6 265.84 L 752.6 266.1 C 752.11 267.79 746.51 269.57 739.18 269.57 C 731.83 269.57 726.23 267.78 725.75 266.09 Z M 753.75 265.86 C 753.75 263.88 748.07 261 739.18 261 C 730.28 261 724.6 263.88 724.6 265.86 L 724.66 266.29 L 727.83 289.44 C 727.9 292.03 734.81 293 739.17 293 C 744.59 293 750.34 291.76 750.41 289.45 L 751.78 279.79 C 752.54 279.97 753.17 280.07 753.67 280.07 C 754.35 280.07 754.8 279.9 755.08 279.57 C 755.31 279.3 755.4 278.97 755.33 278.62 C 755.18 277.83 754.24 276.98 752.33 275.89 L 753.69 266.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 304px; margin-left: 740px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3 Bucket<br />(WafLogBucket)</div></div></div></foreignObject><text x="740" y="316" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3 Buc...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-36"><g><path d="M 820 257 L 860 257 L 860 297 L 820 297 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 843.91 285.73 L 851.19 285.73 L 851.19 286.85 L 843.91 286.85 L 843.91 287.97 L 842.8 287.97 L 842.8 286.85 L 840.56 286.85 L 840.56 285.73 L 842.8 285.73 L 842.8 285.17 L 843.91 285.17 Z M 846.15 282.37 L 851.19 282.37 L 851.19 283.49 L 846.15 283.49 L 846.15 284.61 L 845.03 284.61 L 845.03 283.49 L 840.56 283.49 L 840.56 282.37 L 845.03 282.37 L 845.03 281.81 L 846.15 281.81 Z M 848.95 279.02 L 851.19 279.02 L 851.19 280.13 L 848.95 280.13 L 848.95 281.25 L 847.83 281.25 L 847.83 280.13 L 840.56 280.13 L 840.56 279.02 L 847.83 279.02 L 847.83 278.46 L 848.95 278.46 Z M 829.37 281.25 L 834.41 281.25 L 834.41 282.37 L 829.37 282.37 C 828.67 282.37 827.92 282.16 827.37 281.81 C 826.24 281.09 824.34 279.42 824.34 276.25 C 824.34 272.4 826.95 270.98 828.47 270.49 L 828.44 269.98 C 828.44 266.81 830.53 263.56 833.31 262.38 C 836.57 261 840.02 261.69 842.54 264.22 C 843.32 265 843.97 265.96 844.46 267.07 C 845.45 266.24 846.8 265.96 848.05 266.37 C 849.65 266.89 850.64 268.37 850.75 270.35 C 852.34 270.73 855.66 272.03 855.66 276.3 C 855.66 276.46 855.65 276.61 855.65 276.76 L 854.53 276.73 C 854.53 276.58 854.54 276.44 854.54 276.3 C 854.54 272.53 851.43 271.58 850.1 271.35 C 849.95 271.33 849.81 271.24 849.73 271.11 C 849.64 270.99 849.61 270.83 849.64 270.68 C 849.63 269.02 848.92 267.83 847.7 267.43 C 846.61 267.07 845.41 267.47 844.72 268.42 C 844.59 268.58 844.39 268.67 844.18 268.64 C 843.98 268.61 843.81 268.47 843.74 268.27 C 843.27 266.96 842.6 265.86 841.75 265.01 C 839.56 262.81 836.57 262.22 833.75 263.41 C 831.4 264.41 829.56 267.28 829.56 269.94 L 829.61 270.87 C 829.63 271.14 829.45 271.38 829.19 271.44 C 827.8 271.8 825.46 272.89 825.46 276.25 C 825.46 278.75 826.82 280.14 827.97 280.86 C 828.35 281.1 828.88 281.25 829.37 281.25 Z M 854.54 284.23 L 853.55 284.17 C 853.29 284.16 853.04 284.34 852.98 284.61 C 852.79 285.45 852.46 286.24 852 286.97 C 851.85 287.2 851.9 287.5 852.1 287.69 L 852.84 288.34 L 851 290.18 L 850.35 289.44 C 850.17 289.24 849.86 289.2 849.63 289.34 C 848.9 289.8 848.11 290.13 847.27 290.32 C 847 290.38 846.82 290.63 846.83 290.9 L 846.89 291.88 L 844.3 291.88 L 844.35 290.9 C 844.37 290.63 844.18 290.38 843.92 290.32 C 843.08 290.13 842.28 289.8 841.55 289.34 C 841.32 289.19 841.02 289.24 840.84 289.44 L 840.18 290.18 L 838.35 288.34 L 839.08 287.69 C 839.29 287.5 839.33 287.2 839.18 286.97 C 838.72 286.24 838.4 285.45 838.21 284.61 C 838.15 284.34 837.88 284.16 837.63 284.17 L 836.64 284.23 L 836.64 281.63 L 837.63 281.69 C 837.89 281.71 838.15 281.52 838.21 281.26 C 838.4 280.42 838.73 279.63 839.19 278.9 C 839.34 278.67 839.29 278.36 839.09 278.18 L 838.35 277.52 L 840.18 275.69 L 840.84 276.43 C 841.03 276.63 841.33 276.67 841.56 276.53 C 842.29 276.07 843.08 275.74 843.92 275.55 C 844.18 275.49 844.37 275.25 844.35 274.97 L 844.3 273.98 L 846.89 273.98 L 846.83 274.97 C 846.82 275.25 847 275.49 847.27 275.55 C 848.1 275.74 848.9 276.07 849.63 276.53 C 849.86 276.67 850.16 276.63 850.34 276.43 L 851 275.69 L 852.84 277.52 L 852.1 278.18 C 851.89 278.36 851.85 278.67 852 278.9 C 852.46 279.62 852.79 280.42 852.98 281.26 C 853.04 281.52 853.29 281.71 853.55 281.69 L 854.54 281.63 Z M 855.07 280.48 L 853.95 280.55 C 853.77 279.91 853.51 279.29 853.19 278.71 L 854.02 277.96 C 854.14 277.86 854.21 277.71 854.21 277.56 C 854.22 277.41 854.16 277.26 854.05 277.15 L 851.37 274.48 C 851.27 274.37 851.11 274.3 850.96 274.31 C 850.81 274.32 850.66 274.38 850.56 274.5 L 849.81 275.34 C 849.23 275.01 848.62 274.76 847.98 274.58 L 848.04 273.45 C 848.05 273.3 847.99 273.15 847.89 273.04 C 847.78 272.93 847.64 272.86 847.48 272.86 L 843.7 272.86 C 843.55 272.86 843.4 272.93 843.3 273.04 C 843.19 273.15 843.14 273.3 843.14 273.45 L 843.21 274.58 C 842.57 274.76 841.95 275.01 841.37 275.34 L 840.62 274.5 C 840.52 274.38 840.38 274.32 840.22 274.31 C 840.06 274.31 839.92 274.37 839.81 274.48 L 837.14 277.15 C 837.03 277.26 836.97 277.41 836.97 277.56 C 836.98 277.71 837.05 277.86 837.16 277.96 L 838 278.71 C 837.68 279.29 837.42 279.91 837.24 280.55 L 836.12 280.48 C 835.97 280.47 835.81 280.53 835.7 280.63 C 835.59 280.74 835.53 280.89 835.53 281.04 L 835.53 284.82 C 835.53 284.98 835.59 285.12 835.7 285.23 C 835.81 285.33 835.97 285.39 836.12 285.38 L 837.23 285.32 C 837.42 285.96 837.67 286.57 838 287.16 L 837.16 287.9 C 837.05 288 836.98 288.15 836.97 288.3 C 836.97 288.46 837.03 288.61 837.14 288.71 L 839.81 291.39 C 839.92 291.5 840.08 291.56 840.22 291.55 C 840.38 291.55 840.52 291.48 840.62 291.36 L 841.37 290.53 C 841.95 290.86 842.57 291.11 843.21 291.29 L 843.14 292.41 C 843.14 292.56 843.19 292.71 843.3 292.82 C 843.4 292.94 843.55 293 843.7 293 L 847.48 293 C 847.64 293 847.78 292.94 847.89 292.82 C 847.99 292.71 848.05 292.56 848.04 292.41 L 847.98 291.3 C 848.62 291.11 849.24 290.86 849.82 290.53 L 850.56 291.36 C 850.66 291.48 850.81 291.55 850.96 291.55 C 851.11 291.56 851.27 291.5 851.37 291.39 L 854.05 288.71 C 854.16 288.61 854.22 288.46 854.21 288.3 C 854.21 288.15 854.14 288 854.02 287.9 L 853.19 287.16 C 853.51 286.57 853.77 285.96 853.95 285.32 L 855.07 285.38 C 855.22 285.39 855.37 285.33 855.48 285.23 C 855.6 285.12 855.66 284.98 855.66 284.82 L 855.66 281.04 C 855.66 280.89 855.6 280.74 855.48 280.63 C 855.37 280.53 855.22 280.47 855.07 280.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 304px; margin-left: 840px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">SSM<br />(Parameter Store)</div></div></div></foreignObject><text x="840" y="316" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">SSM...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-37"><g><rect x="560" y="587" width="330" height="160" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 328px; height: 1px; padding-top: 584px; margin-left: 561px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">cloudfront-stack</div></div></div></foreignObject><text x="725" y="584" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">cloudfront-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-38"><g><path d="M 710 647 L 750 647 L 750 687 L 710 687 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 741.94 668.65 L 742.16 667.11 C 744.18 668.32 744.21 668.82 744.21 668.83 C 744.2 668.84 743.86 669.13 741.94 668.65 Z M 740.83 668.34 C 737.33 667.29 732.46 665.05 730.49 664.12 C 730.49 664.11 730.49 664.11 730.49 664.1 C 730.49 663.34 729.88 662.72 729.12 662.72 C 728.36 662.72 727.75 663.34 727.75 664.1 C 727.75 664.85 728.36 665.47 729.12 665.47 C 729.45 665.47 729.75 665.35 729.99 665.15 C 732.31 666.25 737.14 668.45 740.67 669.49 L 739.27 679.32 C 739.27 679.35 739.27 679.37 739.27 679.4 C 739.27 680.27 735.43 681.86 729.17 681.86 C 722.84 681.86 718.97 680.27 718.97 679.4 C 718.97 679.37 718.97 679.35 718.97 679.32 L 716.05 658.06 C 718.57 659.8 723.99 660.71 729.18 660.71 C 734.35 660.71 739.76 659.8 742.29 658.07 Z M 715.75 655.84 C 715.79 655.09 720.11 652.14 729.18 652.14 C 738.24 652.14 742.56 655.09 742.6 655.84 L 742.6 656.1 C 742.11 657.79 736.51 659.57 729.18 659.57 C 721.83 659.57 716.23 657.78 715.75 656.09 Z M 743.75 655.86 C 743.75 653.88 738.07 651 729.18 651 C 720.28 651 714.6 653.88 714.6 655.86 L 714.66 656.29 L 717.83 679.44 C 717.9 682.03 724.81 683 729.17 683 C 734.59 683 740.34 681.76 740.41 679.45 L 741.78 669.79 C 742.54 669.97 743.17 670.07 743.67 670.07 C 744.35 670.07 744.8 669.9 745.08 669.57 C 745.31 669.3 745.4 668.97 745.33 668.62 C 745.18 667.83 744.24 666.98 742.33 665.89 L 743.69 656.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 694px; margin-left: 730px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3<br />(静的Webページ)</div></div></div></foreignObject><text x="730" y="706" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-39"><g><path d="M 610 647 L 650 647 L 650 687 L 610 687 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/><path d="M 638.64 673.93 C 638.64 672.98 637.87 672.21 636.93 672.21 C 635.98 672.21 635.21 672.98 635.21 673.93 C 635.21 674.87 635.98 675.64 636.93 675.64 C 637.87 675.64 638.64 674.87 638.64 673.93 Z M 639.78 673.93 C 639.78 675.5 638.5 676.78 636.93 676.78 C 635.35 676.78 634.07 675.5 634.07 673.93 C 634.07 672.35 635.35 671.07 636.93 671.07 C 638.5 671.07 639.78 672.35 639.78 673.93 Z M 624.51 665.68 C 624.51 664.74 623.74 663.97 622.8 663.97 C 621.85 663.97 621.08 664.74 621.08 665.68 C 621.08 666.63 621.85 667.4 622.8 667.4 C 623.74 667.4 624.51 666.63 624.51 665.68 Z M 625.65 665.68 C 625.65 667.26 624.37 668.54 622.8 668.54 C 621.22 668.54 619.94 667.26 619.94 665.68 C 619.94 664.11 621.22 662.82 622.8 662.82 C 624.37 662.82 625.65 664.11 625.65 665.68 Z M 629.85 656.72 C 629.85 657.66 630.61 658.43 631.56 658.43 C 632.51 658.43 633.27 657.66 633.27 656.72 C 633.27 655.77 632.51 655 631.56 655 C 630.61 655 629.85 655.77 629.85 656.72 Z M 628.7 656.72 C 628.7 655.14 629.99 653.86 631.56 653.86 C 633.14 653.86 634.42 655.14 634.42 656.72 C 634.42 658.29 633.14 659.58 631.56 659.58 C 629.99 659.58 628.7 658.29 628.7 656.72 Z M 644.86 667 C 644.86 661.7 642.02 656.8 637.44 654.15 C 636.61 654.31 635.82 654.54 634.83 654.9 L 634.44 653.82 C 634.96 653.64 635.42 653.49 635.87 653.36 C 634.03 652.56 632.03 652.14 630 652.14 C 629.03 652.14 628.09 652.24 627.16 652.42 C 627.83 652.82 628.43 653.21 629 653.65 L 628.31 654.56 C 627.5 653.94 626.65 653.42 625.54 652.83 C 619.93 654.6 615.89 659.55 615.25 665.36 C 616.42 665.12 617.55 664.99 618.81 664.96 L 618.84 666.1 C 617.52 666.13 616.39 666.27 615.16 666.55 C 615.15 666.7 615.14 666.85 615.14 667 C 615.14 671.95 617.59 676.51 621.62 679.26 C 620.9 677.12 620.54 675.11 620.54 673.14 C 620.54 672.02 620.74 671.1 620.94 670.12 C 620.99 669.9 621.04 669.67 621.08 669.43 L 622.2 669.65 C 622.16 669.89 622.11 670.13 622.06 670.36 C 621.86 671.31 621.69 672.14 621.69 673.14 C 621.69 675.37 622.18 677.68 623.17 680.19 C 625.3 681.29 627.59 681.86 630 681.86 C 631.57 681.86 633.11 681.61 634.58 681.12 C 635.15 679.99 635.58 678.92 635.94 677.69 L 637.03 678 C 636.77 678.9 636.48 679.72 636.12 680.53 C 637.04 680.11 637.91 679.6 638.73 679 C 638.54 678.52 638.32 678.04 638.09 677.57 L 639.11 677.06 C 639.31 677.46 639.49 677.86 639.67 678.27 C 642.97 675.44 644.86 671.38 644.86 667 Z M 646 667 C 646 671.99 643.73 676.6 639.78 679.66 C 638.81 680.42 637.74 681.05 636.62 681.56 C 636.15 681.77 635.66 681.97 635.16 682.14 C 633.52 682.71 631.78 683 630 683 C 627.37 683 624.76 682.35 622.45 681.11 C 617.24 678.32 614 672.91 614 667 C 614 666.61 614.01 666.31 614.03 666.03 C 614.42 659.36 619 653.58 625.43 651.67 C 626.89 651.22 628.43 651 630 651 C 632.75 651 635.45 651.71 637.82 653.05 C 642.86 655.87 646 661.22 646 667 Z M 628.47 658.69 L 627.71 657.83 C 626.43 658.95 625.44 660.14 624.27 661.93 L 625.23 662.55 C 626.33 660.85 627.27 659.74 628.47 658.69 Z M 626.73 666.1 L 626.36 667.18 C 628.98 668.08 631.27 669.52 633.56 671.71 L 634.35 670.88 C 631.94 668.58 629.51 667.06 626.73 666.1 Z M 634.37 659.38 C 636.51 662.65 637.72 666.24 637.96 670.05 L 636.82 670.12 C 636.59 666.51 635.45 663.11 633.41 660.01 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 694px; margin-left: 630px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">CloudFront</div></div></div></foreignObject><text x="630" y="706" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">CloudF...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-40"><g><path d="M 810 647 L 850 647 L 850 687 L 810 687 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 841.94 668.65 L 842.16 667.11 C 844.18 668.32 844.21 668.82 844.21 668.83 C 844.2 668.84 843.86 669.13 841.94 668.65 Z M 840.83 668.34 C 837.33 667.29 832.46 665.05 830.49 664.12 C 830.49 664.11 830.49 664.11 830.49 664.1 C 830.49 663.34 829.88 662.72 829.12 662.72 C 828.36 662.72 827.75 663.34 827.75 664.1 C 827.75 664.85 828.36 665.47 829.12 665.47 C 829.45 665.47 829.75 665.35 829.99 665.15 C 832.31 666.25 837.14 668.45 840.67 669.49 L 839.27 679.32 C 839.27 679.35 839.27 679.37 839.27 679.4 C 839.27 680.27 835.43 681.86 829.17 681.86 C 822.84 681.86 818.97 680.27 818.97 679.4 C 818.97 679.37 818.97 679.35 818.97 679.32 L 816.05 658.06 C 818.57 659.8 823.99 660.71 829.18 660.71 C 834.35 660.71 839.76 659.8 842.29 658.07 Z M 815.75 655.84 C 815.79 655.09 820.11 652.14 829.18 652.14 C 838.24 652.14 842.56 655.09 842.6 655.84 L 842.6 656.1 C 842.11 657.79 836.51 659.57 829.18 659.57 C 821.83 659.57 816.23 657.78 815.75 656.09 Z M 843.75 655.86 C 843.75 653.88 838.07 651 829.18 651 C 820.28 651 814.6 653.88 814.6 655.86 L 814.66 656.29 L 817.83 679.44 C 817.9 682.03 824.81 683 829.17 683 C 834.59 683 840.34 681.76 840.41 679.45 L 841.78 669.79 C 842.54 669.97 843.17 670.07 843.67 670.07 C 844.35 670.07 844.8 669.9 845.08 669.57 C 845.31 669.3 845.4 668.97 845.33 668.62 C 845.18 667.83 844.24 666.98 842.33 665.89 L 843.69 656.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 694px; margin-left: 830px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3<br />(Log)</div></div></div></foreignObject><text x="830" y="706" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-41"><g><rect x="0" y="397" width="400" height="150" fill="none" stroke="#000000" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 398px; height: 1px; padding-top: 394px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">db-aurora-stack</div></div></div></foreignObject><text x="200" y="394" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">db-aurora-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-42"><g><rect x="240" y="427" width="140" height="100" rx="15" ry="15" fill="none" stroke="#000000" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 424px; margin-left: 241px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">aurora-alarm-construct</div></div></div></foreignObject><text x="310" y="424" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">aurora-alarm-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-43"><g><rect x="20" y="427" width="200" height="100" rx="15" ry="15" fill="none" stroke="#4d72f3" stroke-dasharray="3 3" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 424px; margin-left: 21px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">aurora-mysql-constructs</div></div></div></foreignObject><text x="120" y="424" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">aurora-mysql-constructs</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-44"><g><path d="M 40 447 L 80 447 L 80 487 L 40 487 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 62.81 454.89 L 61.15 454.89 L 61.15 453.77 L 62.81 453.77 L 62.81 452.1 L 63.92 452.1 L 63.92 453.77 L 65.58 453.77 L 65.58 454.89 L 63.92 454.89 L 63.92 456.56 L 62.81 456.56 Z M 69.45 461.01 L 67.79 461.01 L 67.79 459.9 L 69.45 459.9 L 69.45 458.23 L 70.55 458.23 L 70.55 459.9 L 72.21 459.9 L 72.21 461.01 L 70.55 461.01 L 70.55 462.68 L 69.45 462.68 Z M 66.57 478.6 C 65.51 475.92 63.07 473.47 60.4 472.4 C 63.07 471.34 65.51 468.89 66.57 466.2 C 67.62 468.89 70.06 471.34 72.73 472.4 C 70.06 473.47 67.62 475.92 66.57 478.6 Z M 75.45 471.85 C 71.56 471.85 67.12 467.38 67.12 463.47 C 67.12 463.16 66.87 462.91 66.57 462.91 C 66.26 462.91 66.01 463.16 66.01 463.47 C 66.01 467.38 61.58 471.85 57.68 471.85 C 57.38 471.85 57.13 472.1 57.13 472.4 C 57.13 472.71 57.38 472.96 57.68 472.96 C 61.58 472.96 66.01 477.42 66.01 481.34 C 66.01 481.65 66.26 481.9 66.57 481.9 C 66.87 481.9 67.12 481.65 67.12 481.34 C 67.12 477.42 71.56 472.96 75.45 472.96 C 75.75 472.96 76 472.71 76 472.4 C 76 472.1 75.75 471.85 75.45 471.85 Z M 45.11 460.88 C 46.72 462.06 49.85 462.68 52.85 462.68 C 55.85 462.68 58.99 462.06 60.6 460.88 L 60.6 466.21 C 59.8 467.28 56.87 468.33 52.96 468.33 C 48.47 468.33 45.11 466.91 45.11 465.65 Z M 52.85 456.56 C 57.65 456.56 60.6 458.02 60.6 459.06 C 60.6 460.11 57.65 461.57 52.85 461.57 C 48.05 461.57 45.11 460.11 45.11 459.06 C 45.11 458.02 48.05 456.56 52.85 456.56 Z M 60.6 477.45 C 60.6 478.73 57.28 480.17 52.85 480.17 C 48.42 480.17 45.11 478.73 45.11 477.45 L 45.11 473.89 C 46.74 475.14 49.93 475.8 52.99 475.8 C 55.12 475.8 57.18 475.49 58.79 474.94 L 58.44 473.89 C 56.94 474.4 55.01 474.68 52.99 474.68 C 48.48 474.68 45.11 473.27 45.11 472.01 L 45.11 467.54 C 46.73 468.78 49.91 469.44 52.96 469.44 C 56.23 469.44 59.03 468.76 60.6 467.69 L 60.6 469.36 L 61.7 469.36 L 61.7 459.06 C 61.7 456.71 57.14 455.44 52.85 455.44 C 48.73 455.44 44.38 456.61 44.03 458.78 L 44 458.78 L 44 477.45 C 44 479.94 48.56 481.28 52.85 481.28 C 57.14 481.28 61.7 479.94 61.7 477.45 L 61.7 475.48 L 60.6 475.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 494px; margin-left: 60px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Postgre SQL</div></div></div></foreignObject><text x="60" y="506" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Postgr...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-45"><g><path d="M 160 447 L 200 447 L 200 487 L 160 487 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 182.81 454.89 L 181.15 454.89 L 181.15 453.77 L 182.81 453.77 L 182.81 452.1 L 183.92 452.1 L 183.92 453.77 L 185.58 453.77 L 185.58 454.89 L 183.92 454.89 L 183.92 456.56 L 182.81 456.56 Z M 189.45 461.01 L 187.79 461.01 L 187.79 459.9 L 189.45 459.9 L 189.45 458.23 L 190.55 458.23 L 190.55 459.9 L 192.21 459.9 L 192.21 461.01 L 190.55 461.01 L 190.55 462.68 L 189.45 462.68 Z M 186.57 478.6 C 185.51 475.92 183.07 473.47 180.4 472.4 C 183.07 471.34 185.51 468.89 186.57 466.2 C 187.62 468.89 190.06 471.34 192.73 472.4 C 190.06 473.47 187.62 475.92 186.57 478.6 Z M 195.45 471.85 C 191.56 471.85 187.12 467.38 187.12 463.47 C 187.12 463.16 186.87 462.91 186.57 462.91 C 186.26 462.91 186.01 463.16 186.01 463.47 C 186.01 467.38 181.58 471.85 177.68 471.85 C 177.38 471.85 177.13 472.1 177.13 472.4 C 177.13 472.71 177.38 472.96 177.68 472.96 C 181.58 472.96 186.01 477.42 186.01 481.34 C 186.01 481.65 186.26 481.9 186.57 481.9 C 186.87 481.9 187.12 481.65 187.12 481.34 C 187.12 477.42 191.56 472.96 195.45 472.96 C 195.75 472.96 196 472.71 196 472.4 C 196 472.1 195.75 471.85 195.45 471.85 Z M 165.11 460.88 C 166.72 462.06 169.85 462.68 172.85 462.68 C 175.85 462.68 178.99 462.06 180.6 460.88 L 180.6 466.21 C 179.8 467.28 176.87 468.33 172.96 468.33 C 168.47 468.33 165.11 466.91 165.11 465.65 Z M 172.85 456.56 C 177.65 456.56 180.6 458.02 180.6 459.06 C 180.6 460.11 177.65 461.57 172.85 461.57 C 168.05 461.57 165.11 460.11 165.11 459.06 C 165.11 458.02 168.05 456.56 172.85 456.56 Z M 180.6 477.45 C 180.6 478.73 177.28 480.17 172.85 480.17 C 168.42 480.17 165.11 478.73 165.11 477.45 L 165.11 473.89 C 166.74 475.14 169.93 475.8 172.99 475.8 C 175.12 475.8 177.18 475.49 178.79 474.94 L 178.44 473.89 C 176.94 474.4 175.01 474.68 172.99 474.68 C 168.48 474.68 165.11 473.27 165.11 472.01 L 165.11 467.54 C 166.73 468.78 169.91 469.44 172.96 469.44 C 176.23 469.44 179.03 468.76 180.6 467.69 L 180.6 469.36 L 181.7 469.36 L 181.7 459.06 C 181.7 456.71 177.14 455.44 172.85 455.44 C 168.73 455.44 164.38 456.61 164.03 458.78 L 164 458.78 L 164 477.45 C 164 479.94 168.56 481.28 172.85 481.28 C 177.14 481.28 181.7 479.94 181.7 477.45 L 181.7 475.48 L 180.6 475.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 494px; margin-left: 180px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">MySQL</div></div></div></foreignObject><text x="180" y="506" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">MySQL</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-46"><g><rect x="90" y="452" width="60" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 467px; margin-left: 91px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OR</div></div></div></foreignObject><text x="120" y="471" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">OR</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-47"><g><rect x="290" y="447" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 329.09 449.73 L 290.91 449.73 C 290.41 449.73 290 450.13 290 450.64 L 290 459.73 C 290 460.23 290.41 460.64 290.91 460.64 L 329.09 460.64 C 329.59 460.64 330 460.23 330 459.73 L 330 450.64 C 330 450.13 329.59 449.73 329.09 449.73 Z M 291.82 458.82 L 291.82 451.55 L 328.18 451.55 L 328.18 458.82 Z M 328.18 470.18 L 330 470.18 L 330 471.55 C 330 472.05 329.59 472.45 329.09 472.45 L 327.73 472.45 L 327.73 470.64 L 328.18 470.64 Z M 311.36 472.45 L 314.09 472.45 L 314.09 470.64 L 311.36 470.64 Z M 322.27 472.45 L 325 472.45 L 325 470.64 L 322.27 470.64 Z M 300.45 472.45 L 303.18 472.45 L 303.18 470.64 L 300.45 470.64 Z M 295 472.45 L 297.73 472.45 L 297.73 470.64 L 295 470.64 Z M 316.82 472.45 L 319.55 472.45 L 319.55 470.64 L 316.82 470.64 Z M 305.91 472.45 L 308.64 472.45 L 308.64 470.64 L 305.91 470.64 Z M 291.82 470.64 L 292.27 470.64 L 292.27 472.45 L 290.91 472.45 C 290.41 472.45 290 472.05 290 471.55 L 290 470.18 L 291.82 470.18 Z M 290 468.36 L 291.82 468.36 L 291.82 466.55 L 290 466.55 Z M 290.91 462.45 L 292.27 462.45 L 292.27 464.27 L 291.82 464.27 L 291.82 464.73 L 290 464.73 L 290 463.36 C 290 462.86 290.41 462.45 290.91 462.45 Z M 311.36 464.27 L 314.09 464.27 L 314.09 462.45 L 311.36 462.45 Z M 305.91 464.27 L 308.64 464.27 L 308.64 462.45 L 305.91 462.45 Z M 300.45 464.27 L 303.18 464.27 L 303.18 462.45 L 300.45 462.45 Z M 322.27 464.27 L 325 464.27 L 325 462.45 L 322.27 462.45 Z M 295 464.27 L 297.73 464.27 L 297.73 462.45 L 295 462.45 Z M 316.82 464.27 L 319.55 464.27 L 319.55 462.45 L 316.82 462.45 Z M 330 463.36 L 330 464.73 L 328.18 464.73 L 328.18 464.27 L 327.73 464.27 L 327.73 462.45 L 329.09 462.45 C 329.59 462.45 330 462.86 330 463.36 Z M 328.18 468.36 L 330 468.36 L 330 466.55 L 328.18 466.55 Z M 328.18 482 L 330 482 L 330 483.36 C 330 483.87 329.59 484.27 329.09 484.27 L 327.73 484.27 L 327.73 482.45 L 328.18 482.45 Z M 300.45 484.27 L 303.18 484.27 L 303.18 482.45 L 300.45 482.45 Z M 311.36 484.27 L 314.09 484.27 L 314.09 482.45 L 311.36 482.45 Z M 295 484.27 L 297.73 484.27 L 297.73 482.45 L 295 482.45 Z M 322.27 484.27 L 325 484.27 L 325 482.45 L 322.27 482.45 Z M 316.82 484.27 L 319.55 484.27 L 319.55 482.45 L 316.82 482.45 Z M 305.91 484.27 L 308.64 484.27 L 308.64 482.45 L 305.91 482.45 Z M 291.82 482.45 L 292.27 482.45 L 292.27 484.27 L 290.91 484.27 C 290.41 484.27 290 483.87 290 483.36 L 290 482 L 291.82 482 Z M 290 480.18 L 291.82 480.18 L 291.82 478.36 L 290 478.36 Z M 290.91 474.27 L 292.27 474.27 L 292.27 476.09 L 291.82 476.09 L 291.82 476.55 L 290 476.55 L 290 475.18 C 290 474.68 290.41 474.27 290.91 474.27 Z M 311.36 476.09 L 314.09 476.09 L 314.09 474.27 L 311.36 474.27 Z M 316.82 476.09 L 319.55 476.09 L 319.55 474.27 L 316.82 474.27 Z M 295 476.09 L 297.73 476.09 L 297.73 474.27 L 295 474.27 Z M 305.91 476.09 L 308.64 476.09 L 308.64 474.27 L 305.91 474.27 Z M 322.27 476.09 L 325 476.09 L 325 474.27 L 322.27 474.27 Z M 300.45 476.09 L 303.18 476.09 L 303.18 474.27 L 300.45 474.27 Z M 330 475.18 L 330 476.55 L 328.18 476.55 L 328.18 476.09 L 327.73 476.09 L 327.73 474.27 L 329.09 474.27 C 329.59 474.27 330 474.68 330 475.18 Z M 328.18 480.18 L 330 480.18 L 330 478.36 L 328.18 478.36 Z" fill="#b0084d" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 494px; margin-left: 310px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">SNS Topic</div></div></div></foreignObject><text x="310" y="506" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">SNS To...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-48"><g><path d="M 1025 447 L 1065 447 L 1065 487 L 1025 487 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 1053.43 471.18 C 1053.43 468.96 1051.62 467.16 1049.38 467.16 C 1047.15 467.16 1045.34 468.96 1045.34 471.18 C 1045.34 473.4 1047.15 475.21 1049.38 475.21 C 1051.62 475.21 1053.43 473.4 1053.43 471.18 M 1054.58 471.18 C 1054.58 474.03 1052.25 476.34 1049.38 476.34 C 1046.52 476.34 1044.19 474.03 1044.19 471.18 C 1044.19 468.34 1046.52 466.03 1049.38 466.03 C 1052.25 466.03 1054.58 468.34 1054.58 471.18 M 1059.37 478.95 L 1055.39 475.4 C 1055.07 475.84 1054.7 476.26 1054.28 476.62 L 1058.25 480.18 C 1058.59 480.48 1059.12 480.46 1059.43 480.12 C 1059.73 479.78 1059.7 479.26 1059.37 478.95 M 1049.38 477.36 C 1052.81 477.36 1055.6 474.59 1055.6 471.18 C 1055.6 467.78 1052.81 465.01 1049.38 465.01 C 1045.96 465.01 1043.17 467.78 1043.17 471.18 C 1043.17 474.59 1045.96 477.36 1049.38 477.36 M 1060.27 480.88 C 1059.89 481.31 1059.35 481.52 1058.81 481.52 C 1058.34 481.52 1057.87 481.36 1057.49 481.02 L 1053.36 477.32 C 1052.21 478.06 1050.85 478.49 1049.38 478.49 C 1045.33 478.49 1042.03 475.21 1042.03 471.18 C 1042.03 467.15 1045.33 463.87 1049.38 463.87 C 1053.44 463.87 1056.74 467.15 1056.74 471.18 C 1056.74 472.34 1056.47 473.43 1055.98 474.4 L 1060.13 478.11 C 1060.93 478.84 1061 480.08 1060.27 480.88 M 1034.27 461.32 C 1034.27 461.61 1034.28 461.9 1034.32 462.19 C 1034.34 462.35 1034.29 462.51 1034.18 462.63 C 1034.09 462.73 1033.98 462.79 1033.85 462.82 C 1032.45 463.18 1030.14 464.27 1030.14 467.53 C 1030.14 470 1031.51 471.36 1032.66 472.07 C 1033.05 472.32 1033.52 472.45 1034.01 472.45 L 1040.89 472.46 L 1040.88 473.59 L 1034 473.58 C 1033.29 473.58 1032.62 473.39 1032.06 473.03 C 1030.92 472.33 1029 470.68 1029 467.53 C 1029 463.74 1031.61 462.34 1033.14 461.84 C 1033.13 461.67 1033.13 461.49 1033.13 461.32 C 1033.13 458.22 1035.24 455 1038.05 453.84 C 1041.33 452.48 1044.8 453.15 1047.34 455.65 C 1048.13 456.43 1048.78 457.37 1049.28 458.45 C 1049.94 457.9 1050.77 457.6 1051.63 457.6 C 1053.34 457.6 1055.25 458.89 1055.6 461.7 C 1057.19 462.06 1060.56 463.34 1060.56 467.58 C 1060.56 469.27 1060.03 470.67 1058.98 471.73 L 1058.16 470.93 C 1059 470.09 1059.42 468.96 1059.42 467.58 C 1059.42 463.87 1056.3 462.95 1054.95 462.72 C 1054.8 462.7 1054.67 462.61 1054.58 462.48 C 1054.49 462.36 1054.46 462.21 1054.49 462.07 C 1054.3 459.77 1052.93 458.73 1051.63 458.73 C 1050.82 458.73 1050.05 459.13 1049.54 459.82 C 1049.41 459.98 1049.2 460.07 1048.99 460.04 C 1048.79 460.01 1048.61 459.87 1048.54 459.67 C 1048.08 458.38 1047.4 457.3 1046.54 456.46 C 1044.34 454.29 1041.33 453.71 1038.49 454.89 C 1036.12 455.87 1034.27 458.69 1034.27 461.32" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 494px; margin-left: 1045px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">CloudWatch Dashboard</div></div></div></foreignObject><text x="1045" y="506" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">CloudW...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-49"><g><rect x="560" y="397" width="390" height="150" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 388px; height: 1px; padding-top: 394px; margin-left: 561px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">openserch-stack</div></div></div></foreignObject><text x="755" y="394" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">openserch-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-50"><g><rect x="580" y="427" width="160" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 424px; margin-left: 660px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">opensearch-serverless-construct</div></div></div></foreignObject><text x="660" y="424" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">opensearch-serverless-cons...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-51"><g><path d="M 640 447 L 680 447 L 680 487 L 640 487 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/><path d="M 650.47 460.53 L 644.59 460.53 C 644.26 460.53 644 460.8 644 461.12 L 644 482.29 C 644 482.61 644.26 482.88 644.59 482.88 L 650.47 482.88 C 650.79 482.88 651.06 482.61 651.06 482.29 L 651.06 461.12 C 651.06 460.8 650.79 460.53 650.47 460.53 Z M 645.18 481.7 L 645.18 461.71 L 649.88 461.71 L 649.88 481.7 Z M 653.41 464.65 L 652.23 464.65 L 652.23 457 C 652.23 456.68 652.5 456.42 652.82 456.42 L 658.7 456.42 C 659.03 456.42 659.29 456.68 659.29 457 L 659.29 459.36 L 658.11 459.36 L 658.11 457.59 L 653.41 457.59 Z M 658.11 478.17 L 659.29 478.17 L 659.29 482.29 C 659.29 482.61 659.03 482.88 658.7 482.88 L 652.82 482.88 C 652.5 482.88 652.23 482.61 652.23 482.29 L 652.23 473.47 L 653.41 473.47 L 653.41 481.7 L 658.11 481.7 Z M 661.64 458.77 L 660.46 458.77 L 660.46 451.71 C 660.46 451.39 660.73 451.12 661.05 451.12 L 666.93 451.12 C 667.26 451.12 667.52 451.39 667.52 451.71 L 667.52 459.94 L 666.34 459.94 L 666.34 452.3 L 661.64 452.3 Z M 666.34 479.35 L 667.52 479.35 L 667.52 482.29 C 667.52 482.61 667.26 482.88 666.93 482.88 L 661.05 482.88 C 660.73 482.88 660.46 482.61 660.46 482.29 L 660.46 478.76 L 661.64 478.76 L 661.64 481.7 L 666.34 481.7 Z M 675.75 455.24 L 675.75 475.23 L 674.58 475.23 L 674.58 455.83 L 669.87 455.83 L 669.87 462.3 L 668.7 462.3 L 668.7 455.24 C 668.7 454.92 668.96 454.65 669.28 454.65 L 675.17 454.65 C 675.49 454.65 675.75 454.92 675.75 455.24 Z M 669.91 472.61 C 670.45 471.5 670.76 470.26 670.76 468.95 C 670.76 464.33 667 460.57 662.38 460.57 C 657.76 460.57 654 464.33 654 468.95 C 654 473.57 657.76 477.33 662.38 477.33 C 664.09 477.33 665.67 476.82 667 475.94 L 672.06 480.5 C 672.48 480.88 673.01 481.07 673.54 481.07 C 674.14 481.07 674.75 480.82 675.19 480.34 C 676 479.43 675.93 478.03 675.02 477.21 Z M 655.18 468.95 C 655.18 464.98 658.41 461.75 662.38 461.75 C 666.35 461.75 669.58 464.98 669.58 468.95 C 669.58 472.92 666.35 476.16 662.38 476.16 C 658.41 476.16 655.18 472.92 655.18 468.95 Z M 674.31 479.55 C 673.93 479.98 673.27 480.01 672.84 479.63 L 667.94 475.21 C 668.46 474.74 668.92 474.23 669.31 473.65 L 674.23 478.08 C 674.66 478.47 674.69 479.13 674.31 479.55 Z M 662.38 463.07 C 659.14 463.07 656.5 465.71 656.5 468.95 C 656.5 472.2 659.14 474.84 662.38 474.84 C 665.63 474.84 668.27 472.2 668.27 468.95 C 668.27 465.71 665.63 463.07 662.38 463.07 Z M 662.38 473.66 C 659.78 473.66 657.67 471.55 657.67 468.95 C 657.67 466.36 659.78 464.24 662.38 464.24 C 664.98 464.24 667.09 466.36 667.09 468.95 C 667.09 471.55 664.98 473.66 662.38 473.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 494px; margin-left: 660px;"><div data-drawio-colors="color: #232F3E; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">OpenSearch Serverless<br />(Collection)</div></div></div></foreignObject><text x="660" y="506" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">OpenSe...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-52"><g><rect x="740" y="472" width="30" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 487px; margin-left: 741px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OR</div></div></div></foreignObject><text x="755" y="491" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">OR</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-53"><g><rect x="770" y="427" width="160" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 424px; margin-left: 771px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">opensearch-construct</div></div></div></foreignObject><text x="850" y="424" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">opensearch-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-54"><g><path d="M 790 447 L 830 447 L 830 487 L 790 487 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/><path d="M 800.47 460.53 L 794.59 460.53 C 794.26 460.53 794 460.8 794 461.12 L 794 482.29 C 794 482.61 794.26 482.88 794.59 482.88 L 800.47 482.88 C 800.79 482.88 801.06 482.61 801.06 482.29 L 801.06 461.12 C 801.06 460.8 800.79 460.53 800.47 460.53 Z M 795.18 481.7 L 795.18 461.71 L 799.88 461.71 L 799.88 481.7 Z M 803.41 464.65 L 802.23 464.65 L 802.23 457 C 802.23 456.68 802.5 456.42 802.82 456.42 L 808.7 456.42 C 809.03 456.42 809.29 456.68 809.29 457 L 809.29 459.36 L 808.11 459.36 L 808.11 457.59 L 803.41 457.59 Z M 808.11 478.17 L 809.29 478.17 L 809.29 482.29 C 809.29 482.61 809.03 482.88 808.7 482.88 L 802.82 482.88 C 802.5 482.88 802.23 482.61 802.23 482.29 L 802.23 473.47 L 803.41 473.47 L 803.41 481.7 L 808.11 481.7 Z M 811.64 458.77 L 810.46 458.77 L 810.46 451.71 C 810.46 451.39 810.73 451.12 811.05 451.12 L 816.93 451.12 C 817.26 451.12 817.52 451.39 817.52 451.71 L 817.52 459.94 L 816.34 459.94 L 816.34 452.3 L 811.64 452.3 Z M 816.34 479.35 L 817.52 479.35 L 817.52 482.29 C 817.52 482.61 817.26 482.88 816.93 482.88 L 811.05 482.88 C 810.73 482.88 810.46 482.61 810.46 482.29 L 810.46 478.76 L 811.64 478.76 L 811.64 481.7 L 816.34 481.7 Z M 825.75 455.24 L 825.75 475.23 L 824.58 475.23 L 824.58 455.83 L 819.87 455.83 L 819.87 462.3 L 818.7 462.3 L 818.7 455.24 C 818.7 454.92 818.96 454.65 819.28 454.65 L 825.17 454.65 C 825.49 454.65 825.75 454.92 825.75 455.24 Z M 819.91 472.61 C 820.45 471.5 820.76 470.26 820.76 468.95 C 820.76 464.33 817 460.57 812.38 460.57 C 807.76 460.57 804 464.33 804 468.95 C 804 473.57 807.76 477.33 812.38 477.33 C 814.09 477.33 815.67 476.82 817 475.94 L 822.06 480.5 C 822.48 480.88 823.01 481.07 823.54 481.07 C 824.14 481.07 824.75 480.82 825.19 480.34 C 826 479.43 825.93 478.03 825.02 477.21 Z M 805.18 468.95 C 805.18 464.98 808.41 461.75 812.38 461.75 C 816.35 461.75 819.58 464.98 819.58 468.95 C 819.58 472.92 816.35 476.16 812.38 476.16 C 808.41 476.16 805.18 472.92 805.18 468.95 Z M 824.31 479.55 C 823.93 479.98 823.27 480.01 822.84 479.63 L 817.94 475.21 C 818.46 474.74 818.92 474.23 819.31 473.65 L 824.23 478.08 C 824.66 478.47 824.69 479.13 824.31 479.55 Z M 812.38 463.07 C 809.14 463.07 806.5 465.71 806.5 468.95 C 806.5 472.2 809.14 474.84 812.38 474.84 C 815.63 474.84 818.27 472.2 818.27 468.95 C 818.27 465.71 815.63 463.07 812.38 463.07 Z M 812.38 473.66 C 809.78 473.66 807.67 471.55 807.67 468.95 C 807.67 466.36 809.78 464.24 812.38 464.24 C 814.98 464.24 817.09 466.36 817.09 468.95 C 817.09 471.55 814.98 473.66 812.38 473.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 494px; margin-left: 810px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">OpenSearch<br />(Domain)</div></div></div></foreignObject><text x="810" y="506" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">OpenSe...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-55"><g><path d="M 870 447 L 910 447 L 910 487 L 870 487 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 904.86 477.29 L 906 477.29 L 906 483 L 900.29 483 L 900.29 481.86 L 904.05 481.86 L 899.31 477.12 L 900.12 476.31 L 904.86 481.05 Z M 880.69 477.12 L 875.95 481.86 L 879.71 481.86 L 879.71 483 L 874 483 L 874 477.29 L 875.14 477.29 L 875.14 481.05 L 879.88 476.31 Z M 906 451 L 906 456.71 L 904.86 456.71 L 904.86 452.95 L 900.12 457.69 L 899.31 456.88 L 904.05 452.14 L 900.29 452.14 L 900.29 451 Z M 875.95 452.14 L 880.69 456.88 L 879.88 457.69 L 875.14 452.95 L 875.14 456.71 L 874 456.71 L 874 451 L 879.71 451 L 879.71 452.14 Z M 896.1 474.81 C 895.44 475.48 893.22 476.17 889.75 476.17 C 885.52 476.17 883.16 475.13 883.14 474.43 L 883.14 461.2 C 884.63 462.08 887.33 462.43 889.75 462.43 C 892.15 462.43 894.81 462.08 896.29 461.21 L 896.29 474.44 C 896.29 474.49 896.29 474.61 896.1 474.81 Z M 889.75 457.86 C 893.24 457.86 895.45 458.53 896.11 459.2 C 896.29 459.39 896.29 459.52 896.29 459.56 L 896.29 459.57 C 896.29 460.27 893.96 461.29 889.75 461.29 C 885.67 461.29 883.16 460.29 883.14 459.58 C 883.18 458.85 885.48 457.86 889.75 457.86 Z M 897.43 459.57 C 897.43 459.28 897.35 458.84 896.92 458.4 C 895.89 457.35 893.21 456.71 889.75 456.71 C 886.21 456.71 882.05 457.46 882 459.57 L 882 474.44 C 882.05 476.56 886.21 477.31 889.75 477.31 C 892.41 477.31 895.69 476.86 896.92 475.6 C 897.35 475.16 897.44 474.71 897.43 474.43 L 897.43 459.58 L 897.43 459.57 L 897.43 459.57 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 494px; margin-left: 890px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">EBS</div></div></div></foreignObject><text x="890" y="506" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">EBS</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-57"><g><rect x="420" y="397" width="100" height="150" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 394px; margin-left: 421px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">efs-stack</div></div></div></foreignObject><text x="470" y="394" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">efs-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-58"><g><path d="M 450 447 L 490 447 L 490 487 L 450 487 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 465.08 457.87 C 468.66 456.37 471.2 458.34 472.12 459.26 C 472.87 460.01 473.46 460.96 473.87 462.1 C 473.94 462.29 474.11 462.44 474.32 462.47 C 474.53 462.5 474.74 462.42 474.86 462.25 C 475.31 461.65 475.97 461.31 476.67 461.31 C 477.71 461.31 478.98 462.1 479.11 464.33 C 479.12 464.59 479.31 464.8 479.56 464.85 C 482.15 465.4 483.46 466.83 483.46 469.12 C 483.46 471.25 482.49 472.56 480.57 473.03 L 480.84 474.14 C 483.27 473.55 484.61 471.77 484.61 469.12 C 484.61 466.43 483.05 464.56 480.21 463.83 C 479.9 461.3 478.24 460.16 476.67 460.16 C 475.92 460.16 475.2 460.42 474.61 460.89 C 474.17 459.94 473.61 459.13 472.93 458.45 C 470.66 456.2 467.57 455.59 464.64 456.82 C 462.14 457.87 460.26 460.76 460.26 463.55 C 460.26 463.68 460.26 463.82 460.27 463.97 C 457.97 464.73 456.62 466.61 456.62 469.09 C 456.62 469.22 456.62 469.34 456.63 469.46 C 456.75 471.61 458.18 473.47 460.29 474.19 L 460.66 473.11 C 459 472.54 457.86 471.08 457.77 469.4 C 457.77 469.3 457.76 469.2 457.76 469.09 C 457.76 466.19 459.8 465.24 461.02 464.94 C 461.3 464.87 461.48 464.61 461.45 464.32 C 461.41 464.04 461.4 463.78 461.4 463.55 C 461.4 461.23 463.02 458.74 465.08 457.87 Z M 476.86 469.86 C 476.86 469.63 476.81 469.46 476.72 469.38 C 476.62 469.29 476.45 469.28 476.32 469.29 L 468.86 469.29 C 468.54 469.29 468.29 469.03 468.29 468.71 C 468.29 468.56 468.23 468.13 468.16 467.86 L 466.18 467.86 C 466.1 468.12 466 468.57 466 468.72 C 465.99 469.03 465.74 469.29 465.43 469.29 L 463.71 469.29 C 463.55 469.28 463.38 469.29 463.28 469.38 C 463.19 469.46 463.14 469.63 463.14 469.86 L 463.14 477.86 L 476.86 477.86 Z M 478 471 L 478 475.13 L 479.48 471 Z M 477.97 478.62 L 477.96 478.62 C 477.88 478.84 477.68 479 477.43 479 L 462.57 479 C 462.26 479 462 478.75 462 478.43 L 462 469.86 C 462 469.16 462.27 468.76 462.5 468.54 C 462.72 468.34 463.11 468.1 463.75 468.15 L 464.92 468.14 C 465.03 467.55 465.28 466.71 465.89 466.71 L 468.49 466.71 C 468.53 466.71 468.56 466.72 468.6 466.72 C 469.13 466.82 469.32 467.59 469.39 468.14 L 476.29 468.14 C 476.89 468.1 477.28 468.34 477.5 468.54 C 477.73 468.76 478 469.16 478 469.86 L 480.29 469.86 C 480.47 469.86 480.65 469.95 480.75 470.1 C 480.86 470.25 480.89 470.45 480.82 470.62 Z M 484.86 481.05 L 480.51 476.7 L 479.7 477.51 L 484.05 481.86 L 480.9 481.86 L 480.9 483 L 485.43 483 C 485.74 483 486 482.74 486 482.43 L 486 477.9 L 484.86 477.9 Z M 460.08 476.68 L 455.14 481.14 L 455.14 477.9 L 454 477.9 L 454 482.43 C 454 482.74 454.26 483 454.57 483 L 459.67 483 L 459.67 481.86 L 456.06 481.86 L 460.85 477.53 Z M 485.43 451 L 480.33 451 L 480.33 452.14 L 484.14 452.14 L 479.68 457.08 L 480.53 457.85 L 484.86 453.06 L 484.86 456.67 L 486 456.67 L 486 451.57 C 486 451.26 485.74 451 485.43 451 Z M 455.14 456.67 L 454 456.67 L 454 451.57 C 454 451.26 454.26 451 454.57 451 L 459.67 451 L 459.67 452.14 L 455.95 452.14 L 460.87 457.06 L 460.06 457.87 L 455.14 452.95 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 494px; margin-left: 470px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">EFS</div></div></div></foreignObject><text x="470" y="506" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">EFS</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-59"><g/><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-60"><g><rect x="20" y="627" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 624px; margin-left: 21px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">backup-vault-stack</div></div></div></foreignObject><text x="80" y="624" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">backup-vault-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-61"><g><rect x="60" y="647" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 67.54 666.05 L 65.73 666.06 C 65.76 670.62 67.87 674.79 71.52 677.51 C 74.09 679.43 77.11 680.36 80.1 680.36 C 84.49 680.36 88.83 678.36 91.65 674.57 C 96.4 668.21 95.08 659.18 88.72 654.44 C 82.56 649.85 73.91 650.95 69.06 656.8 L 69.09 654.29 L 67.27 654.26 L 67.21 658.81 C 67.21 659.05 67.3 659.28 67.47 659.45 C 67.64 659.63 67.87 659.72 68.11 659.73 L 72.71 659.79 L 72.74 657.97 L 70.47 657.94 C 74.71 652.85 82.25 651.89 87.63 655.89 C 93.19 660.04 94.34 667.93 90.2 673.49 C 86.05 679.05 78.16 680.2 72.6 676.05 C 69.42 673.68 67.57 670.03 67.54 666.05 Z M 80 662.45 C 77.99 662.45 76.36 664.09 76.36 666.09 C 76.36 668.1 77.99 669.73 80 669.73 C 82.01 669.73 83.64 668.1 83.64 666.09 C 83.64 664.09 82.01 662.45 80 662.45 Z M 73.59 671.35 L 75.61 669.32 C 74.95 668.41 74.55 667.3 74.55 666.09 C 74.55 664.84 74.98 663.68 75.69 662.76 L 73.59 660.81 L 74.83 659.48 L 77.03 661.52 C 77.88 660.97 78.9 660.64 80 660.64 C 81.22 660.64 82.34 661.04 83.25 661.72 L 85.44 659.48 L 86.74 660.75 L 84.51 663.03 C 85.11 663.91 85.45 664.96 85.45 666.09 C 85.45 667.23 85.1 668.29 84.5 669.16 L 86.74 671.32 L 85.48 672.63 L 83.24 670.47 C 82.33 671.14 81.21 671.55 80 671.55 C 78.86 671.55 77.79 671.19 76.92 670.59 L 74.87 672.63 Z M 90 685.18 L 93.64 685.18 L 93.64 684.27 L 90 684.27 Z M 66.36 685.18 L 70 685.18 L 70 684.27 L 66.36 684.27 Z M 100 647.91 L 100 683.36 C 100 683.87 99.59 684.27 99.09 684.27 L 95.45 684.27 L 95.45 686.09 C 95.45 686.59 95.05 687 94.55 687 L 89.09 687 C 88.59 687 88.18 686.59 88.18 686.09 L 88.18 684.27 L 71.82 684.27 L 71.82 686.09 C 71.82 686.59 71.41 687 70.91 687 L 65.45 687 C 64.95 687 64.55 686.59 64.55 686.09 L 64.55 684.27 L 60.91 684.27 C 60.41 684.27 60 683.87 60 683.36 L 60 675.18 L 61.82 675.18 L 61.82 682.45 L 98.18 682.45 L 98.18 648.82 L 61.82 648.82 L 61.82 655.18 L 60 655.18 L 60 647.91 C 60 647.41 60.41 647 60.91 647 L 99.09 647 C 99.59 647 100 647.41 100 647.91 Z M 60 669.73 L 61.82 669.73 L 61.82 660.64 L 60 660.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 694px; margin-left: 80px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Backup Vault</div></div></div></foreignObject><text x="80" y="706" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Backup...</text></switch></g></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-62"><g><path d="M 420 647 L 460 647 L 460 687 L 420 687 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 447.38 674.56 L 451.69 674.56 L 451.69 673.33 L 447.38 673.33 Z M 441.85 674.56 L 446.15 674.56 L 446.15 673.33 L 441.85 673.33 Z M 436.31 674.56 L 440.61 674.56 L 440.61 673.33 L 436.31 673.33 Z M 443.08 669.63 C 443.08 668.96 443.63 668.4 444.31 668.4 C 444.99 668.4 445.54 668.96 445.54 669.63 C 445.54 670.31 444.99 670.86 444.31 670.86 C 443.63 670.86 443.08 670.31 443.08 669.63 Z M 446.77 669.63 C 446.77 668.28 445.67 667.17 444.31 667.17 C 442.95 667.17 441.85 668.28 441.85 669.63 C 441.85 670.99 442.95 672.1 444.31 672.1 C 445.67 672.1 446.77 670.99 446.77 669.63 Z M 438.15 668.4 C 438.83 668.4 439.38 668.96 439.38 669.63 C 439.38 670.31 438.83 670.86 438.15 670.86 C 437.47 670.86 436.92 670.31 436.92 669.63 C 436.92 668.96 437.47 668.4 438.15 668.4 Z M 438.15 672.1 C 439.51 672.1 440.61 670.99 440.61 669.63 C 440.61 668.28 439.51 667.17 438.15 667.17 C 436.79 667.17 435.69 668.28 435.69 669.63 C 435.69 670.99 436.79 672.1 438.15 672.1 Z M 456 662.86 L 456 677.02 C 456 677.36 455.72 677.63 455.38 677.63 L 436.31 677.63 L 436.31 676.4 L 454.77 676.4 L 454.77 663.48 L 441.23 663.48 L 441.23 662.25 L 455.38 662.25 C 455.72 662.25 456 662.52 456 662.86 Z M 434.27 665.97 C 434.02 666.05 433.84 666.29 433.84 666.56 L 433.84 679.51 L 432.37 680.77 L 430.77 678.94 L 430.77 677.33 C 430.77 677.16 430.7 677.01 430.59 676.89 L 429.18 675.48 L 430.59 674.07 C 430.7 673.95 430.77 673.8 430.77 673.63 L 430.77 672.4 C 430.77 672.24 430.7 672.08 430.59 671.97 L 429.18 670.56 L 430.59 669.15 C 430.7 669.03 430.77 668.87 430.77 668.71 L 430.77 666.56 C 430.77 666.29 430.59 666.06 430.34 665.98 C 427.17 664.97 425.3 661.72 425.99 658.4 C 426.48 656.02 428.32 654.1 430.67 653.53 C 432.66 653.04 434.72 653.46 436.29 654.7 C 437.87 655.94 438.77 657.79 438.77 659.79 C 438.77 662.59 436.92 665.13 434.27 665.97 Z M 440 659.79 C 440 657.41 438.92 655.2 437.05 653.73 C 435.18 652.26 432.74 651.75 430.37 652.33 C 427.57 653.02 425.37 655.3 424.78 658.16 L 424.78 658.16 C 424 661.94 426.03 665.66 429.54 667 L 429.54 668.46 L 427.87 670.12 C 427.63 670.36 427.63 670.75 427.87 670.99 L 429.54 672.66 L 429.54 673.38 L 427.87 675.05 C 427.63 675.29 427.63 675.68 427.87 675.92 L 429.54 677.58 L 429.54 679.17 C 429.54 679.32 429.59 679.47 429.69 679.58 L 431.84 682.04 C 431.96 682.18 432.13 682.25 432.31 682.25 C 432.45 682.25 432.59 682.2 432.71 682.1 L 434.86 680.26 C 435 680.14 435.08 679.97 435.08 679.79 L 435.08 666.99 C 438 665.87 440 662.97 440 659.79 Z M 432.31 661.25 C 431.46 661.25 430.77 660.56 430.77 659.71 C 430.77 658.86 431.46 658.17 432.31 658.17 C 433.15 658.17 433.84 658.86 433.84 659.71 C 433.84 660.56 433.15 661.25 432.31 661.25 Z M 432.31 656.94 C 430.78 656.94 429.54 658.18 429.54 659.71 C 429.54 661.23 430.78 662.48 432.31 662.48 C 433.83 662.48 435.08 661.23 435.08 659.71 C 435.08 658.18 433.83 656.94 432.31 656.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 694px; margin-left: 440px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">KMS Key</div></div></div></foreignObject><text x="440" y="706" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">KMS Key</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-63"><g><rect x="200" y="627" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 624px; margin-left: 201px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">backup-plan-stack</div></div></div></foreignObject><text x="260" y="624" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">backup-plan-stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-64"><g><rect x="240" y="647" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 273.64 685.18 L 273.64 656.09 L 267.27 656.09 C 266.77 656.09 266.36 655.68 266.36 655.18 L 266.36 648.82 L 246.36 648.82 L 246.36 685.18 Z M 268.18 654.27 L 272.35 654.27 L 268.18 650.1 Z M 275.45 655.18 L 275.45 686.09 C 275.45 686.59 275.05 687 274.55 687 L 245.45 687 C 244.95 687 244.55 686.59 244.55 686.09 L 244.55 647.91 C 244.55 647.41 244.95 647 245.45 647 L 267.27 647 L 267.27 647.01 C 267.51 647.01 267.74 647.09 267.92 647.27 L 275.19 654.54 C 275.36 654.71 275.45 654.95 275.45 655.18 Z M 269.62 678.29 L 268.1 679.81 L 266.59 678.29 L 265.3 679.58 L 266.82 681.09 L 265.3 682.61 L 266.59 683.89 L 268.1 682.38 L 269.62 683.89 L 270.9 682.61 L 269.39 681.09 L 270.9 679.58 Z M 252.27 656.09 C 253.02 656.09 253.64 655.48 253.64 654.73 C 253.64 653.98 253.02 653.36 252.27 653.36 C 251.52 653.36 250.91 653.98 250.91 654.73 C 250.91 655.48 251.52 656.09 252.27 656.09 Z M 252.27 657.91 C 250.52 657.91 249.09 656.48 249.09 654.73 C 249.09 652.97 250.52 651.55 252.27 651.55 C 254.03 651.55 255.45 652.97 255.45 654.73 C 255.45 656.48 254.03 657.91 252.27 657.91 Z M 261.39 678.71 C 260.9 678.78 260.42 678.81 259.94 678.81 C 257.81 678.81 255.76 678.13 254.03 676.84 C 251.51 674.97 250.06 672.09 250.03 668.95 L 251.85 668.94 C 251.87 671.5 253.06 673.85 255.11 675.38 C 256.85 676.68 258.98 677.22 261.13 676.91 C 263.27 676.59 265.16 675.47 266.45 673.73 C 269.12 670.15 268.38 665.06 264.8 662.39 C 261.46 659.9 256.81 660.38 254.03 663.36 L 255.45 663.36 L 255.45 665.18 L 251.82 665.18 C 251.32 665.18 250.91 664.77 250.91 664.27 L 250.91 660.64 L 252.73 660.64 L 252.73 662.1 C 256.13 658.47 261.81 657.89 265.89 660.93 C 270.27 664.2 271.18 670.43 267.91 674.82 C 266.33 676.94 264.01 678.32 261.39 678.71 Z" fill="#3f8624" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 694px; margin-left: 260px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Backup Plan</div></div></div></foreignObject><text x="260" y="706" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Backup...</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-65"><g><rect x="950" y="607" width="150" height="50" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 632px; margin-left: 951px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Stack</div></div></div></foreignObject><text x="1025" y="636" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Stack</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-66"><g><rect x="950" y="677" width="150" height="50" rx="7.5" ry="7.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 702px; margin-left: 951px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Construct</div></div></div></foreignObject><text x="1025" y="706" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-67"><g/><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-68"><g><rect x="860" y="837" width="220" height="210" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 834px; margin-left: 861px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Bastion Container</div></div></div></foreignObject><text x="970" y="834" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Bastion Container</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-69"><g><rect x="878.34" y="926.43" width="183.33" height="31.15" rx="4.67" ry="4.67" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 181px; height: 1px; padding-top: 942px; margin-left: 879px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">bastion-ecs-construct</div></div></div></foreignObject><text x="970" y="946" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">bastion-ecs-construct</text></switch></g></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-70"><g><rect x="580" y="837" width="240" height="210" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 834px; margin-left: 581px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="text-align: right;">BlueGreen Deploy</span></div></div></div></foreignObject><text x="700" y="834" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">BlueGreen Deploy</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-71"><g><rect x="600" y="947" width="200" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 962px; margin-left: 601px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">alb-blue-green-construct</div></div></div></foreignObject><text x="700" y="966" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">alb-blue-green-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-72"><g><rect x="600" y="852" width="200" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 867px; margin-left: 601px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ecs-service-construct</div></div></div></foreignObject><text x="700" y="871" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ecs-service-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-73"><g><rect x="600" y="997" width="200" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 1012px; margin-left: 601px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">pipeline-blue-green-construct</div></div></div></foreignObject><text x="700" y="1016" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">pipeline-blue-green-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-74"><g><rect x="600" y="897" width="200" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 912px; margin-left: 601px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">alb-blue-green-construct</div></div></div></foreignObject><text x="700" y="916" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">alb-blue-green-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-75"><g><rect x="300" y="837" width="240" height="210" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 834px; margin-left: 301px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">common construct</div></div></div></foreignObject><text x="420" y="834" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">common construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-76"><g><rect x="320" y="852" width="200" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 867px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ecs-commmon-construct</div></div></div></foreignObject><text x="420" y="871" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ecs-commmon-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-77"><g><rect x="320" y="947" width="200" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 962px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ecs-app-construct</div></div></div></foreignObject><text x="420" y="966" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ecs-app-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-78"><g><rect x="320" y="997" width="200" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 1012px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ecs-task-role-construct</div></div></div></foreignObject><text x="420" y="1016" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ecs-task-role-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-79"><g><rect x="320" y="897" width="200" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 912px; margin-left: 321px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">alb-target-group-construct</div></div></div></foreignObject><text x="420" y="916" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">alb-target-group-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-80"><g><rect x="40" y="837" width="220" height="210" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 834px; margin-left: 41px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="text-align: left;">Ecspresso (Rolling)</span></div></div></div></foreignObject><text x="150" y="834" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Ecspresso (Rolling)</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-81"><g><rect x="60" y="852" width="180" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 867px; margin-left: 61px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">alb-construct</div></div></div></foreignObject><text x="150" y="871" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">alb-construct</text></switch></g></g></g><g data-cell-id="l7UZtRPf9y5K1fjJFRUz-82"><g><rect x="60" y="897" width="180" height="30" rx="4.5" ry="4.5" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 912px; margin-left: 61px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">pipeline-ecspresso-construct</div></div></div></foreignObject><text x="150" y="916" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">pipeline-ecspresso-construct</text></switch></g></g></g><g data-cell-id="7N-v3ZshiFUB6YnmFjTH-0"><g><path d="M 1011 258 L 1049 258 L 1049 296 L 1011 296 Z" fill="#c925d1" stroke="none" pointer-events="all"/><path d="M 1035.97 288.43 L 1035.97 285.25 C 1034.6 286.14 1032.12 286.56 1029.76 286.56 C 1027.19 286.56 1025.18 286.11 1024.03 285.34 L 1024.03 288.43 C 1024.03 289.35 1026.17 290.3 1029.76 290.3 C 1033.42 290.3 1035.97 289.31 1035.97 288.43 Z M 1029.76 281.73 C 1027.19 281.73 1025.18 281.29 1024.03 280.52 L 1024.03 283.62 C 1024.05 284.53 1026.19 285.48 1029.76 285.48 C 1033.41 285.48 1035.95 284.5 1035.97 283.61 L 1035.97 280.43 C 1034.6 281.31 1032.12 281.73 1029.76 281.73 Z M 1035.97 278.79 L 1035.97 275.12 C 1034.6 276 1032.12 276.42 1029.76 276.42 C 1027.19 276.42 1025.18 275.98 1024.03 275.21 L 1024.03 278.79 C 1024.05 279.7 1026.19 280.65 1029.76 280.65 C 1033.41 280.65 1035.95 279.67 1035.97 278.79 Z M 1024.03 273.47 C 1024.03 273.47 1024.03 273.47 1024.03 273.47 L 1024.03 273.47 L 1024.03 273.48 C 1024.05 274.39 1026.19 275.34 1029.76 275.34 C 1033.74 275.34 1035.95 274.24 1035.97 273.48 L 1035.97 273.47 L 1035.97 273.47 C 1035.97 273.47 1035.97 273.47 1035.97 273.47 C 1035.97 272.7 1033.76 271.6 1029.76 271.6 C 1026.17 271.6 1024.03 272.55 1024.03 273.47 Z M 1037.06 273.48 L 1037.06 278.78 L 1037.06 278.78 C 1037.06 278.78 1037.06 278.79 1037.06 278.79 L 1037.06 283.6 L 1037.06 283.6 C 1037.06 283.61 1037.06 283.61 1037.06 283.62 L 1037.06 288.43 C 1037.06 290.46 1033.27 291.39 1029.76 291.39 C 1025.62 291.39 1022.94 290.23 1022.94 288.43 L 1022.94 283.62 C 1022.94 283.62 1022.94 283.61 1022.94 283.6 L 1022.94 283.6 L 1022.94 278.79 C 1022.94 278.79 1022.94 278.78 1022.94 278.78 L 1022.94 278.78 L 1022.94 273.48 C 1022.94 273.48 1022.94 273.47 1022.94 273.47 C 1022.94 271.67 1025.62 270.51 1029.76 270.51 C 1033.27 270.51 1037.06 271.44 1037.06 273.47 C 1037.06 273.47 1037.06 273.48 1037.06 273.48 Z M 1044.66 266.48 C 1044.96 266.48 1045.2 266.24 1045.2 265.94 L 1045.2 263.16 C 1045.2 262.86 1044.96 262.61 1044.66 262.61 L 1015.34 262.61 C 1015.04 262.61 1014.8 262.86 1014.8 263.16 L 1014.8 265.94 C 1014.8 266.24 1015.04 266.48 1015.34 266.48 C 1016.01 266.48 1016.54 267.02 1016.54 267.67 C 1016.54 268.33 1016.01 268.87 1015.34 268.87 C 1015.04 268.87 1014.8 269.11 1014.8 269.41 L 1014.8 280.53 C 1014.8 280.83 1015.04 281.07 1015.34 281.07 L 1020.77 281.07 L 1020.77 279.99 L 1018.06 279.99 L 1018.06 278.36 L 1020.77 278.36 L 1020.77 277.27 L 1017.51 277.27 C 1017.21 277.27 1016.97 277.52 1016.97 277.82 L 1016.97 279.99 L 1015.89 279.99 L 1015.89 269.89 C 1016.89 269.65 1017.63 268.74 1017.63 267.67 C 1017.63 266.6 1016.89 265.7 1015.89 265.46 L 1015.89 263.7 L 1044.11 263.7 L 1044.11 265.46 C 1043.11 265.7 1042.37 266.6 1042.37 267.67 C 1042.37 268.74 1043.11 269.65 1044.11 269.89 L 1044.11 279.99 L 1043.03 279.99 L 1043.03 277.82 C 1043.03 277.52 1042.79 277.27 1042.49 277.27 L 1039.23 277.27 L 1039.23 278.36 L 1041.94 278.36 L 1041.94 279.99 L 1039.23 279.99 L 1039.23 281.07 L 1044.66 281.07 C 1044.96 281.07 1045.2 280.83 1045.2 280.53 L 1045.2 269.41 C 1045.2 269.11 1044.96 268.87 1044.66 268.87 C 1043.99 268.87 1043.46 268.33 1043.46 267.67 C 1043.46 267.02 1043.99 266.48 1044.66 266.48 Z M 1023.49 270.21 L 1023.49 265.87 C 1023.49 265.57 1023.24 265.33 1022.94 265.33 L 1019.69 265.33 C 1019.39 265.33 1019.14 265.57 1019.14 265.87 L 1019.14 275.1 C 1019.14 275.4 1019.39 275.64 1019.69 275.64 L 1021.31 275.64 L 1021.31 274.56 L 1020.23 274.56 L 1020.23 266.41 L 1022.4 266.41 L 1022.4 270.21 Z M 1039.77 274.56 L 1039.23 274.56 L 1039.23 275.64 L 1040.31 275.64 C 1040.61 275.64 1040.86 275.4 1040.86 275.1 L 1040.86 265.87 C 1040.86 265.57 1040.61 265.33 1040.31 265.33 L 1037.06 265.33 C 1036.76 265.33 1036.51 265.57 1036.51 265.87 L 1036.51 270.21 L 1037.6 270.21 L 1037.6 266.41 L 1039.77 266.41 Z M 1035.43 269.67 L 1035.43 265.87 C 1035.43 265.57 1035.19 265.33 1034.89 265.33 L 1031.09 265.33 C 1030.79 265.33 1030.54 265.57 1030.54 265.87 L 1030.54 269.13 L 1031.63 269.13 L 1031.63 266.41 L 1034.34 266.41 L 1034.34 269.67 Z M 1028.37 269.13 L 1028.37 266.41 L 1025.66 266.41 L 1025.66 269.67 L 1024.57 269.67 L 1024.57 265.87 C 1024.57 265.57 1024.81 265.33 1025.11 265.33 L 1028.91 265.33 C 1029.21 265.33 1029.46 265.57 1029.46 265.87 L 1029.46 269.13 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 303px; margin-left: 1030px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ElastiCache</div></div></div></foreignObject><text x="1030" y="315" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Elasti...</text></switch></g></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>