<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="601px" height="321px" viewBox="-0.5 -0.5 601 321" content="&lt;mxfile&gt;&lt;diagram name=&quot;240822&quot; id=&quot;xNOoMu4lXysAkjPaJcP_&quot;&gt;5ZjLbqMwFIafxsuRgh2MWQYK7WKq0aSLdlYjB8xlauLIcULSpx8n2ASUKGo6TYumK45/Hy7+/nMsAKCw2txKuijuRco4gKN0A9ANgNBBBOvDTtk2ike8RshlmZqkg/BQvjAjjoy6KlO27CUqIbgqF30xEfM5S1RPo1KKup+WCd6/64Lm7Eh4SCg/Vh/LVBWNSqB30O9YmRf2zg72m5mK2mSzkmVBU1F3JBQBFEohVBNVm5DxHTzLJXmupn+q0RN22a/Fj6c75P/MvjUXiy85pV2CZHP15kvHyCnqx/vVb1iX2+2LEnA6NaeM1pSvDC8QIUAgmAQmCHwbxPsAAT+yOWM7NTGE1NZiZ6l2wQznYq4PQaEqrkeODl+5ILPwpVjJhJ1Zha0rKnOmzuS5Td7u2TrFYXDdMlExJbc6QTJOVbnuVxA1hZi3eQfYOjC8L2DvHLEPvkfHJOuiVOxhQfcEat2kfZRZyXkouJD7bBSGkRvHFyNeM6nY5iyUdkcwNWP2A+iacX3oLse2TNHpLDK6Ekf4X9Sw+8oaxoOqYfSl2HuDYj/+UuzJoNi7J9hjEDiAaMAe8MdgQgxy/QLVBAGyJngmmIR2ygaE/OP+7/theNX9H3lD2//x6T7ocr6xxU5s+Xs2iFvyGhnm+mGDmdRRvos+xcKMJCxJtL5UUjyzzsyMuGON+3rmYmdo5nrvZG44FHOzDJ42N8Uz7OIPNLft5E8zl7yTuZOBmJu6jKTjU+YSOEP4I81tX9OvYK4eHr5693Odfwco+gs=&lt;/diagram&gt;&lt;diagram id=&quot;fd1B5gNecEqGeb0Rrr6i&quot; name=&quot;240815&quot;&gt;3ZfPc6MgFMf/Gu4KieJRU5M9dE859Ewjq8ygZAjRZP/6JfHhj6W705lOa9uTX748xPfh8SZBZFNfdpodq5+q4BLhoLgg8oAwDkO8to+bc+2dKIh7o9SigKDR2IvfHMwA3LMo+GkWaJSSRhzn5kE1DT+Ymce0Vt087JeS812PrOSesT8w6btPojBV71Icj/4PLsrK7RxGST9TMxcMmZwqVqhuYpEckY1WyvSqvmy4vMFzXPp123/MDh+meWNesyDqF7RMniE3lBNEMUozEFnixPYuCEpyF7NyUylkY64OES8sMRg2qrGPrDK1tKPQSv8z4ctP6qwP8AoMJ8t0ySEK6ub28skySG3HVc2NvtoAzSUzop0fF4NTL4e4EYwVwOZlTtjjlD3maUu8vLtKGL4/snsSnS3/Vybecm345b9JuVlXO3B58ArG3ViKQ0w1KUMavJ0D/cT1svbrJV6qXlyn+iqgksVA+TfrU4MKw6VIrV8AFaEsRNTSiFGyQikFPjQCkRFHLAaRbtyUE5Qu0MRItGATiz2Sj6IVTYlwJO022bO2qrypL8MzChbkmXg8d7xlTSO+EdChYD8CqGsyf931xF73AOUUpRGiWw/cg+uF1HXH2IntBOW3OZIB/zsciR2OP8Lvc5O/MiT/Aw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 120 160 L 233.63 160" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 238.88 160 L 231.88 163.5 L 233.63 160 L 231.88 156.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 160px; margin-left: 180px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                カスタマイズ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="163" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    カスタマイズ
                </text>
            </switch>
        </g>
        <rect x="0" y="120" width="120" height="80" fill="#cce5ff" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 160px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                BLEA
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="164" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    BLEA
                </text>
            </switch>
        </g>
        <path d="M 360 160 L 473.63 160" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 478.88 160 L 471.88 163.5 L 473.63 160 L 471.88 156.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 160px; margin-left: 420px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                カスタマイズ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="163" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    カスタマイズ
                </text>
            </switch>
        </g>
        <path d="M 360 190 L 474.3 247.15" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 479 249.5 L 471.17 249.5 L 474.3 247.15 L 474.3 243.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 220px; margin-left: 420px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                カスタマイズ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="223" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    カスタマイズ
                </text>
            </switch>
        </g>
        <path d="M 360 130 L 474.3 72.85" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 479 70.5 L 474.3 76.76 L 474.3 72.85 L 471.17 70.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 100px; margin-left: 420px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                カスタマイズ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="103" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    カスタマイズ
                </text>
            </switch>
        </g>
        <rect x="240" y="120" width="120" height="80" fill="#99ccff" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 160px; margin-left: 241px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                汎用テンプレート
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="300" y="164" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    汎用テンプレート
                </text>
            </switch>
        </g>
        <rect x="480" y="120" width="120" height="80" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 160px; margin-left: 481px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                プロジェクトB
                                <br/>
                                テンプレート
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="164" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    プロジェクトB...
                </text>
            </switch>
        </g>
        <rect x="480" y="240" width="120" height="80" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 280px; margin-left: 481px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                プロジェクトC
                                <br/>
                                テンプレート
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    プロジェクトC...
                </text>
            </switch>
        </g>
        <rect x="480" y="0" width="120" height="80" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 40px; margin-left: 481px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                プロジェクトA
                                <br/>
                                テンプレート
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    プロジェクトA...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
