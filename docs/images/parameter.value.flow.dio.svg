<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="941px" height="1021px" viewBox="-0.5 -0.5 941 1021" content="&lt;mxfile&gt;&lt;diagram id=&quot;yny-sU1lQc9J4URSI4Re&quot; name=&quot;ページ1&quot;&gt;7R1Zd6LM8rfcB8/M95AcZOcRFHfFXeMbAgKKgGyCv/52s7iiMYnJJHNmJmcC1QvVVdXVtcEUsNI6rDqirbUtWTEKKCKHBaxcQFGqSIN/ISBKACRBJgDV0eUEVDwABvpOSYFICvV1WXFPOnqWZXi6fQqULNNUJO8EJjqOtT3ttrCM06faoqpcAAaSaFxCJ7rsaQmURqkDvKboqpY+maCxpGEtZn3ThbiaKFvbIxDGF7CSY1lecrUOS4oBSZeRJRlXudK6x8tRTO+eAWgyIBANP11aipcXZWt1LN+UFdgfKWDcVtM9ZWCLEmzdAuYCmOatDXBXBJeXz09RChTHU8IjUIpPVbHWiudEoEvaSqSkSUWDTm+3BzozeArTjmhcRDKaiilz1f3UBwKAi5QGV+jxaIIY4lwxOFFaqfGwkmVYTjwNxhOlUgWgxbmeY62UrEVWFqJvgEVxC8v0UtEvoun90Xgk/gPhumFkcNMylcewoYic8qGIFS8YQdB5jKCZj/MBy2EDGVPFtUXzhB/kxoe7hTu5UtPfe0I+aghseHJjrrCgQ5Gww5sTzTPAXAdoV+aGIj6pvuJ6T4rkirb95Ipr21CePTcbAXudzwJgCUZnkzuvQ64MBeCEkqdgWQ+uL3shrnUjShZeskzXMgCLUcBLIAUkWAbol3YvWb6jKwAXpKNszxuTIWvLtNxk2wAIYuim8pSJUUxZ5hXK3s0i9MpE+tq2HDhFgYIYDDSwXfqKC1CXFHfggT0bt5XhAeFYa/Dr1/MzoJuhzyH1kg4VFw57crJxTzH8110MiIl9Cn2kdN9Lh3+y83aaAcPC9RLkb/Ntvlf9T1Kio+G8jjr/jRJEshoUwfcX/1172kJXr5E/nvFVRJKVJWA4QPdEQ5fuoZK0P3MOHePTZ7G4HFsvneN6lwQh8fz7Vfh37gFH2fi6o/xONiY478Q1kKrKr3gqOK1iBk0l+u/ouf6Bbm/doh+xAwCxVRNcG8oCTgbPYR3Yk2wK9izYXwWbwk3nu2I4pAd8zpl/akakQFl0tRjFBxlnd1kFTI5V8AijgLgwCkrl5gVbgEK24aW+jo34PalbkKBdy9U93YIkn1ueB7T6VUofDLF8ZomunbgWCz2EJObiB7IZFMkgcCrRE8GuS26BnJpgf5T0MSf0t0izqlos+NMZjDR+pIKrGgL+4ZgS2wa/S/jU3k5hB3baGfSROuu4uET2IKBv9kZFDvQJl9uAfumNIBAd8eGoL60MRO4IqGfbwJTiqhVCURbAx+J8j/IHA7JTxHBPljBpuyt2dyFAqgvZkP5wJEXLHAtYW5lOmKBWB+5aZQlOOw4+l98Mi1GM80CuNITR0cD3/7AC4GQlCOtw3obEazNpy7KVkAekkPqsZK3pHQ2axvPBqM+Nq1qZGm8a3qZt8XW0MZlyLTicAccGx7yUehrHqztkN6N2ZlCV+rSjjrxJCyOkQYOPZMEsayHuDsEQnsaKiEwFDDS7CERN1l91dw4WjHaaJcd6pLKU1YnNiFJZIpUh68o7qUMtuqjYoWky8CYEOKgqcGCpupkHJaiDps52sSQ7Swtcl3XKCASzgbcDZthHFG6i4GSpxi7BiHWna9I6tdlC7rQ7rSq2tHB2NejVVWXdo0aY1FJ9oxjUwTw90GW3mAVjky+Ly8iehtq41aeB18lxw2ZHtyechKHBbNhrRnxXMI3+ohVpWJNn5vO6AjQft40wcS2ypCU0BYndTUi6N65vOH7kj8Vuk6ftqF0ZI2Omzxgbpxv1aza1lCUz2k0taTYnxFaHnbZJpQ+3KtbTyr1tX5lIdUZSfQ5DaUFa4bYjliEBCF2DjJVDAmc1zIPSA6k03ggTd7JdE2ip157KvdYw2FWra6wqDNiaP4toyVraLwjje0K0kDAH7G7OWgA2VByB1oZRpU8xGD7Dpw2W8plOY1PjKMDuWmNWdvSi7KzHju3tdA+pNnfOgCGG0M7gyh5GsaikanMg51zlpYxvhblKzOsSuA1EN+LbvrTUDdMPqyPIxw5oqHhoW2UIA1UxXpOxaA5MO4HoUlpUnmJD9CWMIFZGgOwkVGuR3IttllWnzWEjpDttiQMRC2wwV2RN2LZENCaIrfaXEinrUOXNeu0uvSGJyao8RfDKyudXvAkECY6gd7KA6JuAlXfgzpblZZMqaq3hCCF1q2Qjm6iszRpCWJot1uVxtJZnfU1ooStuPWiteiOhYQd9qddHdqOxh7cGosWMVyq+chR/qiPmBIgSNy2q2+VKjjZ+vet1RvWdFAQzLAK+NdZBd1DW+qgp96xeXRiOh3x1vF75daQ+eOkK8/Gy3i26L8uwSE6kVst9KbFysU11dWYrCYJnoIsa4xr1GuMMaq6A89ZkTHcEjyMEaYB1wMO9+sTDdnV0Y1V7LyFVJOn1GG3QTJPyulNVLnprbtFxG52qpkR1E+1PyuYIIj1suXp3MRDCijHRRku1zVMGrsl2b1mXpZHM2bOGJkGOYoqpRtu60CvrptGWIkFGhlTF29SqdPFlYdRlYlQfLOZtZeWrdYfCN9ZSqTVnyBARN2rfNQeAAC/gcJlE3NRuSFOBY2S9YzfA1EZzRRoBs9xwnfpS0MN5Pwga0oqbMKbpr0lFmyoTcadOe00CLpX2plNzZTVNbvYy6EYjPvD7vlh2VmoTX413c7pGRqI8RCWUHetTF+92az1h1pdwxVrAw69COT1EIUig9sXIGHKRLw27+HweMty0zXd8joJ7amrypmpA7NohaYTCsrt1hlAxT8cdbLorlb1gMVOgA2oi3qZDbNvcplgrjddQ87YCf4H3dmTbjtC6ElUcf0g1ez3X5auuGXpLV9x6amOOtHGaq3IzEenhymDdWdUFpVEv04ivtXWDGm8rsoHXPBXd6W3X15p0G5i13HxDlvA145EsWpGgiutUCXRQdJeCYC59cukbvFNrNIZdbam9TEKFL1lYv0j0+5jSE7WZHYmqKylzjt+GOy2orZtObT6WB0Dxmiw441RajlrBZDcQFmJp41jDRt3lWo7Hjl2jEhCoY1PerqlULEy0XjbV3m7Zcqrs3Bq6zdm0w9arTdfeTbgSpTu0WmQrFL1oAJkr94M2wHUps3Cndl/mS1QUmA3fw9VlUeLEDiR6oEjYRIGHIrUVYjUP/UEEJcdgAzQ9XGZ73drORkkZgWTetio7aoOxO33KjxdloiG0gEoaGitqtQHtYadC2jNoHujVBuVtSHoCNZCtV0MLWkYegw3DAVYeF2VD7uo1QxMMzqst9dVU78YHKKL6Qp2eB33VsREv1DuVUp1Zjp1eaUVUGH1VNalihzAdshbaU4duTLXZclH3w1CfatXWi1DjufYQtzpFiEQw8vm221sh0rS59ZgOGgpuO6xzgGt1S+0Sg7nbHGD9cbHWxWp6WF8NGwgJtx4PBmtSsCpb23BYcnozk4rPdHYwGgv9JlF6qdehofYpscIcYzTHFiUeESikLwxPRVaVQXprOZ5mqZYpGvwByp16DEfegRLq3jQFw+sXeP0MDN7kthwetZWj7MYEOMejnons9iWbEN4chsV32bh744quJ4KNBKPlx04GgFV0SJZ0ZjnrIUHnJfYoATDtctPrSCImaQIgDeKLjqp4JyFASNWbouAohujpwWlkPo+18VCArRgddbAt3YSxt/3MXQg4SBh15vCg+FlA/aw/xhC3+sONGmNwELL9Uu6TO+YyMspTBQYtcEiBJwosWqCxAo/BfwEq8AItAEs+vcDTJpYr8HSBZQtsGV6AXcuVcgU69pryXVkJMFRxcvyjtS7Libwrrr4T5/F8UFpSYoPJCa5AlN8gi5dClG3AC2Wxz/qkDz7JrOQpEeS5iKD4Cdee0pneJlsHYcimwU5HWIuFC8T7XM28TQLIq6HxbxHn3gdhgKpQvz6u/YOCkNdQffuIr3j2w8LMj4hQKmESur8WTXxEeHYfld3HaTEkPzw7tqUulPqbAVrdXDw/kgT11566j6lS70tF/BkRSTaaOXftdA2P4OARK68E2HXZuSu8vtdgv4rw2ECewV8woEj+yldzpR9M+rUYsjs3GYf+pKVQ5dfPiXxsPwL9hCqBLNj//kKAu9IOD3DELszk7P7IFUNzqwUeUbWRYxNftRuuKpIz7YEkKiP7legN5MKMqClGoECaHrXnyChyrtlNy1mLxlFbIDq6CH4Dzoie7yjuK/0k0b7WZXtkCeCp+YwYiges9SdoseimejnScmxNNPfbPYYB5ntPqQhB8N7iz9p0IOJm+iQkW2rc4jlgsgWYP3tSWpcCBSIuvzp6zNZy5FPE9nOBtcxXOpgOzplkuJ5SkTrpdyvNWsyU/+HivyNMZUWyHBEmhZ48TZdWpuK62ZGte3pGn/O+x6f0rX5H6Jz0WxiW6J0TR9Zd2xCjrDu068DF/5JKBdHMV5xPUiFOdOqOZa7TLQzO/ByvkCgwSIEuQwUJ3cBigaUKPFmgSwWairvxBYZNmxgqu6jcmup2ScDx9ruSpgPayLB873UdaSuODjQDlL9sUPcA+gQVekgA2mnWEI2DLTgG1UgyGYEcNSetdGyUuPGj8l3XN2tXDD/LuhZztGt+UdwDtGtWrJmjXv9KbytzHtyTgqC9PW1CFPOqhX6Ltp2ujwRLxMEmtJddR1noIdhxT6cj4i6lC/P8oxbUNfP5VinHYhEL/8V06wi4GKVTy/jElLqv8OTzK2Be96u+ws17DBYfclVObPQ3iNI7d9N1zwFKTvvIfUjY95xR8Dl1LV7D98Mbgir/96V2/F9a5YNi9GViJa/Kh3pEZgW/bs3nbytgPgLL9mhLARv599PTMTx/L73Z5bwWBb2jJPQ0EvreqOfNA/eq3/GZJ/GFx3HsVPytQdGz0mGg1wq5pcKxKeH4kgczqrb0tL8/KxB+SOAkN0b+hplvHE/pmUQlv/H0aEKunudHF7d33j3eeGyiSGTqyF6b6YoH8vE4z9sZ8RURbh164gu4Cx9b+5tj13Ydy3avY/LYVXuKKd94mCSvHhpFv2uBD7XQb2/BDx9lfyhF9PV+B9CjcbjoM72nCzvW9NfzNAD2WWHkd9i43+0g+TLp/AOJxdh2v/qy0FerwsNbSq+w6ooleVXy94aK5fx2JctWDsZjas+UUpP9aC/GMDtWpg9/J+WN59J/d+vtWyrsHOT6tpLRIyPAYdGvu5p3Mebuod9H1edKDzy8Erv4KGwG9OlvT9Pd8zgZpN/zcbQMmtRfFiK7A3Q4lGKSJgh/6in33mDOoxd9fP4lyz4GfzmW94R0PvbQBx6or0315dD5AfZ5+ep7XmgnCt8yj30e+cKwy0xL7lvvGPOA0BeWF/o6L8rMq7g9Ys0pH18heaWSkjy/APmV+uNX652vMuS49DdLL71a6HtnVfd7azZTCcDpUwnAz2OaCe7pqBuVwAxzOg9+Nk9S63wxz9sLlE9DtSRzE6uz7viDy5Oxy3c0/04J/ibSeq6v8PN3G+6V1vOJyPO6nM8R1wzfO8U1w+rOPXfW/ePSnVd7/YOle/8GyRPyjCAnL5Egz0WaKtx+kSS+O6rBKN8sdzh5twP51rvoQvjfqfMp+nM20QW+2Nt20du6U8zJkfLxXUT9Xbvop50RdPHstZR3SjfDnM3zOUdEhu6d0pph9cZFPEy482pRz4QbOk32Van5+AtN5wyn7nZiHvFmZPawn5e/T4Ke4mW29l/u/i/K3T8unvIn1/uTc8ywDOyDKdd/idj7Q6j/ErH/ErGfLJ0/WctlKd20iOsrU7hH6dT3qrw/kdg9n/TLToazrO77cqf/8rzf30B7Q445PhoKp1lmRUKfTzPNv8Dtr++TTdZtVpYdxXWz1/wgxvUD8BlW+f/OENp/svIrss578P6p/32LVPTPy0J/muX6L2MdRxX+yoz1Pn70esr6a6I5OS//YXsZOWIOcucXEb6KiTkvfmJv4+CHmJO2XqRnceaZYUic3v9cROb2b8SfhILJZ4ogDz/kxzmLX4/TPe6T4h+Kyu0/JbQPRBwF4nINqvk57I98HP3fd4n+fgv1sdL1k6N5DyNG3heN7vx4/I/+xJG0ryrNC6X9tNWsX4nSfe/1nJi/3xvVdxxM3wvzP6m5/yKF+zX/PceXfKXiG3ym75GMevUTfQ/cnO/227/m42hvgX6eh/gDP6SWV4Ce+yE19B2f+gG3h/9qMCl2Ofx3jRj/fw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="0" width="940" height="1020" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="50" y="51" width="580" height="189" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="50" y="51" width="590" height="89" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 588px; height: 1px; padding-top: 58px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="">
                                    <font style="">
                                        <font style="font-size: 15px;">
                                            <b>
                                                bin/blea-guest-ecsapp-sample.ts
                                            </b>
                                        </font>
                                        <br/>
                                        <br/>
                                    </font>
                                </span>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <font style="font-size: 12px;">
                                        import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
                                    </font>
                                </div>
                                <span style="">
                                    <font style="font-size: 12px;">
                                        <br/>
                                    </font>
                                </span>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <font style="font-size: 12px;">
                                        const
                                        <span style="background-color: rgb(255, 204, 204);">
                                            config
                                        </span>
                                        :
                                        <span style="font-style: italic;">
                                            <font color="#0000ff">
                                                IConfig
                                            </font>
                                        </span>
                                        =
                                        <u style="">
                                            require('../params/' + envKey)
                                        </u>
                                        ;
                                    </font>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="70" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    bin/blea-guest-ecsapp-sample.ts...
                </text>
            </switch>
        </g>
        <image x="-0.5" y="0.5" width="50" height="50" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="12" y="59" width="27" height="15" stroke-width="0"/>
            <text x="24.5" y="68.5">
                CDK
            </text>
        </g>
        <path d="M 650 150 L 650 160 L 345 160 L 345 146.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 345 141.12 L 348.5 148.12 L 345 146.37 L 341.5 148.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 157px; margin-left: 476px;">
                        <div data-drawio-colors="color: #000000; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                環境ファイル読込
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="476" y="160" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    環境ファイル読込
                </text>
            </switch>
        </g>
        <rect x="650" y="120" width="280" height="120" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 278px; height: 1px; padding-top: 127px; margin-left: 652px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="">
                                    <font style="font-size: 15px;">
                                        <b>
                                            params/stage.ts
                                        </b>
                                    </font>
                                    <br/>
                                    <br/>
                                </font>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        <div style="line-height: 19px;">
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    <span style="font-style: italic;">
                                                        export
                                                    </span>
                                                    const
                                                    <span style="background-color: rgb(204, 255, 230);">
                                                        VpcParam
                                                    </span>
                                                    : inf.
                                                    <span style="font-style: italic;">
                                                        IVpcParam
                                                    </span>
                                                    = {
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    <span style="background-color: rgb(204, 204, 255);">
                                                        cidr
                                                    </span>
                                                    :
                                                    <b>
                                                        '10.100.0.0/16'
                                                    </b>
                                                    ,
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    maxAzs: 2,
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    };
                                                </font>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="652" y="139" fill="#000000" font-family="Helvetica" font-size="12px">
                    params/stage.ts...
                </text>
            </switch>
        </g>
        <path d="M 290 30 L 530 30 L 530 80 L 415.2 80 L 393.2 130 L 405.2 80 L 290 80 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 55px; margin-left: 291px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <br/>
                                <span style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    -c environment={環境名}で指定した環境名
                                </span>
                                <br/>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="410" y="59" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    -c environment={環境名}で指定した環境名...
                </text>
            </switch>
        </g>
        <rect x="50" y="158" width="590" height="70" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 588px; height: 1px; padding-top: 165px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
                                    </div>
                                    <div style="">
                                        <font color="#ff0000">
                                            myVpcCidr
                                        </font>
                                        :
                                        <span style="background-color: rgb(255, 204, 204);">
                                            config
                                        </span>
                                        .
                                        <span style="background-color: rgb(204, 255, 230);">
                                            VpcParam
                                        </span>
                                        .
                                        <span style="background-color: rgb(204, 204, 255);">
                                            cidr
                                        </span>
                                        ,
                                    </div>
                                    <div style="line-height: 19px;">
                                        <div>
                                            myVpcMaxAzs: config.VpcParam.maxAzs,
                                        </div>
                                    </div>
                                    <div style="">
                                        });
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="177" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {...
                </text>
            </switch>
        </g>
        <rect x="50" y="250" width="580" height="390" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 578px; height: 1px; padding-top: 257px; margin-left: 52px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <b style="border-color: var(--border-color);">
                                    <font style="font-size: 15px;">
                                        lib/stack/share-resources-stack.ts
                                    </font>
                                    <br/>
                                    <br/>
                                    <div style="font-size: 12px; font-family: Consolas, &quot;Courier New&quot;, monospace; font-weight: normal; line-height: 19px;">
                                        <div style="">
                                            <div style="line-height: 19px;">
                                                <div style="">
                                                    import { Vpc } from '../construct/vpc-construct';
                                                </div>
                                                <div style="">
                                                    <br/>
                                                </div>
                                                <div style="background-color: rgb(2, 27, 45); color: rgb(255, 255, 255);">
                                                    <span style="color: #ffc600;"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="">
                                            <span style="font-style: italic;">
                                                export
                                            </span>
                                            interface
                                            <span style="font-style: italic;">
                                                ShareResourcesStackProps
                                            </span>
                                            <span style="font-style: italic;">
                                                extends
                                            </span>
                                            cdk.
                                            <span style="font-style: italic;">
                                                StackProps
                                            </span>
                                            {
                                        </div>
                                        <div style="">
                                            <div style="border-color: var(--border-color);">
                                                <div style="line-height: 19px;">
                                                    <div style="">
                                                        <font color="#ff0000">
                                                            myVpcCidr
                                                        </font>
                                                        : string;
                                                    </div>
                                                    <div style="">
                                                        myVpcMaxAzs: number;
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="">
                                            }
                                        </div>
                                        <div style="">
                                            <br/>
                                        </div>
                                        <div style="">
                                            <div style="line-height: 19px;">
                                                <div>
                                                    <span style="font-style: italic;">
                                                        export
                                                    </span>
                                                    class ShareResourcesStack
                                                    <span style="font-style: italic;">
                                                        extends
                                                    </span>
                                                    cdk.Stack {
                                                </div>
                                                <br/>
                                                <div>
                                                    constructor(scope: Construct, id: string, props:
                                                    <span style="font-style: italic;">
                                                        ShareResourcesStackProps
                                                    </span>
                                                    ) {
                                                </div>
                                                <div>
                                                    super(scope, id, props);
                                                </div>
                                                <div>
                                                    <br/>
                                                </div>
                                                <div>
                                                    <div style="line-height: 19px;">
                                                        <div style="">
                                                            const vpc = new Vpc(this, `${props.pjPrefix}-Vpc`, {
                                                        </div>
                                                        <div style="">
                                                            myVpcCidr: props.
                                                            <font color="#ff0000">
                                                                myVpcCidr
                                                            </font>
                                                            ,
                                                        </div>
                                                        <div style="">
                                                            myVpcMaxAzs: props.myVpcMaxAzs,
                                                        </div>
                                                        <div style="">
                                                            });
                                                        </div>
                                                        <div style="">
                                                            }
                                                        </div>
                                                        <div style="">
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="272" fill="#000000" font-family="Helvetica" font-size="15px">
                    lib/stack/share-resources-stack.ts...
                </text>
            </switch>
        </g>
        <path d="M 50 193 L 50 189 L 20 189 L 20 360 L 42.63 360" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 47.88 360 L 40.88 363.5 L 42.63 360 L 40.88 356.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 50 370 L 20 370 L 20 540 L 43.63 540" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 48.88 540 L 41.88 543.5 L 43.63 540 L 41.88 536.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 50 540 L 50 550 L 20 550 L 20 719 L 47.1 719 L 47.1 721.82" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 47.1 716.57 L 50.6 723.57 L 47.1 721.82 L 43.6 723.57 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 50 733 L 20 733 L 20 913 L 42.63 913" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 47.88 913 L 40.88 916.5 L 42.63 913 L 40.88 909.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="50" y="650" width="580" height="350" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 578px; height: 1px; padding-top: 657px; margin-left: 52px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <b style="border-color: var(--border-color);">
                                    <font style="font-size: 15px;">
                                        lib/constract/vpc-construct.ts
                                    </font>
                                    <br/>
                                    <br/>
                                    <div style="font-size: 12px; font-family: Consolas, &quot;Courier New&quot;, monospace; font-weight: normal; line-height: 19px;">
                                        <div style="">
                                            <div style="line-height: 19px;">
                                                <div style=""></div>
                                            </div>
                                        </div>
                                        <div style="">
                                            <div style="line-height: 19px;">
                                                <span style="font-style: italic;">
                                                    export
                                                </span>
                                                interface
                                                <span style="font-style: italic;">
                                                    VpcProps
                                                </span>
                                                {
                                            </div>
                                        </div>
                                        <div style="">
                                            <div style="border-color: var(--border-color);">
                                                <div style="line-height: 19px;">
                                                    <div style="">
                                                        <font color="#ff0000">
                                                            myVpcCidr
                                                        </font>
                                                        : string;
                                                    </div>
                                                    <div style="">
                                                        myVpcMaxAzs: number;
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="">
                                            }
                                        </div>
                                        <div style="">
                                            <br/>
                                        </div>
                                        <div style="">
                                            <div style="line-height: 19px;">
                                                <div>
                                                    <div style="line-height: 19px;">
                                                        <span style="font-style: italic;">
                                                            export
                                                        </span>
                                                        class Vpc
                                                        <span style="font-style: italic;">
                                                            extends
                                                        </span>
                                                        Construct {
                                                    </div>
                                                </div>
                                                <br/>
                                                <div>
                                                    constructor(scope: Construct, id: string, props:
                                                    <span style="font-style: italic;">
                                                        VpcProps
                                                    </span>
                                                    ) {
                                                    <br/>
                                                </div>
                                                <div>
                                                    super(scope, id, props);
                                                </div>
                                                <div>
                                                    <br/>
                                                </div>
                                                <div>
                                                    <div style="line-height: 19px;">
                                                        <div style="">
                                                            <div style="line-height: 19px;">
                                                                <div style="">
                                                                    const myVpc = new ec2.Vpc(this, 'Vpc', {
                                                                </div>
                                                                <div style="">
                                                                    ipAddresses: ec2.IpAddresses.cidr(
                                                                    <u>
                                                                        props.
                                                                        <font color="#ff0000">
                                                                            myVpcCidr
                                                                        </font>
                                                                    </u>
                                                                    ),
                                                                </div>
                                                                <div style="">
                                                                    maxAzs: props.myVpcMaxAzs,
                                                                </div>
                                                                <div style="">
                                                                    });
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div style="">
                                                            }
                                                        </div>
                                                        <div style="">
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="672" fill="#000000" font-family="Helvetica" font-size="15px">
                    lib/constract/vpc-construct.ts...
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 928px; margin-left: 321px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                =
                                <b>
                                    '10.100.0.0/16'
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="932" fill="#000000" font-family="Helvetica" font-size="13px" text-anchor="middle">
                    = '10.100.0.0/16'
                </text>
            </switch>
        </g>
        <rect x="650" y="250" width="280" height="200" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 278px; height: 1px; padding-top: 257px; margin-left: 652px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 12px;">
                                    <b style="font-size: 15px;">
                                        params/interface.ts
                                        <br/>
                                    </b>
                                    <font style="font-size: 12px;">
                                        <br/>
                                    </font>
                                </font>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        <div style="line-height: 19px;">
                                            <div style="">
                                                <div style="line-height: 19px;">
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            <span style="font-style: italic;">
                                                                export
                                                            </span>
                                                            interface
                                                            <span style="font-style: italic;">
                                                                <font style="font-size: 12px;">
                                                                    IVpcParam
                                                                </font>
                                                            </span>
                                                            {
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            cidr: string;
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            maxAzs: number;
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            }
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            <br/>
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            <div style="line-height: 19px;">
                                                                <div style="">
                                                                    <span style="font-style: italic;">
                                                                        export
                                                                    </span>
                                                                    interface
                                                                    <span style="font-style: italic;">
                                                                        <font color="#0000ff">
                                                                            IConfig
                                                                        </font>
                                                                    </span>
                                                                    {
                                                                </div>
                                                                <div style="">
                                                                    <span style="background-color: rgb(204, 255, 230);">
                                                                        VpcParam
                                                                    </span>
                                                                    :
                                                                    <span style="font-style: italic;">
                                                                        IVpcParam
                                                                    </span>
                                                                    ;
                                                                </div>
                                                                <div style="">
                                                                    }
                                                                </div>
                                                            </div>
                                                        </font>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="652" y="269" fill="#000000" font-family="Helvetica" font-size="12px">
                    params/interface.ts...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>