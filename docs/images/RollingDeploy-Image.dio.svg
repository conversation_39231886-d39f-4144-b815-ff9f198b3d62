<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="729px" height="197px" viewBox="-0.5 -0.5 729 197" content="&lt;mxfile&gt;&lt;diagram id=&quot;PXPQwqdl3H-qCSlhmvf-&quot; name=&quot;240823&quot;&gt;7VpRb9owEP41SNsLIo7jhEeg0D10UqVp2mNlEhOymjhyDIX9+tnEIUldMtI2hK60UknOZ/t89/nznUvPnqy2txwny+8sILQHBsG2Z9/0ALAs4MgPJdllEtcdZoKQR4FWKgQ/oj9ECwdauo4CklYUBWNURElV6LM4Jr6oyDDn7KmqtmC0OmuCQ2IIfviYmtJfUSCWmdQDbiH/RqJwmc9sIb2+Fc6V9UrSJQ7YU0lkT3v2hDMmsqfVdkKocl7ul6zf7EjrwTBOYnFSB6DtELt8cSSQa9WvjIslC1mM6bSQjpdiRWWjJR8XLBYTRhnf97WH0LmZyUWMs1HVUEcN06KUrbmvtbSXBOYhEeWQa+NvCVsRwXdShxOKRbSpDo91XMODnu464hzvSgoJi2KRlka+VwKpkEMU6vhogFromRuf6Xu16vIhMyB/K62kEO0jcyRKevgNpmu9YgltgaOYcCN+6SMRvkLZQMaBrQWVWpPDRlDCRURpKWgIjcFMmjpOBWePpNzie2S+kC0BTpck0N03hItIboY7PCf0nqWRiFgs2+ZMCLYqKYxoFKoGwRIpxfrNlwiQZhsw0tvcAvm7XpKach8vwqcbosKW9ZFbJ1Htq22oWKaPn1LYP7jlwTosKN+iSoLTJPPDItqqBWmgKovJth6qJgZ1B1QNPhjo96eCGnI4LUuskMteAm0JOfXAsK7AuFxgWF6HyLAvndiP+K4ZsZvM7Qz6A+A5rgX3f22nysxg2B8Ohw50IIKe63iwOkFmtB6zhvCHtbMAy+1bjofkHJZsA6g6SeYGY5K2D6kD/Fo6pYYGF43uxq9hoSr05O9MTTiWuzmISNEWs5iYpAVvgDsaGaSllbvmK4NmTiYwnCRU2qJMfKAMBw9zTHHs7w0w6YyqJY2x/xhyto6D3AsBWeA1Fe9Eb6CKJ++M7AbPym5lKrO8F7jMOdl37eatEDSjBBu1nLg6dfkJ2iNxrp5C9fTlZxJgQb6+R+YymyF0zVxeu7Vt2GHmgq457eUiA3ZZ7biXxfqoJdY3ad0+S1JruxeQ1TY9wmDbdy/eNa39VGktHHSW1gLzOqeW8XT0C+K7YyqUe1f9JkLsdMzwWrDTqDCP0YKS7UjdlDco+U2PllyWe/GNTAiO1LzNue4IzbyezipBbsYx/whzmwdbzuLlgw3YLR1sTc8Ct/7GwtBH9Tcibz4Lcsdcy5mPlbQi0F3SCuAVMx8RM26HJXD+//FLOQ9gS+eBSfhWtQKBVTp/pzoHOXWznKfMaXq0uS3f1AHz1uVa5vzPZQ7yuitzXANrtXz3+cocB0iGKv1YL+795sR3hHTOUfTI1+JrTZl68eUwe/oX&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 84 40 L 84 70 Q 84 80 74 80 L 34 80 Q 24 80 24 90 L 24 120.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 24 126.04 L 20.5 119.04 L 24 120.79 L 27.5 119.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="4" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 43.09 127.16 L 4.91 127.16 C 4.41 127.16 4 127.57 4 128.07 L 4 151.93 C 4 152.43 4.41 152.84 4.91 152.84 L 43.09 152.84 C 43.59 152.84 44 152.43 44 151.93 L 44 128.07 C 44 127.57 43.59 127.16 43.09 127.16 Z M 5.82 151.02 L 5.82 128.98 L 42.18 128.98 L 42.18 151.02 Z M 8.77 148.75 L 10.59 148.75 L 10.59 131.25 L 8.77 131.25 Z M 13.55 148.75 L 15.36 148.75 L 15.36 131.25 L 13.55 131.25 Z M 18.32 148.75 L 20.14 148.75 L 20.14 131.25 L 18.32 131.25 Z M 23.09 148.75 L 24.91 148.75 L 24.91 131.25 L 23.09 131.25 Z M 27.86 148.75 L 29.68 148.75 L 29.68 131.25 L 27.86 131.25 Z M 32.64 148.75 L 34.45 148.75 L 34.45 131.25 L 32.64 131.25 Z M 37.41 148.75 L 39.23 148.75 L 39.23 131.25 L 37.41 131.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 24px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="24" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <rect x="124" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 163.09 127.16 L 124.91 127.16 C 124.41 127.16 124 127.57 124 128.07 L 124 151.93 C 124 152.43 124.41 152.84 124.91 152.84 L 163.09 152.84 C 163.59 152.84 164 152.43 164 151.93 L 164 128.07 C 164 127.57 163.59 127.16 163.09 127.16 Z M 125.82 151.02 L 125.82 128.98 L 162.18 128.98 L 162.18 151.02 Z M 128.77 148.75 L 130.59 148.75 L 130.59 131.25 L 128.77 131.25 Z M 133.55 148.75 L 135.36 148.75 L 135.36 131.25 L 133.55 131.25 Z M 138.32 148.75 L 140.14 148.75 L 140.14 131.25 L 138.32 131.25 Z M 143.09 148.75 L 144.91 148.75 L 144.91 131.25 L 143.09 131.25 Z M 147.86 148.75 L 149.68 148.75 L 149.68 131.25 L 147.86 131.25 Z M 152.64 148.75 L 154.45 148.75 L 154.45 131.25 L 152.64 131.25 Z M 157.41 148.75 L 159.23 148.75 L 159.23 131.25 L 157.41 131.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 144px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="144" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 84 40 L 84 70 Q 84 80 94 80 L 134 80 Q 144 80 144 90 L 144 120.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 144 126.04 L 140.5 119.04 L 144 120.79 L 147.5 119.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="64" y="0" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 84 0 C 72.97 0 64 8.97 64 20 C 64 31.03 72.97 40 84 40 C 95.03 40 104 31.03 104 20 C 104 8.97 95.03 0 84 0 Z M 84 38.18 C 73.97 38.18 65.82 30.03 65.82 20 C 65.82 9.97 73.97 1.82 84 1.82 C 94.03 1.82 102.18 9.97 102.18 20 C 102.18 30.03 94.03 38.18 84 38.18 Z M 95.85 25.45 L 94.45 25.45 L 94.45 22.39 C 94.45 21.88 94.05 21.48 93.55 21.48 L 91.27 21.48 L 91.27 18.41 C 91.27 17.91 90.87 17.5 90.36 17.5 L 84.91 17.5 L 84.91 15.34 L 90.36 15.34 C 90.87 15.34 91.27 14.93 91.27 14.43 L 91.27 7.27 C 91.27 6.77 90.87 6.36 90.36 6.36 L 77.64 6.36 C 77.13 6.36 76.73 6.77 76.73 7.27 L 76.73 14.43 C 76.73 14.93 77.13 15.34 77.64 15.34 L 83.09 15.34 L 83.09 17.5 L 77.64 17.5 C 77.13 17.5 76.73 17.91 76.73 18.41 L 76.73 21.48 L 74.46 21.48 C 73.95 21.48 73.55 21.88 73.55 22.39 L 73.55 25.45 L 72.15 25.45 C 71.65 25.45 71.24 25.86 71.24 26.36 L 71.24 30.34 C 71.24 30.84 71.65 31.25 72.15 31.25 L 76.05 31.25 C 76.55 31.25 76.96 30.84 76.96 30.34 L 76.96 26.36 C 76.96 25.86 76.55 25.45 76.05 25.45 L 75.36 25.45 L 75.36 23.3 L 79.11 23.3 L 79.11 25.45 L 78.43 25.45 C 77.93 25.45 77.52 25.86 77.52 26.36 L 77.52 30.34 C 77.52 30.84 77.93 31.25 78.43 31.25 L 82.41 31.25 C 82.91 31.25 83.32 30.84 83.32 30.34 L 83.32 26.36 C 83.32 25.86 82.91 25.45 82.41 25.45 L 80.93 25.45 L 80.93 22.39 C 80.93 21.88 80.53 21.48 80.02 21.48 L 78.55 21.48 L 78.55 19.32 L 89.45 19.32 L 89.45 21.48 L 87.98 21.48 C 87.47 21.48 87.07 21.88 87.07 22.39 L 87.07 25.45 L 85.59 25.45 C 85.09 25.45 84.68 25.86 84.68 26.36 L 84.68 30.34 C 84.68 30.84 85.09 31.25 85.59 31.25 L 89.57 31.25 C 90.07 31.25 90.48 30.84 90.48 30.34 L 90.48 26.36 C 90.48 25.86 90.07 25.45 89.57 25.45 L 88.89 25.45 L 88.89 23.3 L 92.64 23.3 L 92.64 25.45 L 91.9 25.45 C 91.4 25.45 90.99 25.86 90.99 26.36 L 90.99 30.34 C 90.99 30.84 91.4 31.25 91.9 31.25 L 95.85 31.25 C 96.35 31.25 96.76 30.84 96.76 30.34 L 96.76 26.36 C 96.76 25.86 96.35 25.45 95.85 25.45 Z M 78.55 13.52 L 78.55 8.18 L 89.45 8.18 L 89.45 13.52 Z M 73.06 29.43 L 73.06 27.27 L 75.14 27.27 L 75.14 29.43 Z M 79.34 29.43 L 79.34 27.27 L 81.5 27.27 L 81.5 29.43 Z M 86.5 29.43 L 86.5 27.27 L 88.66 27.27 L 88.66 29.43 Z M 92.81 29.43 L 92.81 27.27 L 94.94 27.27 L 94.94 29.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 47px; margin-left: 84px;">
                        <div data-drawio-colors="color: #232F3E; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="84" y="59" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 364 40 L 364 70 Q 364 80 354 80 L 314 80 Q 304 80 304 90 L 304 120.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 304 126.04 L 300.5 119.04 L 304 120.79 L 307.5 119.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="284" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 323.09 127.16 L 284.91 127.16 C 284.41 127.16 284 127.57 284 128.07 L 284 151.93 C 284 152.43 284.41 152.84 284.91 152.84 L 323.09 152.84 C 323.59 152.84 324 152.43 324 151.93 L 324 128.07 C 324 127.57 323.59 127.16 323.09 127.16 Z M 285.82 151.02 L 285.82 128.98 L 322.18 128.98 L 322.18 151.02 Z M 288.77 148.75 L 290.59 148.75 L 290.59 131.25 L 288.77 131.25 Z M 293.55 148.75 L 295.36 148.75 L 295.36 131.25 L 293.55 131.25 Z M 298.32 148.75 L 300.14 148.75 L 300.14 131.25 L 298.32 131.25 Z M 303.09 148.75 L 304.91 148.75 L 304.91 131.25 L 303.09 131.25 Z M 307.86 148.75 L 309.68 148.75 L 309.68 131.25 L 307.86 131.25 Z M 312.64 148.75 L 314.45 148.75 L 314.45 131.25 L 312.64 131.25 Z M 317.41 148.75 L 319.23 148.75 L 319.23 131.25 L 317.41 131.25 Z" fill="#66ff66" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 304px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                                <br/>
                                (Update)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="304" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <rect x="404" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 443.09 127.16 L 404.91 127.16 C 404.41 127.16 404 127.57 404 128.07 L 404 151.93 C 404 152.43 404.41 152.84 404.91 152.84 L 443.09 152.84 C 443.59 152.84 444 152.43 444 151.93 L 444 128.07 C 444 127.57 443.59 127.16 443.09 127.16 Z M 405.82 151.02 L 405.82 128.98 L 442.18 128.98 L 442.18 151.02 Z M 408.77 148.75 L 410.59 148.75 L 410.59 131.25 L 408.77 131.25 Z M 413.55 148.75 L 415.36 148.75 L 415.36 131.25 L 413.55 131.25 Z M 418.32 148.75 L 420.14 148.75 L 420.14 131.25 L 418.32 131.25 Z M 423.09 148.75 L 424.91 148.75 L 424.91 131.25 L 423.09 131.25 Z M 427.86 148.75 L 429.68 148.75 L 429.68 131.25 L 427.86 131.25 Z M 432.64 148.75 L 434.45 148.75 L 434.45 131.25 L 432.64 131.25 Z M 437.41 148.75 L 439.23 148.75 L 439.23 131.25 L 437.41 131.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 424px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="424" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 364 40 L 364 70 Q 364 80 374 80 L 414 80 Q 424 80 424 90 L 424 120.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 424 126.04 L 420.5 119.04 L 424 120.79 L 427.5 119.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="344" y="0" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 364 0 C 352.97 0 344 8.97 344 20 C 344 31.03 352.97 40 364 40 C 375.03 40 384 31.03 384 20 C 384 8.97 375.03 0 364 0 Z M 364 38.18 C 353.97 38.18 345.82 30.03 345.82 20 C 345.82 9.97 353.97 1.82 364 1.82 C 374.03 1.82 382.18 9.97 382.18 20 C 382.18 30.03 374.03 38.18 364 38.18 Z M 375.85 25.45 L 374.45 25.45 L 374.45 22.39 C 374.45 21.88 374.05 21.48 373.55 21.48 L 371.27 21.48 L 371.27 18.41 C 371.27 17.91 370.87 17.5 370.36 17.5 L 364.91 17.5 L 364.91 15.34 L 370.36 15.34 C 370.87 15.34 371.27 14.93 371.27 14.43 L 371.27 7.27 C 371.27 6.77 370.87 6.36 370.36 6.36 L 357.64 6.36 C 357.13 6.36 356.73 6.77 356.73 7.27 L 356.73 14.43 C 356.73 14.93 357.13 15.34 357.64 15.34 L 363.09 15.34 L 363.09 17.5 L 357.64 17.5 C 357.13 17.5 356.73 17.91 356.73 18.41 L 356.73 21.48 L 354.46 21.48 C 353.95 21.48 353.55 21.88 353.55 22.39 L 353.55 25.45 L 352.15 25.45 C 351.65 25.45 351.24 25.86 351.24 26.36 L 351.24 30.34 C 351.24 30.84 351.65 31.25 352.15 31.25 L 356.05 31.25 C 356.55 31.25 356.96 30.84 356.96 30.34 L 356.96 26.36 C 356.96 25.86 356.55 25.45 356.05 25.45 L 355.36 25.45 L 355.36 23.3 L 359.11 23.3 L 359.11 25.45 L 358.43 25.45 C 357.93 25.45 357.52 25.86 357.52 26.36 L 357.52 30.34 C 357.52 30.84 357.93 31.25 358.43 31.25 L 362.41 31.25 C 362.91 31.25 363.32 30.84 363.32 30.34 L 363.32 26.36 C 363.32 25.86 362.91 25.45 362.41 25.45 L 360.93 25.45 L 360.93 22.39 C 360.93 21.88 360.53 21.48 360.02 21.48 L 358.55 21.48 L 358.55 19.32 L 369.45 19.32 L 369.45 21.48 L 367.98 21.48 C 367.47 21.48 367.07 21.88 367.07 22.39 L 367.07 25.45 L 365.59 25.45 C 365.09 25.45 364.68 25.86 364.68 26.36 L 364.68 30.34 C 364.68 30.84 365.09 31.25 365.59 31.25 L 369.57 31.25 C 370.07 31.25 370.48 30.84 370.48 30.34 L 370.48 26.36 C 370.48 25.86 370.07 25.45 369.57 25.45 L 368.89 25.45 L 368.89 23.3 L 372.64 23.3 L 372.64 25.45 L 371.9 25.45 C 371.4 25.45 370.99 25.86 370.99 26.36 L 370.99 30.34 C 370.99 30.84 371.4 31.25 371.9 31.25 L 375.85 31.25 C 376.35 31.25 376.76 30.84 376.76 30.34 L 376.76 26.36 C 376.76 25.86 376.35 25.45 375.85 25.45 Z M 358.55 13.52 L 358.55 8.18 L 369.45 8.18 L 369.45 13.52 Z M 353.06 29.43 L 353.06 27.27 L 355.14 27.27 L 355.14 29.43 Z M 359.34 29.43 L 359.34 27.27 L 361.5 27.27 L 361.5 29.43 Z M 366.5 29.43 L 366.5 27.27 L 368.66 27.27 L 368.66 29.43 Z M 372.81 29.43 L 372.81 27.27 L 374.94 27.27 L 374.94 29.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 47px; margin-left: 364px;">
                        <div data-drawio-colors="color: #232F3E; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="364" y="59" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 184.5 85 L 184.5 75 L 244.5 75 L 244.5 64.5 L 263.5 80 L 244.5 95.5 L 244.5 85 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 644 40 L 644 70 Q 644 80 634 80 L 594 80 Q 584 80 584 90 L 584 120.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 584 126.04 L 580.5 119.04 L 584 120.79 L 587.5 119.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="564" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 603.09 127.16 L 564.91 127.16 C 564.41 127.16 564 127.57 564 128.07 L 564 151.93 C 564 152.43 564.41 152.84 564.91 152.84 L 603.09 152.84 C 603.59 152.84 604 152.43 604 151.93 L 604 128.07 C 604 127.57 603.59 127.16 603.09 127.16 Z M 565.82 151.02 L 565.82 128.98 L 602.18 128.98 L 602.18 151.02 Z M 568.77 148.75 L 570.59 148.75 L 570.59 131.25 L 568.77 131.25 Z M 573.55 148.75 L 575.36 148.75 L 575.36 131.25 L 573.55 131.25 Z M 578.32 148.75 L 580.14 148.75 L 580.14 131.25 L 578.32 131.25 Z M 583.09 148.75 L 584.91 148.75 L 584.91 131.25 L 583.09 131.25 Z M 587.86 148.75 L 589.68 148.75 L 589.68 131.25 L 587.86 131.25 Z M 592.64 148.75 L 594.45 148.75 L 594.45 131.25 L 592.64 131.25 Z M 597.41 148.75 L 599.23 148.75 L 599.23 131.25 L 597.41 131.25 Z" fill="#66ff66" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 584px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                                <br/>
                                (Update)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="584" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <rect x="684" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 723.09 127.16 L 684.91 127.16 C 684.41 127.16 684 127.57 684 128.07 L 684 151.93 C 684 152.43 684.41 152.84 684.91 152.84 L 723.09 152.84 C 723.59 152.84 724 152.43 724 151.93 L 724 128.07 C 724 127.57 723.59 127.16 723.09 127.16 Z M 685.82 151.02 L 685.82 128.98 L 722.18 128.98 L 722.18 151.02 Z M 688.77 148.75 L 690.59 148.75 L 690.59 131.25 L 688.77 131.25 Z M 693.55 148.75 L 695.36 148.75 L 695.36 131.25 L 693.55 131.25 Z M 698.32 148.75 L 700.14 148.75 L 700.14 131.25 L 698.32 131.25 Z M 703.09 148.75 L 704.91 148.75 L 704.91 131.25 L 703.09 131.25 Z M 707.86 148.75 L 709.68 148.75 L 709.68 131.25 L 707.86 131.25 Z M 712.64 148.75 L 714.45 148.75 L 714.45 131.25 L 712.64 131.25 Z M 717.41 148.75 L 719.23 148.75 L 719.23 131.25 L 717.41 131.25 Z" fill="#66ff66" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 704px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                                <br/>
                                (Update)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="704" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 644 40 L 644 70 Q 644 80 654 80 L 694 80 Q 704 80 704 90 L 704 120.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 704 126.04 L 700.5 119.04 L 704 120.79 L 707.5 119.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="624" y="0" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 644 0 C 632.97 0 624 8.97 624 20 C 624 31.03 632.97 40 644 40 C 655.03 40 664 31.03 664 20 C 664 8.97 655.03 0 644 0 Z M 644 38.18 C 633.97 38.18 625.82 30.03 625.82 20 C 625.82 9.97 633.97 1.82 644 1.82 C 654.03 1.82 662.18 9.97 662.18 20 C 662.18 30.03 654.03 38.18 644 38.18 Z M 655.85 25.45 L 654.45 25.45 L 654.45 22.39 C 654.45 21.88 654.05 21.48 653.55 21.48 L 651.27 21.48 L 651.27 18.41 C 651.27 17.91 650.87 17.5 650.36 17.5 L 644.91 17.5 L 644.91 15.34 L 650.36 15.34 C 650.87 15.34 651.27 14.93 651.27 14.43 L 651.27 7.27 C 651.27 6.77 650.87 6.36 650.36 6.36 L 637.64 6.36 C 637.13 6.36 636.73 6.77 636.73 7.27 L 636.73 14.43 C 636.73 14.93 637.13 15.34 637.64 15.34 L 643.09 15.34 L 643.09 17.5 L 637.64 17.5 C 637.13 17.5 636.73 17.91 636.73 18.41 L 636.73 21.48 L 634.46 21.48 C 633.95 21.48 633.55 21.88 633.55 22.39 L 633.55 25.45 L 632.15 25.45 C 631.65 25.45 631.24 25.86 631.24 26.36 L 631.24 30.34 C 631.24 30.84 631.65 31.25 632.15 31.25 L 636.05 31.25 C 636.55 31.25 636.96 30.84 636.96 30.34 L 636.96 26.36 C 636.96 25.86 636.55 25.45 636.05 25.45 L 635.36 25.45 L 635.36 23.3 L 639.11 23.3 L 639.11 25.45 L 638.43 25.45 C 637.93 25.45 637.52 25.86 637.52 26.36 L 637.52 30.34 C 637.52 30.84 637.93 31.25 638.43 31.25 L 642.41 31.25 C 642.91 31.25 643.32 30.84 643.32 30.34 L 643.32 26.36 C 643.32 25.86 642.91 25.45 642.41 25.45 L 640.93 25.45 L 640.93 22.39 C 640.93 21.88 640.53 21.48 640.02 21.48 L 638.55 21.48 L 638.55 19.32 L 649.45 19.32 L 649.45 21.48 L 647.98 21.48 C 647.47 21.48 647.07 21.88 647.07 22.39 L 647.07 25.45 L 645.59 25.45 C 645.09 25.45 644.68 25.86 644.68 26.36 L 644.68 30.34 C 644.68 30.84 645.09 31.25 645.59 31.25 L 649.57 31.25 C 650.07 31.25 650.48 30.84 650.48 30.34 L 650.48 26.36 C 650.48 25.86 650.07 25.45 649.57 25.45 L 648.89 25.45 L 648.89 23.3 L 652.64 23.3 L 652.64 25.45 L 651.9 25.45 C 651.4 25.45 650.99 25.86 650.99 26.36 L 650.99 30.34 C 650.99 30.84 651.4 31.25 651.9 31.25 L 655.85 31.25 C 656.35 31.25 656.76 30.84 656.76 30.34 L 656.76 26.36 C 656.76 25.86 656.35 25.45 655.85 25.45 Z M 638.55 13.52 L 638.55 8.18 L 649.45 8.18 L 649.45 13.52 Z M 633.06 29.43 L 633.06 27.27 L 635.14 27.27 L 635.14 29.43 Z M 639.34 29.43 L 639.34 27.27 L 641.5 27.27 L 641.5 29.43 Z M 646.5 29.43 L 646.5 27.27 L 648.66 27.27 L 648.66 29.43 Z M 652.81 29.43 L 652.81 27.27 L 654.94 27.27 L 654.94 29.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 47px; margin-left: 644px;">
                        <div data-drawio-colors="color: #232F3E; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="644" y="59" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 464.5 85 L 464.5 75 L 524.5 75 L 524.5 64.5 L 543.5 80 L 524.5 95.5 L 524.5 85 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>