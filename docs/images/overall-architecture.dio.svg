<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1240px" height="917px" viewBox="-0.5 -0.5 1240 917" content="&lt;mxfile host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36&quot; version=&quot;24.8.4&quot; pages=&quot;7&quot;&gt;&#10;  &lt;diagram name=&quot;241113&quot; id=&quot;cnRi_Jx6RtLgTm2ffi-Y&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1539&quot; dy=&quot;1987&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot; value=&quot;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; width=&quot;1160&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-3&quot; value=&quot;VPC&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_vpc;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;strokeWidth=1;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;40&quot; width=&quot;480&quot; height=&quot;600&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-4&quot; value=&quot;Private subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;300&quot; width=&quot;420&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-5&quot; value=&quot;ECS Cluster&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecs;labelPosition=right;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-4&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-6&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-4&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-7&quot; value=&quot;Container&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-4&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-8&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-7&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-9&quot; value=&quot;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.ec2_compute_container;fillColor=#F58534;gradientColor=none;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-7&quot;&gt;&#10;          &lt;mxGeometry y=&quot;8.5&quot; width=&quot;40&quot; height=&quot;23&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-10&quot; value=&quot;Fargate Service&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-4&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-11&quot; value=&quot;Public subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#248814;fillColor=#E9F3E6;verticalAlign=top;align=left;spacingLeft=30;fontColor=#248814;dashed=0;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;80&quot; width=&quot;420&quot; height=&quot;200&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-12&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=#FF6666;dashed=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-11&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;270&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-13&quot; value=&quot;WAF(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-11&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-14&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-11&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;70&quot; width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-15&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;dashed=1;dashPattern=1 4;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-14&quot;&gt;&#10;          &lt;mxGeometry width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-16&quot; value=&quot;ALB&amp;lt;br&amp;gt;(BG)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-14&quot;&gt;&#10;          &lt;mxGeometry x=&quot;10&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-17&quot; value=&quot;Blue&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-14&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-18&quot; value=&quot;Gleen&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-14&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;60&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-19&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelPosition=right;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-20&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sns;strokeWidth=1;labelPosition=right;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-21&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;strokeWidth=1;labelPosition=right;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-22&quot; value=&quot;StepFunctions&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.step_functions;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-23&quot; value=&quot;ECR&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecr;labelBackgroundColor=none;labelPosition=right;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-24&quot; value=&quot;CloudWatch Logs&quot; style=&quot;group;verticalAlign=middle;labelPosition=right;verticalLabelPosition=middle;align=left;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-25&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.logs;shadow=0;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-24&quot;&gt;&#10;          &lt;mxGeometry y=&quot;5.125&quot; width=&quot;40&quot; height=&quot;29.75&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-26&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-24&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-27&quot; value=&quot;CloudWatch&amp;lt;br&amp;gt;Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-28&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-29&quot; value=&quot;&quot; style=&quot;group;aspect=fixed;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;480&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-30&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-29&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-31&quot; value=&quot;Parameter Store&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.parameter_store;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-29&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.7699999999999818&quot; width=&quot;38.459999999999994&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-32&quot; value=&quot;Protected subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;480&quot; width=&quot;420&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-33&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-32&quot;&gt;&#10;          &lt;mxGeometry x=&quot;123&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-34&quot; value=&quot;Aurora&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-32&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-39&quot; value=&quot;OpenSearch&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-32&quot;&gt;&#10;          &lt;mxGeometry x=&quot;227&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-40&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log Bucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=none;labelPosition=right;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-41&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-42&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-43&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-44&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-45&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-46&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-47&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-48&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-49&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-50&quot; value=&quot;&amp;lt;span&amp;gt;&amp;lt;font style=&amp;quot;font-size: 13px;&amp;quot;&amp;gt;CodeDeploy&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codedeploy;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-51&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeColor=default;strokeWidth=1;fontSize=14;fontColor=default;fillColor=none;container=0;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-52&quot; value=&quot;OpenID Connect&quot; style=&quot;group;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;40.00000000000001&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-53&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=none;fillColor=none;strokeColor=default;noLabel=1;aspect=fixed;container=0;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-52&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-54&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;container=0;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-52&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2.613549152305872&quot; y=&quot;0.5069039660541587&quot; width=&quot;34.772901695388256&quot; height=&quot;39.070676062234&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-55&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;83.63636363636364&quot; width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-56&quot; value=&quot;Role&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#BF0816;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.role;labelBackgroundColor=none;labelBorderColor=none;container=0;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-55&quot;&gt;&#10;          &lt;mxGeometry y=&quot;9.512727272727304&quot; width=&quot;40&quot; height=&quot;22.56&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-57&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;container=0;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-55&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-58&quot; value=&quot;InfraResource Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-59&quot; value=&quot;BlueGreen Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-60&quot; value=&quot;Ecspresso Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-61&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;labelPosition=right;labelBackgroundColor=none;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-2&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-62&quot; value=&quot;Disaster Recovery&amp;amp;nbsp;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;-160&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-63&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-62&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-64&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-62&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-65&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-66&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelBackgroundColor=none;container=0;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;-120&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-67&quot; style=&quot;edgeStyle=none;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=default;labelBackgroundColor=none;&quot; edge=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-1&quot; source=&quot;sF_ue8Q8BgG2MTyI-QfG-68&quot; target=&quot;sF_ue8Q8BgG2MTyI-QfG-66&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-68&quot; value=&quot;WAF&amp;lt;br&amp;gt;(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;container=0;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;-120&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-69&quot; value=&quot;AWS Cloud&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_aws_cloud_alt;strokeColor=#232F3E;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#232F3E;dashed=0;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;161&quot; y=&quot;-196&quot; width=&quot;1239&quot; height=&quot;916&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-70&quot; value=&quot;ALB&amp;lt;br&amp;gt;(Rolling)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-69&quot;&gt;&#10;          &lt;mxGeometry x=&quot;169&quot; y=&quot;356&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sF_ue8Q8BgG2MTyI-QfG-71&quot; value=&quot;NAT Gateway&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.nat_gateway;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-69&quot;&gt;&#10;          &lt;mxGeometry x=&quot;429&quot; y=&quot;356&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;0PMTHTs6A5PybTUWxF9K-0&quot; value=&quot;ElastiCache&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticache;&quot; vertex=&quot;1&quot; parent=&quot;sF_ue8Q8BgG2MTyI-QfG-69&quot;&gt;&#10;          &lt;mxGeometry x=&quot;427.5&quot; y=&quot;726&quot; width=&quot;43&quot; height=&quot;43&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram name=&quot;241001&quot; id=&quot;414cglOzIN7KDjMR2cED&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1539&quot; dy=&quot;1987&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-1&quot; parent=&quot;7qt5FFseYsHQUmal--vj-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-2&quot; value=&quot;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; width=&quot;1160&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-3&quot; value=&quot;VPC&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_vpc;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;strokeWidth=1;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;40&quot; width=&quot;480&quot; height=&quot;600&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-4&quot; value=&quot;Private subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;300&quot; width=&quot;420&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-5&quot; value=&quot;ECS Cluster&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecs;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-6&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-7&quot; value=&quot;Container&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-4&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-8&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-7&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-9&quot; value=&quot;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.ec2_compute_container;fillColor=#F58534;gradientColor=none;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;aspect=fixed;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-7&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;8.5&quot; width=&quot;40&quot; height=&quot;23&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-10&quot; value=&quot;Fargate Service&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-11&quot; value=&quot;Public subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#248814;fillColor=#E9F3E6;verticalAlign=top;align=left;spacingLeft=30;fontColor=#248814;dashed=0;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;80&quot; width=&quot;420&quot; height=&quot;200&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-12&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=#FF6666;dashed=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;270&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-13&quot; value=&quot;WAF(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-15&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-11&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;70&quot; width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-16&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;dashed=1;dashPattern=1 4;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-15&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-17&quot; value=&quot;ALB&amp;lt;br&amp;gt;(BG)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-15&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;10&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-18&quot; value=&quot;Blue&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-15&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-19&quot; value=&quot;Gleen&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-15&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;60&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-20&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-21&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sns;strokeWidth=1;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-22&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;strokeWidth=1;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-23&quot; value=&quot;StepFunctions&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.step_functions;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-24&quot; value=&quot;ECR&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecr;labelBackgroundColor=none;labelPosition=right;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-25&quot; value=&quot;CloudWatch Logs&quot; style=&quot;group;verticalAlign=middle;labelPosition=right;verticalLabelPosition=middle;align=left;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-26&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.logs;shadow=0;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-25&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;5.125&quot; width=&quot;40&quot; height=&quot;29.75&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-27&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-25&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-28&quot; value=&quot;CloudWatch&amp;lt;br&amp;gt;Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-29&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-30&quot; value=&quot;&quot; style=&quot;group;aspect=fixed;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;480&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-31&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-30&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-32&quot; value=&quot;Parameter Store&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.parameter_store;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-30&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.7699999999999818&quot; width=&quot;38.459999999999994&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-33&quot; value=&quot;Protected subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;480&quot; width=&quot;420&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-34&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-33&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;123&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-35&quot; value=&quot;Aurora&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-33&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-36&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-33&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;330&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-37&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-36&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-38&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-37&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;2.30499999999995&quot; width=&quot;40&quot; height=&quot;35.39&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-39&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeColor=none;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-36&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-40&quot; value=&quot;OpenSearch&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-33&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;227&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-41&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log Bucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=none;labelPosition=right;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-42&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-43&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-44&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-45&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-46&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-47&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-48&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-49&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-50&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-51&quot; value=&quot;&amp;lt;span&amp;gt;&amp;lt;font style=&amp;quot;font-size: 13px;&amp;quot;&amp;gt;CodeDeploy&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codedeploy;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-52&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeColor=default;strokeWidth=1;fontSize=14;fontColor=default;fillColor=none;container=0;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-53&quot; value=&quot;OpenID Connect&quot; style=&quot;group;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;40.00000000000001&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-54&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=none;fillColor=none;strokeColor=default;noLabel=1;aspect=fixed;container=0;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-53&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-55&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;container=0;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-53&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2.613549152305872&quot; y=&quot;0.5069039660541587&quot; width=&quot;34.772901695388256&quot; height=&quot;39.070676062234&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-56&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;83.63636363636364&quot; width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-57&quot; value=&quot;Role&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#BF0816;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.role;labelBackgroundColor=none;labelBorderColor=none;container=0;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-56&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;9.512727272727304&quot; width=&quot;40&quot; height=&quot;22.56&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-58&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;container=0;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-56&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-59&quot; value=&quot;InfraResource Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-60&quot; value=&quot;BlueGreen Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-61&quot; value=&quot;Ecspresso Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-62&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-63&quot; value=&quot;Disaster Recovery&amp;amp;nbsp;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;-160&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-64&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-63&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-65&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-63&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-66&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-67&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelBackgroundColor=none;container=0;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;-120&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-68&quot; style=&quot;edgeStyle=none;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=default;labelBackgroundColor=none;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-1&quot; source=&quot;7qt5FFseYsHQUmal--vj-69&quot; target=&quot;7qt5FFseYsHQUmal--vj-67&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-69&quot; value=&quot;WAF&amp;lt;br&amp;gt;(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;container=0;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;-120&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-70&quot; value=&quot;AWS Cloud&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_aws_cloud_alt;strokeColor=#232F3E;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#232F3E;dashed=0;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;161&quot; y=&quot;-196&quot; width=&quot;1239&quot; height=&quot;916&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7qt5FFseYsHQUmal--vj-14&quot; value=&quot;ALB&amp;lt;br&amp;gt;(Rolling)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-70&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;169&quot; y=&quot;356&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wf9X4KlT7icRp5tHUdNW-0&quot; value=&quot;NAT Gateway&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.nat_gateway;&quot; parent=&quot;7qt5FFseYsHQUmal--vj-70&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;429&quot; y=&quot;356&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram name=&quot;240819&quot; id=&quot;HsCQJuRp3P_eCWZoXFte&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1365&quot; dy=&quot;1948&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-1&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; value=&quot;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; width=&quot;1160&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-3&quot; value=&quot;VPC&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_vpc;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;strokeWidth=1;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;40&quot; width=&quot;480&quot; height=&quot;600&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-4&quot; value=&quot;Private subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;300&quot; width=&quot;420&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-5&quot; value=&quot;ECS Cluster&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecs;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-6&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-7&quot; value=&quot;Container&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-4&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-8&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-7&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-9&quot; value=&quot;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.ec2_compute_container;fillColor=#F58534;gradientColor=none;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;aspect=fixed;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-7&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;8.5&quot; width=&quot;40&quot; height=&quot;23&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-10&quot; value=&quot;Fargate Service&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-11&quot; value=&quot;Public subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#248814;fillColor=#E9F3E6;verticalAlign=top;align=left;spacingLeft=30;fontColor=#248814;dashed=0;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;80&quot; width=&quot;420&quot; height=&quot;200&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-12&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=#FF6666;dashed=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-13&quot; value=&quot;WAF(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-14&quot; value=&quot;ALB&amp;lt;br&amp;gt;(Rolling)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-15&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-11&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;230&quot; y=&quot;70&quot; width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-16&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;dashed=1;dashPattern=1 4;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-15&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-17&quot; value=&quot;ALB&amp;lt;br&amp;gt;(BG)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-15&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;10&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-18&quot; value=&quot;Blue&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-15&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-19&quot; value=&quot;Gleen&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-15&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;60&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-20&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-21&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sns;strokeWidth=1;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-22&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;strokeWidth=1;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-23&quot; value=&quot;StepFunctions&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.step_functions;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-24&quot; value=&quot;ECR&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecr;labelBackgroundColor=none;labelPosition=right;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-25&quot; value=&quot;CloudWatch Logs&quot; style=&quot;group;verticalAlign=middle;labelPosition=right;verticalLabelPosition=middle;align=left;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-26&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.logs;shadow=0;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-25&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;5.125&quot; width=&quot;40&quot; height=&quot;29.75&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-27&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-25&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-28&quot; value=&quot;CloudWatch&amp;lt;br&amp;gt;Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-29&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-30&quot; value=&quot;&quot; style=&quot;group;aspect=fixed;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;480&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-31&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-30&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-32&quot; value=&quot;Parameter Store&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.parameter_store;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-30&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.7699999999999818&quot; width=&quot;38.459999999999994&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-33&quot; value=&quot;Protected subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;480&quot; width=&quot;420&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-34&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-33&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;123&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-35&quot; value=&quot;Aurora&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-33&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-36&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-33&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;330&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-37&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-36&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-38&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-37&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;2.30499999999995&quot; width=&quot;40&quot; height=&quot;35.39&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-39&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeColor=none;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-36&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-40&quot; value=&quot;OpenSearch&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-33&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;227&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-41&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log Bucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=none;labelPosition=right;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-42&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-43&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-44&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-45&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-46&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-47&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-48&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-49&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-50&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-51&quot; value=&quot;&amp;lt;span&amp;gt;&amp;lt;font style=&amp;quot;font-size: 13px;&amp;quot;&amp;gt;CodeDeploy&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codedeploy;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-52&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeColor=default;strokeWidth=1;fontSize=14;fontColor=default;fillColor=none;container=0;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-53&quot; value=&quot;OpenID Connect&quot; style=&quot;group;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;40.00000000000001&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-54&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=none;fillColor=none;strokeColor=default;noLabel=1;aspect=fixed;container=0;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-53&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-55&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;container=0;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-53&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2.613549152305872&quot; y=&quot;0.5069039660541587&quot; width=&quot;34.772901695388256&quot; height=&quot;39.070676062234&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-56&quot; value=&quot;&quot; style=&quot;group;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;83.63636363636364&quot; width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-57&quot; value=&quot;Role&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#BF0816;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.role;labelBackgroundColor=none;labelBorderColor=none;container=0;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-56&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;9.512727272727304&quot; width=&quot;40&quot; height=&quot;22.56&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-58&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;container=0;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-56&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-59&quot; value=&quot;InfraResource Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-60&quot; value=&quot;BlueGreen Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-61&quot; value=&quot;Ecspresso Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-62&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;labelPosition=right;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-66&quot; value=&quot;Disaster Recovery&amp;amp;nbsp;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;-160&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-67&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-66&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-68&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-66&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-69&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-64&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelBackgroundColor=none;container=0;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;-120&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;m_KGktaS3rLT-IfDSEbm-0&quot; style=&quot;edgeStyle=none;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=default;labelBackgroundColor=none;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-1&quot; source=&quot;3Ni5CcJWneLaYxbdgnx8-65&quot; target=&quot;3Ni5CcJWneLaYxbdgnx8-64&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3Ni5CcJWneLaYxbdgnx8-65&quot; value=&quot;WAF&amp;lt;br&amp;gt;(Web ACL)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;container=0;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;-120&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;1&quot; value=&quot;AWS Cloud&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_aws_cloud_alt;strokeColor=#232F3E;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#232F3E;dashed=0;strokeWidth=1;&quot; parent=&quot;3Ni5CcJWneLaYxbdgnx8-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;161&quot; y=&quot;-196&quot; width=&quot;1239&quot; height=&quot;916&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;XiMurMHVUEbgKz0XYkYH&quot; name=&quot;240816&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;454&quot; dy=&quot;1668&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; value=&quot;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; width=&quot;1160&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-2&quot; value=&quot;VPC&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_vpc;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;40&quot; width=&quot;480&quot; height=&quot;600&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-3&quot; value=&quot;Private subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;300&quot; width=&quot;420&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-4&quot; value=&quot;ECS Cluster&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecs;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-5&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-6&quot; value=&quot;Container&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;labelBackgroundColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-3&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-7&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-6&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-8&quot; value=&quot;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.ec2_compute_container;fillColor=#F58534;gradientColor=none;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;aspect=fixed;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-6&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;8.5&quot; width=&quot;40&quot; height=&quot;23&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-9&quot; value=&quot;Fargate Service&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-10&quot; value=&quot;Public subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#248814;fillColor=#E9F3E6;verticalAlign=top;align=left;spacingLeft=30;fontColor=#248814;dashed=0;shadow=0;labelBackgroundColor=none;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;80&quot; width=&quot;420&quot; height=&quot;200&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-11&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=#FF6666;dashed=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-12&quot; value=&quot;WAF(web acl)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-13&quot; value=&quot;ALB&amp;lt;br&amp;gt;(Rolling)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-14&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-10&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;230&quot; y=&quot;70&quot; width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-15&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;dashed=1;dashPattern=1 4;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-14&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-16&quot; value=&quot;ALB&amp;lt;br&amp;gt;(BG)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-14&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;10&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-17&quot; value=&quot;Blue&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-14&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-18&quot; value=&quot;Gleen&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-14&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;60&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-19&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-20&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sns;strokeWidth=1;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-21&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;strokeWidth=1;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-22&quot; value=&quot;StepFunctions&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.step_functions;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-23&quot; value=&quot;ECR&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecr;labelBackgroundColor=none;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-24&quot; value=&quot;CloudWatch Logs&quot; style=&quot;group;verticalAlign=middle;labelPosition=right;verticalLabelPosition=middle;align=left;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-25&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.logs;shadow=0;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-24&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;5.125&quot; width=&quot;40&quot; height=&quot;29.75&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-26&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-24&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-27&quot; value=&quot;CloudWatch&amp;lt;br&amp;gt;Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-28&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-29&quot; value=&quot;&quot; style=&quot;group;aspect=fixed;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;480&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-30&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-29&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-31&quot; value=&quot;Parameter Store&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.parameter_store;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-29&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.7699999999999818&quot; width=&quot;38.459999999999994&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-32&quot; value=&quot;Protected subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;480&quot; width=&quot;420&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-33&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-32&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;123&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-34&quot; value=&quot;Aurora&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-32&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-35&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-32&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;330&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-36&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-35&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-37&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-36&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;2.30499999999995&quot; width=&quot;40&quot; height=&quot;35.39&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-38&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeColor=none;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-35&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-39&quot; value=&quot;OpenSearch&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-32&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;227&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-40&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log Bucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=default;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-41&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-42&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-43&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-44&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-45&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-46&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-47&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-48&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-49&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-50&quot; value=&quot;&amp;lt;span&amp;gt;&amp;lt;font style=&amp;quot;font-size: 13px;&amp;quot;&amp;gt;CodeDeploy&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codedeploy;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-51&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=default;labelBorderColor=none;strokeColor=default;strokeWidth=1;fontSize=14;fontColor=default;fillColor=none;container=0;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-52&quot; value=&quot;OpenID Connect&quot; style=&quot;group;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;40.00000000000001&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-53&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=none;fillColor=none;strokeColor=default;noLabel=1;aspect=fixed;container=0;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-52&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-54&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;container=0;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-52&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2.613549152305872&quot; y=&quot;0.5069039660541587&quot; width=&quot;34.772901695388256&quot; height=&quot;39.070676062234&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-55&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;83.63636363636364&quot; width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-56&quot; value=&quot;Role&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#BF0816;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.role;labelBackgroundColor=default;labelBorderColor=none;container=0;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-55&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;9.512727272727304&quot; width=&quot;40&quot; height=&quot;22.56&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-57&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;container=0;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-55&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-58&quot; value=&quot;InfraResource Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-59&quot; value=&quot;BlueGreen Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-60&quot; value=&quot;Ecspresso Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-61&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-62&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;-130&quot; width=&quot;50&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-63&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelBackgroundColor=none;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-62&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-64&quot; value=&quot;WAF(web acl)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-62&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;30&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-65&quot; value=&quot;Disaster Recovery&amp;amp;nbsp;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;-160&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-66&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-65&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-67&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;8gYFpUTN49iXKbMzDNmJ-65&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8gYFpUTN49iXKbMzDNmJ-68&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;UI-OKqAq3JC9EcI-4P6T&quot; name=&quot;240815&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1563&quot; dy=&quot;1942&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; value=&quot;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; width=&quot;1160&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-2&quot; value=&quot;VPC&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_vpc;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;40&quot; width=&quot;480&quot; height=&quot;600&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-3&quot; value=&quot;Private subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;300&quot; width=&quot;420&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-4&quot; value=&quot;ECS Cluster&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecs;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-5&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-6&quot; value=&quot;Container&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;labelBackgroundColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-3&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-7&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-6&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-8&quot; value=&quot;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.ec2_compute_container;fillColor=#F58534;gradientColor=none;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;aspect=fixed;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-6&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;8.5&quot; width=&quot;40&quot; height=&quot;23&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-9&quot; value=&quot;Fargate Service&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-10&quot; value=&quot;Public subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#248814;fillColor=#E9F3E6;verticalAlign=top;align=left;spacingLeft=30;fontColor=#248814;dashed=0;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;80&quot; width=&quot;420&quot; height=&quot;200&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-11&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=#FF6666;dashed=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-12&quot; value=&quot;WAF(web acl)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-13&quot; value=&quot;ALB&amp;lt;br&amp;gt;(Rolling)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-14&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-10&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;230&quot; y=&quot;70&quot; width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-15&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;dashed=1;dashPattern=1 4;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-14&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-16&quot; value=&quot;ALB&amp;lt;br&amp;gt;(BG)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-14&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;10&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-17&quot; value=&quot;Blue&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-14&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-18&quot; value=&quot;Gleen&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-14&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;60&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-19&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-20&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sns;strokeWidth=1;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-21&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;strokeWidth=1;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-22&quot; value=&quot;StepFunctions&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.step_functions;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-23&quot; value=&quot;ECR&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecr;labelBackgroundColor=none;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-24&quot; value=&quot;CloudWatch Logs&quot; style=&quot;group;verticalAlign=middle;labelPosition=right;verticalLabelPosition=middle;align=left;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-25&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.logs;shadow=0;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-24&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;5.125&quot; width=&quot;40&quot; height=&quot;29.75&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-26&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-24&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-27&quot; value=&quot;CloudWatch&amp;lt;br&amp;gt;Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-28&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-29&quot; value=&quot;&quot; style=&quot;group;aspect=fixed;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;480&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-30&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-29&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-31&quot; value=&quot;Parameter Store&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.parameter_store;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-29&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.7699999999999818&quot; width=&quot;38.459999999999994&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-32&quot; value=&quot;Protected subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;480&quot; width=&quot;420&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-33&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-32&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;123&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-34&quot; value=&quot;Aurora&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;shadow=0;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-32&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-35&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-32&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;330&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-36&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-35&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-37&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-36&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;2.30499999999995&quot; width=&quot;40&quot; height=&quot;35.39&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-38&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeColor=none;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-35&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-39&quot; value=&quot;OpenSearch&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticsearch_service;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-32&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;227&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-40&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log Bucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=default;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-41&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-42&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-43&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-44&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-45&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-46&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-47&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-48&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-49&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-50&quot; value=&quot;&amp;lt;span&amp;gt;&amp;lt;font style=&amp;quot;font-size: 13px;&amp;quot;&amp;gt;CodeDeploy&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codedeploy;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-51&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=default;labelBorderColor=none;strokeColor=default;strokeWidth=1;fontSize=14;fontColor=default;fillColor=none;container=0;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-52&quot; value=&quot;OpenID Connect&quot; style=&quot;group;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;40.00000000000001&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-53&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=none;fillColor=none;strokeColor=default;noLabel=1;aspect=fixed;container=0;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-52&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-54&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;container=0;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-52&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2.613549152305872&quot; y=&quot;0.5069039660541587&quot; width=&quot;34.772901695388256&quot; height=&quot;39.070676062234&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-55&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;83.63636363636364&quot; width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-56&quot; value=&quot;Role&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#BF0816;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.role;labelBackgroundColor=default;labelBorderColor=none;container=0;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-55&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;9.512727272727304&quot; width=&quot;40&quot; height=&quot;22.56&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-57&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;container=0;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-55&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-58&quot; value=&quot;InfraResource Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-59&quot; value=&quot;BlueGreen Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-60&quot; value=&quot;Ecspresso Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-61&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-62&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;-130&quot; width=&quot;50&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-63&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelBackgroundColor=none;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-62&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-64&quot; value=&quot;WAF(web acl)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-62&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;30&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-65&quot; value=&quot;Disaster Recovery&amp;amp;nbsp;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;-160&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-66&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-65&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;160&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-67&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;wglBqBjyeTBNOeLxhV0Y-65&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;wglBqBjyeTBNOeLxhV0Y-68&quot; value=&quot;VPC Endpoint&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC;imageBackground=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;Xk24UP-ere5XCFbMja_5&quot; name=&quot;240809&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1658&quot; dy=&quot;2278&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; value=&quot;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; width=&quot;1160&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uHXZNJqdo_x7_QTsu0V_-2&quot; value=&quot;VPC&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_vpc;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;40&quot; width=&quot;480&quot; height=&quot;600&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;2&quot; value=&quot;Private subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;300&quot; width=&quot;420&quot; height=&quot;160&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6LeshNHcoD9VhfRIqCkk-1&quot; value=&quot;ECS Cluster&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecs;labelPosition=right;&quot; parent=&quot;2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-83&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-74&quot; value=&quot;Container&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;labelBackgroundColor=default;&quot; parent=&quot;2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;270&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-73&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-74&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-72&quot; value=&quot;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.ec2_compute_container;fillColor=#F58534;gradientColor=none;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;aspect=fixed;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-74&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;8.5&quot; width=&quot;40&quot; height=&quot;23&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-45&quot; value=&quot;Fargate Service&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;70&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-118&quot; value=&quot;Public subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#248814;fillColor=#E9F3E6;verticalAlign=top;align=left;spacingLeft=30;fontColor=#248814;dashed=0;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;80&quot; width=&quot;420&quot; height=&quot;200&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-90&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=#FF6666;dashed=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-118&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;380&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-89&quot; value=&quot;WAF(web acl)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-118&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;40&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-41&quot; value=&quot;ALB&amp;lt;br&amp;gt;(Rolling)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-118&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;110&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-116&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-118&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;230&quot; y=&quot;70&quot; width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-81&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;dashed=1;dashPattern=1 4;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-116&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;110&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-51&quot; value=&quot;ALB&amp;lt;br&amp;gt;(BG)&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.application_load_balancer;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-116&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;10&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-78&quot; value=&quot;Blue&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-116&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;10&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-79&quot; value=&quot;Gleen&amp;lt;br&amp;gt;Listener&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-116&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;60&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-10&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;500&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-8&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=default;gradientColor=none;strokeColor=none;aspect=fixed;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-7&quot; value=&quot;VPC Endpoint&quot; style=&quot;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#4D27AA;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.endpoint;labelBorderColor=none;strokeWidth=1;labelBackgroundColor=default;shadow=0;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-k2V8pwDi5HVyl-puyQm-10&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-k2V8pwDi5HVyl-puyQm-17&quot; value=&quot;SNS Topic&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sns;strokeWidth=1;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uHXZNJqdo_x7_QTsu0V_-3&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;strokeWidth=1;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-108&quot; value=&quot;StepFunctions&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.step_functions;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6LeshNHcoD9VhfRIqCkk-3&quot; value=&quot;ECR&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecr;labelBackgroundColor=none;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-87&quot; value=&quot;CloudWatch Logs&quot; style=&quot;group;verticalAlign=middle;labelPosition=right;verticalLabelPosition=middle;align=left;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;80&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-85&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.logs;shadow=0;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-87&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;5.125&quot; width=&quot;40&quot; height=&quot;29.75&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-86&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;dashPattern=1 4;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-87&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-95&quot; value=&quot;CloudWatch&amp;lt;br&amp;gt;Dashboard&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-103&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-27&quot; value=&quot;&quot; style=&quot;group;aspect=fixed;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;480&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-26&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-27&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-25&quot; value=&quot;Parameter Store&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.parameter_store;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-27&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.7699999999999818&quot; width=&quot;38.459999999999994&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3&quot; value=&quot;Protected subnet&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_security_group;grStroke=0;strokeColor=#147EBA;fillColor=#E6F2F8;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=0;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;480&quot; width=&quot;420&quot; height=&quot;140&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-102&quot; value=&quot;EFS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elastic_file_system;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=center;&quot; parent=&quot;3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;123&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-96&quot; value=&quot;OpenSearch&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;labelPosition=center;align=center;&quot; parent=&quot;3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;227&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-92&quot; value=&quot;Aurora&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8rCQhef2LBLKglBOi00p-12&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;3&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;330&quot; y=&quot;50&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-99&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;group;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;&quot; parent=&quot;8rCQhef2LBLKglBOi00p-12&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-97&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-99&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;2.30499999999995&quot; width=&quot;40&quot; height=&quot;35.39&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-98&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;dashed=1;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeColor=none;strokeWidth=1;fontSize=10;fillColor=none;gradientColor=none;&quot; parent=&quot;8rCQhef2LBLKglBOi00p-12&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;12&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(Log Bucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBackgroundColor=default;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;320&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;10&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;9&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-18&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;labelPosition=center;verticalLabelPosition=middle;align=left;verticalAlign=top;spacing=2;spacingLeft=5;dashed=1;strokeColor=#0000FF;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-15&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-20&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;600&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-21&quot; value=&quot;ChatBot&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F34482;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.chatbot;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;240&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-42&quot; value=&quot;CodeBuild&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codebuild;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-43&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;400&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-54&quot; value=&quot;S3 Bucket&amp;lt;br&amp;gt;(SourceBucket)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#60A337;gradientDirection=north;fillColor=#277116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;730&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-55&quot; value=&quot;&amp;lt;span&amp;gt;&amp;lt;font style=&amp;quot;font-size: 13px;&amp;quot;&amp;gt;CodeDeploy&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codedeploy;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;626.5&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-k2V8pwDi5HVyl-puyQm-9&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=default;labelBorderColor=none;strokeColor=default;strokeWidth=1;fontSize=14;fontColor=default;fillColor=none;container=0;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;240&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-60&quot; value=&quot;OpenID Connect&quot; style=&quot;group;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;40&quot; width=&quot;40.00000000000001&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-k2V8pwDi5HVyl-puyQm-7&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=none;fillColor=none;strokeColor=default;noLabel=1;aspect=fixed;container=0;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-k2V8pwDi5HVyl-puyQm-8&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;container=0;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-60&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;2.613549152305872&quot; y=&quot;0.5069039660541587&quot; width=&quot;34.772901695388256&quot; height=&quot;39.070676062234&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-61&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;83.63636363636364&quot; width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-k2V8pwDi5HVyl-puyQm-4&quot; value=&quot;Role&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#BF0816;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.role;labelBackgroundColor=default;labelBorderColor=none;container=0;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-61&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;9.512727272727304&quot; width=&quot;40&quot; height=&quot;22.56&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-30&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;fillColor=none;gradientColor=none;strokeColor=none;container=0;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-61&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;40&quot; height=&quot;43.63636363636364&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6&quot; value=&quot;InfraResource Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;200&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8&quot; value=&quot;BlueGreen Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;360&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;11&quot; value=&quot;Ecspresso Pipeline&quot; style=&quot;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;520&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-k2V8pwDi5HVyl-puyQm-11&quot; value=&quot;Cognito&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cognito;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-104&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;560&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-91&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;360&quot; y=&quot;-130&quot; width=&quot;50&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6LeshNHcoD9VhfRIqCkk-2&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelBackgroundColor=none;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-91&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;10&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-23&quot; value=&quot;WAF(web acl)&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=middle;verticalAlign=middle;align=left;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.waf;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;labelPosition=right;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-91&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;30&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-105&quot; value=&quot;Disaster Recovery&amp;amp;nbsp;Region&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_region;strokeColor=#147EBA;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#147EBA;dashed=1;shadow=0;labelBackgroundColor=default;labelBorderColor=none;sketch=0;strokeWidth=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;560&quot; y=&quot;-160&quot; width=&quot;200&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-107&quot; value=&quot;Backup Vault&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#3F8624;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.backup_vault;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-105&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;120&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;fdRIWoBgwsfDSBM6xxnp-106&quot; value=&quot;KMS Key&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.key_management_service;shadow=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;&quot; parent=&quot;fdRIWoBgwsfDSBM6xxnp-105&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;40&quot; y=&quot;40&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;MXdhIz_6k3ohLE5k0DK-&quot; name=&quot;240808&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1334&quot; dy=&quot;541&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;P3yxqF-Yb3xbSQQgcTyT-8&quot; value=&quot;&quot; style=&quot;group;flipV=0;flipH=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;170&quot; y=&quot;320&quot; width=&quot;410&quot; height=&quot;130&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;P3yxqF-Yb3xbSQQgcTyT-9&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=default;fillColor=none;&quot; parent=&quot;P3yxqF-Yb3xbSQQgcTyT-8&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;10&quot; width=&quot;410&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;P3yxqF-Yb3xbSQQgcTyT-10&quot; value=&quot;ECS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecs;&quot; parent=&quot;P3yxqF-Yb3xbSQQgcTyT-8&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;30&quot; width=&quot;60&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;P3yxqF-Yb3xbSQQgcTyT-11&quot; value=&quot;ecs-stack&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=default;fontSize=14;&quot; parent=&quot;P3yxqF-Yb3xbSQQgcTyT-8&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;10&quot; width=&quot;70&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;P3yxqF-Yb3xbSQQgcTyT-12&quot; value=&quot;CloudFront&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#945DF2;gradientDirection=north;fillColor=#5A30B5;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;labelBackgroundColor=none;&quot; parent=&quot;P3yxqF-Yb3xbSQQgcTyT-8&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;30&quot; width=&quot;60&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;P3yxqF-Yb3xbSQQgcTyT-13&quot; value=&quot;ECR&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F78E04;gradientDirection=north;fillColor=#D05C17;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecr;labelBackgroundColor=none;&quot; parent=&quot;P3yxqF-Yb3xbSQQgcTyT-8&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;120&quot; y=&quot;30&quot; width=&quot;60&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;P3yxqF-Yb3xbSQQgcTyT-14&quot; value=&quot;&amp;lt;span&amp;gt;&amp;lt;font style=&amp;quot;font-size: 13px;&amp;quot;&amp;gt;CodePipeline&amp;lt;/font&amp;gt;&amp;lt;/span&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#4D72F3;gradientDirection=north;fillColor=#3334B9;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.codepipeline;&quot; parent=&quot;P3yxqF-Yb3xbSQQgcTyT-8&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;220&quot; y=&quot;30&quot; width=&quot;60&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-1&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;260&quot; y=&quot;490&quot; width=&quot;210&quot; height=&quot;180&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-2&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;20&quot; y=&quot;30&quot; width=&quot;170&quot; height=&quot;130&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-3&quot; value=&quot;&quot; style=&quot;group;fontColor=default;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;55&quot; y=&quot;35&quot; width=&quot;60&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-4&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=default;labelBorderColor=none;fontSize=14;fillColor=none;strokeColor=none;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;60&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-5&quot; value=&quot;Role&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#BF0816;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.role;labelBackgroundColor=default;labelBorderColor=none;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;13.080000000000013&quot; width=&quot;60&quot; height=&quot;33.84&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-6&quot; value=&quot;OpenID Connect&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=default;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;33.329999999999984&quot; y=&quot;1.6700000000000017&quot; width=&quot;110&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-7&quot; value=&quot;&quot; style=&quot;group;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-2&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry width=&quot;33.33333333333334&quot; height=&quot;33.33333333333334&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-8&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=none;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=none;fillColor=none;strokeColor=default;noLabel=1;aspect=fixed;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-7&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;33.333333333333336&quot; height=&quot;33.333333333333336&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-9&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;connectable=1;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-7&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1.8333333333333428&quot; width=&quot;29.666666666666664&quot; height=&quot;33.333333333333336&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-10&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=default;labelBorderColor=none;strokeColor=default;strokeWidth=1;fontSize=14;fontColor=default;fillColor=none;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;170&quot; height=&quot;130&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-11&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;labelBackgroundColor=default;labelBorderColor=none;strokeColor=default;strokeWidth=1;fontSize=14;fontColor=default;fillColor=none;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;10&quot; width=&quot;210&quot; height=&quot;170&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;97QkYCJR4VjDYnkwOJD8-12&quot; value=&quot;oidc-stack&quot; style=&quot;text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=default;labelBorderColor=none;strokeWidth=1;fontSize=14;fontColor=default;&quot; parent=&quot;97QkYCJR4VjDYnkwOJD8-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;70&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0"><stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0"><stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0"><stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-ff4f8b-1-bc1356-1-s-0"><stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(255, 79, 139); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0"><stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0"><stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0"><stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/></linearGradient></defs><g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-0"><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-1"><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-2"><g><path d="M 39 196 L 1199 196 L 1199 876 L 39 876 Z" fill="none" stroke="#147eba" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 47.06 199.47 C 46.47 199.47 46 199.95 46 200.53 C 46 200.99 46.3 201.39 46.71 201.53 L 46.71 216.82 L 44.95 216.82 L 44.95 217.57 L 49.16 217.57 L 49.16 216.82 L 47.46 216.82 L 47.46 208.75 L 58.84 208.75 L 56.19 205.59 L 58.83 202.36 L 47.46 202.36 L 47.46 201.51 C 47.85 201.35 48.12 200.97 48.12 200.53 C 48.12 199.95 47.64 199.47 47.06 199.47 Z M 47.06 200.22 C 47.23 200.22 47.37 200.35 47.37 200.53 C 47.37 200.71 47.23 200.84 47.06 200.84 C 46.88 200.84 46.75 200.71 46.75 200.53 C 46.75 200.35 46.88 200.22 47.06 200.22 Z M 47.46 203.11 L 57.25 203.11 L 55.22 205.6 L 57.23 208 L 47.46 208 Z M 39 221 L 39 196 L 64 196 L 64 221 Z" fill="#147eba" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1128px; height: 1px; padding-top: 203px; margin-left: 71px;"><div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Region</div></div></div></foreignObject><text x="71" y="215" fill="#147EBA" font-family="&quot;Helvetica&quot;" font-size="12px">Region</text></switch></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-3"><g><path d="M 79 236 L 559 236 L 559 836 L 79 836 Z" fill="none" stroke="#879196" stroke-miterlimit="10" pointer-events="all"/><path d="M 89.59 242.65 C 89.53 242.65 89.48 242.65 89.42 242.65 L 89.42 242.65 C 88.11 242.68 87.03 243.24 86.14 244.25 C 86.13 244.25 86.13 244.25 86.13 244.25 C 85.2 245.36 84.87 246.52 84.96 247.73 C 83.81 248.06 83.12 248.92 82.76 249.74 C 82.75 249.75 82.75 249.76 82.74 249.78 C 82.33 251.05 82.68 252.36 83.24 253.16 C 83.25 253.17 83.25 253.17 83.26 253.18 C 83.94 254.05 84.97 254.53 86.02 254.53 L 97.17 254.53 C 98.19 254.53 99.07 254.16 99.8 253.37 C 100.25 252.94 100.49 252.29 100.58 251.59 C 100.67 250.9 100.61 250.16 100.32 249.55 C 100.31 249.54 100.31 249.53 100.31 249.52 C 99.8 248.62 98.95 247.81 97.76 247.64 C 97.74 246.79 97.28 245.99 96.68 245.56 C 96.67 245.55 96.66 245.55 96.65 245.54 C 96.01 245.18 95.4 245.14 94.91 245.3 C 94.6 245.4 94.36 245.56 94.14 245.74 C 93.51 244.36 92.43 243.18 90.81 242.79 C 90.81 242.79 90.81 242.79 90.81 242.79 C 90.38 242.7 89.97 242.65 89.59 242.65 Z M 89.43 243.38 C 89.8 243.38 90.2 243.43 90.64 243.53 C 92.16 243.89 93.15 245.07 93.66 246.48 C 93.71 246.6 93.81 246.69 93.94 246.72 C 94.07 246.74 94.2 246.7 94.29 246.61 C 94.54 246.34 94.83 246.11 95.14 246.01 C 95.44 245.91 95.78 245.92 96.26 246.18 C 96.67 246.49 97.11 247.31 97.03 247.9 C 97.01 248.01 97.05 248.12 97.12 248.2 C 97.19 248.28 97.29 248.33 97.39 248.33 C 98.46 248.34 99.16 249.02 99.64 249.88 C 99.85 250.3 99.91 250.92 99.84 251.5 C 99.76 252.07 99.53 252.59 99.28 252.83 C 99.27 252.84 99.27 252.85 99.26 252.85 C 98.65 253.53 98.03 253.78 97.17 253.78 L 86.02 253.78 C 85.2 253.78 84.39 253.41 83.85 252.73 C 83.44 252.13 83.14 251.02 83.46 250.02 C 83.79 249.27 84.36 248.55 85.41 248.36 C 85.6 248.32 85.74 248.14 85.71 247.94 C 85.56 246.79 85.8 245.81 86.7 244.74 C 87.49 243.85 88.33 243.39 89.43 243.38 Z M 91.2 246.7 C 90.77 246.7 90.4 246.93 90.13 247.21 C 89.85 247.5 89.64 247.85 89.64 248.25 L 89.64 248.71 L 89.14 248.71 C 89.04 248.71 88.94 248.75 88.87 248.82 C 88.8 248.89 88.76 248.98 88.76 249.08 L 88.76 251.7 C 88.76 251.8 88.8 251.89 88.87 251.96 C 88.94 252.03 89.04 252.07 89.14 252.07 L 93.16 252.07 C 93.26 252.07 93.35 252.03 93.42 251.96 C 93.49 251.89 93.53 251.8 93.53 251.7 L 93.53 249.08 C 93.53 248.98 93.49 248.89 93.42 248.82 C 93.35 248.75 93.26 248.71 93.16 248.71 L 92.68 248.71 L 92.68 248.25 C 92.68 247.84 92.47 247.47 92.21 247.2 C 91.94 246.92 91.61 246.7 91.2 246.7 Z M 91.2 247.45 C 91.29 247.45 91.5 247.54 91.67 247.72 C 91.83 247.89 91.93 248.11 91.93 248.25 L 91.93 248.71 L 90.39 248.71 L 90.39 248.25 C 90.39 248.15 90.49 247.91 90.66 247.74 C 90.83 247.56 91.06 247.45 91.2 247.45 Z M 89.51 249.46 L 92.78 249.46 L 92.78 251.32 L 89.51 251.32 Z M 79 261 L 79 236 L 104 236 L 104 261 Z" fill="#879196" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 448px; height: 1px; padding-top: 243px; margin-left: 111px;"><div data-drawio-colors="color: #879196; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(135, 145, 150); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">VPC</div></div></div></foreignObject><text x="111" y="255" fill="#879196" font-family="&quot;Helvetica&quot;" font-size="12px">VPC</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-4"><g><path d="M 99 496 L 519 496 L 519 656 L 99 656 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/><path d="M 99 496 L 124 496 L 124 521 L 99 521 Z M 111.52 499.21 C 110.4 499.21 109.31 499.63 108.48 500.39 C 107.67 501.11 107.2 502.15 107.2 503.24 L 107.2 505.78 L 104.89 505.78 C 104.8 505.78 104.7 505.82 104.64 505.89 C 104.57 505.95 104.54 506.04 104.54 506.13 L 104.54 517.43 C 104.54 517.63 104.7 517.79 104.89 517.79 L 118.11 517.79 C 118.3 517.79 118.46 517.63 118.46 517.43 L 118.46 506.15 C 118.47 506.06 118.43 505.97 118.36 505.9 C 118.3 505.83 118.21 505.79 118.11 505.79 L 115.81 505.79 L 115.81 503.29 C 115.8 502.21 115.35 501.18 114.56 500.44 C 113.74 499.65 112.65 499.22 111.52 499.21 Z M 111.51 499.93 C 112.46 499.92 113.37 500.28 114.06 500.93 C 114.72 501.54 115.1 502.4 115.1 503.29 L 115.1 505.79 L 107.88 505.79 L 107.89 503.26 C 107.9 502.36 108.28 501.51 108.95 500.91 C 109.65 500.27 110.57 499.92 111.51 499.93 Z M 105.24 506.5 L 117.76 506.5 L 117.75 517.07 L 105.24 517.07 Z M 111.51 508.74 C 110.48 508.73 109.61 509.51 109.51 510.53 C 109.42 511.56 110.13 512.48 111.14 512.66 L 111.14 515.44 L 111.86 515.44 L 111.86 512.66 C 112.79 512.49 113.47 511.67 113.48 510.72 C 113.48 509.63 112.6 508.75 111.51 508.74 Z M 111.39 509.45 C 111.43 509.45 111.47 509.45 111.51 509.46 C 111.84 509.46 112.16 509.59 112.4 509.83 C 112.64 510.07 112.77 510.39 112.76 510.72 C 112.77 511.06 112.64 511.38 112.4 511.61 C 112.16 511.85 111.84 511.98 111.51 511.98 C 111.04 512.02 110.6 511.8 110.34 511.42 C 110.08 511.03 110.06 510.53 110.28 510.12 C 110.5 509.71 110.93 509.46 111.39 509.45 Z" fill="#147eba" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 388px; height: 1px; padding-top: 503px; margin-left: 131px;"><div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Private subnet</div></div></div></foreignObject><text x="131" y="515" fill="#147EBA" font-family="&quot;Helvetica&quot;" font-size="12px">Private subnet</text></switch></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-5"><g><path d="M 119 536 L 139 536 L 139 556 L 119 556 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/><path d="M 135.59 548.18 L 133.35 546.83 L 133.35 543.62 C 133.35 543.52 133.29 543.43 133.21 543.38 L 129.98 541.5 L 129.98 538.78 L 135.59 542.1 Z M 136.01 541.7 L 129.84 538.05 C 129.76 538 129.65 538 129.56 538.05 C 129.47 538.1 129.42 538.19 129.42 538.29 L 129.42 541.66 C 129.42 541.76 129.47 541.85 129.56 541.9 L 132.79 543.78 L 132.79 546.99 C 132.79 547.09 132.84 547.18 132.92 547.23 L 135.73 548.91 C 135.77 548.94 135.82 548.95 135.87 548.95 C 135.92 548.95 135.97 548.94 136.01 548.91 C 136.1 548.87 136.15 548.77 136.15 548.67 L 136.15 541.94 C 136.15 541.84 136.1 541.75 136.01 541.7 Z M 128.98 553.4 L 122.41 549.9 L 122.41 542.1 L 128.02 538.78 L 128.02 541.51 L 125.06 543.39 C 124.98 543.44 124.93 543.53 124.93 543.62 L 124.93 548.39 C 124.93 548.5 124.99 548.59 125.08 548.64 L 128.86 550.6 C 128.94 550.64 129.03 550.64 129.12 550.6 L 132.78 548.71 L 135.03 550.06 Z M 135.74 549.83 L 132.93 548.15 C 132.85 548.1 132.74 548.1 132.66 548.14 L 128.99 550.04 L 125.49 548.22 L 125.49 543.78 L 128.45 541.9 C 128.53 541.84 128.58 541.76 128.58 541.66 L 128.58 538.29 C 128.58 538.19 128.53 538.1 128.44 538.05 C 128.35 538 128.24 538 128.16 538.05 L 121.99 541.7 C 121.9 541.75 121.85 541.84 121.85 541.94 L 121.85 550.07 C 121.85 550.18 121.91 550.27 122 550.32 L 128.86 553.97 C 128.9 553.99 128.94 554 128.99 554 C 129.03 554 129.08 553.99 129.12 553.96 L 135.73 550.32 C 135.81 550.27 135.87 550.18 135.87 550.08 C 135.87 549.98 135.82 549.88 135.74 549.83 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 546px; margin-left: 141px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ECS Cluster</div></div></div></foreignObject><text x="141" y="550" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">ECS...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-6"><g><rect x="119" y="536" width="380" height="100" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="1 4" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-7"><g/><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px"><text x="388.5" y="623.5">Container</text></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-8"><g><rect x="369" y="566" width="40" height="40" fill="none" stroke="none" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-9"><g><path d="M 369 597.5 L 369 574.5 L 409 574.52 L 408.92 597.5 Z" fill="#f58534" stroke="none" pointer-events="all"/><path d="M 369 597.5 L 369 595.57 L 408.92 595.57 L 408.92 597.5 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all"/><path d="M 405.34 594.32 L 405.34 575.76 L 407.23 575.76 L 407.23 594.32 Z M 401.88 594.32 L 401.88 575.76 L 403.77 575.76 L 403.77 594.32 Z M 398.42 594.32 L 398.42 575.76 L 400.3 575.76 L 400.3 594.32 Z M 394.95 594.32 L 394.95 575.76 L 396.84 575.76 L 396.84 594.32 Z M 391.49 594.32 L 391.49 575.76 L 393.37 575.76 L 393.37 594.32 Z M 388.02 594.32 L 388.02 575.76 L 389.91 575.76 L 389.91 594.32 Z M 384.56 594.32 L 384.56 575.76 L 386.44 575.76 L 386.44 594.32 Z M 381.09 594.32 L 381.09 575.76 L 382.98 575.76 L 382.98 594.32 Z M 377.63 594.32 L 377.63 575.76 L 379.5 575.76 L 379.5 594.32 Z M 374.16 594.32 L 374.16 575.76 L 376.04 575.76 L 376.04 594.32 Z M 370.7 594.32 L 370.7 575.76 L 372.58 575.76 L 372.58 594.32 Z" fill-opacity="0.3" fill="#ffffff" stroke="none" pointer-events="all"/><rect x="369" y="574.5" width="0" height="0" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/><path d="M 407.23 594.32 L 405.84 594.32 L 405.84 576.26 L 407.23 576.26 Z M 403.77 594.32 L 402.37 594.32 L 402.37 576.26 L 403.77 576.26 Z M 400.3 594.32 L 398.91 594.32 L 398.91 576.26 L 400.3 576.26 Z M 396.84 594.32 L 395.44 594.32 L 395.44 576.26 L 396.84 576.26 Z M 393.37 594.32 L 391.98 594.32 L 391.98 576.26 L 393.37 576.26 Z M 389.91 594.32 L 388.51 594.32 L 388.51 576.26 L 389.91 576.26 Z M 386.44 594.32 L 385.05 594.32 L 385.05 576.26 L 386.44 576.26 Z M 382.98 594.32 L 381.58 594.32 L 381.58 576.26 L 382.98 576.26 Z M 379.5 594.32 L 378.12 594.32 L 378.12 576.26 L 379.5 576.26 Z M 376.04 594.32 L 374.65 594.32 L 374.65 576.26 L 376.04 576.26 Z M 372.58 594.32 L 371.19 594.32 L 371.19 576.26 L 372.58 576.26 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-10"><g><path d="M 209 566 L 249 566 L 249 606 L 209 606 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/><path d="M 236.53 596.06 L 236.53 593.02 L 238.41 592.26 L 238.41 595.3 Z M 233.39 592.26 L 235.27 593.02 L 235.27 596.06 L 233.39 595.3 Z M 229.63 600.45 L 229.63 597.41 L 231.51 596.65 L 231.51 599.69 Z M 226.49 596.65 L 228.37 597.41 L 228.37 600.45 L 226.49 599.69 Z M 222.73 596.06 L 222.73 593.02 L 224.61 592.26 L 224.61 595.3 Z M 219.59 592.26 L 221.47 593.02 L 221.47 596.06 L 219.59 595.3 Z M 222.1 590.76 L 223.55 591.34 L 222.1 591.92 L 220.65 591.34 Z M 229 595.15 L 230.45 595.73 L 229 596.31 L 227.55 595.73 Z M 235.9 590.76 L 237.35 591.34 L 235.9 591.92 L 234.45 591.34 Z M 239.27 590.76 L 236.13 589.5 C 235.98 589.44 235.82 589.44 235.67 589.5 L 232.53 590.76 C 232.29 590.85 232.14 591.08 232.14 591.34 L 232.14 595.05 L 229.23 593.89 C 229.08 593.83 228.92 593.83 228.77 593.89 L 225.86 595.05 L 225.86 591.34 C 225.86 591.08 225.71 590.85 225.47 590.76 L 222.33 589.5 C 222.18 589.44 222.02 589.44 221.87 589.5 L 218.73 590.76 C 218.49 590.85 218.34 591.08 218.34 591.34 L 218.34 595.73 C 218.34 595.98 218.49 596.21 218.73 596.31 L 221.87 597.57 C 221.94 597.59 222.02 597.61 222.1 597.61 C 222.18 597.61 222.26 597.59 222.33 597.57 L 225.24 596.4 L 225.24 600.12 C 225.24 600.37 225.39 600.61 225.63 600.7 L 228.77 601.96 C 228.84 601.98 228.92 602 229 602 C 229.08 602 229.16 601.98 229.23 601.96 L 232.37 600.7 C 232.61 600.61 232.76 600.37 232.76 600.12 L 232.76 596.4 L 235.67 597.57 C 235.74 597.59 235.82 597.61 235.9 597.61 C 235.98 597.61 236.06 597.59 236.13 597.57 L 239.27 596.31 C 239.51 596.21 239.66 595.98 239.66 595.73 L 239.66 591.34 C 239.66 591.08 239.51 590.85 239.27 590.76 Z M 244.68 583.15 C 244.68 586.81 236.6 588.79 229 588.79 C 221.4 588.79 213.32 586.81 213.32 583.15 C 213.32 581.4 215.26 579.85 218.79 578.81 L 219.14 580.01 C 216.33 580.85 214.57 582.05 214.57 583.15 C 214.57 585.22 220.5 587.54 229 587.54 C 237.5 587.54 243.43 585.22 243.43 583.15 C 243.43 582.05 241.67 580.85 238.86 580.01 L 239.21 578.81 C 242.74 579.85 244.68 581.4 244.68 583.15 Z M 229 571.31 L 235.41 573.78 L 229 576.24 L 222.59 573.78 Z M 235.64 583.71 C 234.45 584.24 232.47 584.86 229.63 584.94 L 229.63 577.34 L 236.53 574.69 L 236.53 582.34 C 236.53 582.93 236.18 583.47 235.64 583.71 Z M 221.47 582.34 L 221.47 574.69 L 228.37 577.34 L 228.37 584.94 C 225.53 584.86 223.55 584.24 222.36 583.71 C 221.82 583.47 221.47 582.93 221.47 582.34 Z M 221.85 584.86 C 223.23 585.48 225.59 586.21 229 586.21 C 232.41 586.21 234.77 585.48 236.15 584.86 C 237.14 584.42 237.78 583.43 237.78 582.34 L 237.78 573.78 C 237.78 573.52 237.62 573.28 237.38 573.19 L 229.23 570.06 C 229.08 570 228.92 570 228.77 570.06 L 220.62 573.19 C 220.38 573.28 220.22 573.52 220.22 573.78 L 220.22 582.34 C 220.22 583.43 220.86 584.42 221.85 584.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 613px; margin-left: 229px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Fargate Service</div></div></div></foreignObject><text x="229" y="625" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Fargat...</text></switch></g></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-11"><g><path d="M 99 276 L 519 276 L 519 476 L 99 476 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/><path d="M 99 276 L 124 276 L 124 301 L 99 301 Z M 111.52 279.21 C 110.4 279.21 109.31 279.63 108.48 280.39 C 107.67 281.11 107.2 282.15 107.2 283.24 L 107.2 285.78 L 104.89 285.78 C 104.8 285.78 104.7 285.82 104.64 285.89 C 104.57 285.95 104.54 286.04 104.54 286.13 L 104.54 297.43 C 104.54 297.63 104.7 297.79 104.89 297.79 L 118.11 297.79 C 118.3 297.79 118.46 297.63 118.46 297.43 L 118.46 286.15 C 118.47 286.06 118.43 285.97 118.36 285.9 C 118.3 285.83 118.21 285.79 118.11 285.79 L 115.81 285.79 L 115.81 283.29 C 115.8 282.21 115.35 281.18 114.56 280.44 C 113.74 279.65 112.65 279.22 111.52 279.21 Z M 111.51 279.93 C 112.46 279.92 113.37 280.28 114.06 280.93 C 114.72 281.54 115.1 282.4 115.1 283.29 L 115.1 285.79 L 107.88 285.79 L 107.89 283.26 C 107.9 282.36 108.28 281.51 108.95 280.91 C 109.65 280.27 110.57 279.92 111.51 279.93 Z M 105.24 286.5 L 117.76 286.5 L 117.75 297.07 L 105.24 297.07 Z M 111.51 288.74 C 110.48 288.73 109.61 289.51 109.51 290.53 C 109.42 291.56 110.13 292.48 111.14 292.66 L 111.14 295.44 L 111.86 295.44 L 111.86 292.66 C 112.79 292.49 113.47 291.67 113.48 290.72 C 113.48 289.63 112.6 288.75 111.51 288.74 Z M 111.39 289.45 C 111.43 289.45 111.47 289.45 111.51 289.46 C 111.84 289.46 112.16 289.59 112.4 289.83 C 112.64 290.07 112.77 290.39 112.76 290.72 C 112.77 291.06 112.64 291.38 112.4 291.61 C 112.16 291.85 111.84 291.98 111.51 291.98 C 111.04 292.02 110.6 291.8 110.34 291.42 C 110.08 291.03 110.06 290.53 110.28 290.12 C 110.5 289.71 110.93 289.46 111.39 289.45 Z" fill="#248814" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 388px; height: 1px; padding-top: 283px; margin-left: 131px;"><div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Public subnet</div></div></div></foreignObject><text x="131" y="295" fill="#248814" font-family="&quot;Helvetica&quot;" font-size="12px">Public subnet</text></switch></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-12"><g><rect x="119" y="316" width="270" height="140" fill="none" stroke="#ff6666" stroke-dasharray="3 3" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-13"><g><path d="M 119 316 L 139 316 L 139 336 L 119 336 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 121.68 326.29 L 121 326.29 L 121 325.71 L 121.69 325.71 C 121.74 324.27 122.21 322.89 123.06 321.72 L 123.52 322.06 C 122.75 323.13 122.31 324.39 122.26 325.71 L 123 325.71 L 123 326.29 L 122.26 326.29 C 122.31 327.62 122.74 328.88 123.52 329.96 L 123.06 330.3 C 122.21 329.12 121.74 327.74 121.68 326.29 Z M 133.29 331.95 C 132.11 332.8 130.73 333.27 129.29 333.33 L 129.29 334 L 128.71 334 L 128.71 333.33 C 127.27 333.27 125.89 332.8 124.71 331.95 L 125.05 331.49 C 126.12 332.27 127.39 332.7 128.71 332.75 L 128.71 332 L 129.29 332 L 129.29 332.75 C 130.61 332.7 131.88 332.27 132.95 331.49 Z M 124.71 320.07 C 125.89 319.22 127.27 318.75 128.71 318.7 L 128.71 318 L 129.29 318 L 129.29 318.7 C 130.73 318.75 132.11 319.22 133.29 320.07 L 132.95 320.53 C 131.88 319.75 130.61 319.32 129.29 319.27 L 129.29 320 L 128.71 320 L 128.71 319.27 C 127.39 319.32 126.12 319.75 125.05 320.53 Z M 137 325.71 L 137 326.29 L 136.31 326.29 C 136.26 327.74 135.79 329.12 134.94 330.3 L 134.48 329.96 C 135.26 328.88 135.69 327.62 135.74 326.29 L 135 326.29 L 135 325.71 L 135.74 325.71 C 135.69 324.39 135.25 323.13 134.48 322.06 L 134.94 321.72 C 135.79 322.89 136.26 324.27 136.31 325.71 Z M 133.28 321.33 L 135.52 319.09 L 135.92 319.49 L 133.68 321.73 Z M 124.72 330.69 L 122.48 332.93 L 122.08 332.53 L 124.32 330.29 Z M 125.36 322.76 L 121.12 318.52 L 121.52 318.12 L 125.76 322.36 Z M 132.63 329.23 L 136.88 333.48 L 136.48 333.88 L 132.23 329.63 Z M 126.62 326.21 C 126.64 326.17 126.66 326.13 126.69 326.1 C 127.17 325.34 127.03 324.31 126.88 323.69 C 127.29 323.96 127.66 324.52 127.79 324.75 C 127.84 324.85 127.95 324.91 128.06 324.9 C 128.17 324.89 128.27 324.82 128.31 324.71 C 128.73 323.5 128.52 322.6 128.22 322.01 C 128.58 322.22 128.86 322.52 129.05 322.91 C 129.45 323.75 129.36 324.9 128.81 325.91 C 128.06 327.3 128.21 328.74 128.38 329.52 C 127.96 329.34 127.57 329.13 127.24 328.9 C 126.38 328.3 126.1 327.12 126.62 326.21 Z M 130.46 326.11 C 130.44 326.23 130.49 326.35 130.59 326.41 C 130.7 326.47 130.82 326.46 130.92 326.38 C 130.94 326.36 131.41 325.98 131.64 325.04 C 131.91 325.44 132.19 326.24 131.81 327.72 C 131.44 329.17 129.65 329.58 129.01 329.68 C 128.86 329.13 128.55 327.6 129.32 326.19 C 129.86 325.18 130.01 324.05 129.73 323.11 C 130.25 323.72 130.69 324.68 130.46 326.11 Z M 126.12 325.93 C 125.46 327.09 125.81 328.6 126.92 329.37 C 127.42 329.72 128.02 330.02 128.72 330.27 C 128.75 330.28 128.78 330.29 128.81 330.29 C 128.82 330.29 128.82 330.28 128.83 330.28 L 128.83 330.28 C 128.95 330.28 131.81 330.03 132.37 327.86 C 133.09 325.07 131.64 324.24 131.58 324.21 C 131.5 324.16 131.4 324.16 131.31 324.2 C 131.23 324.25 131.17 324.33 131.16 324.43 C 131.14 324.62 131.11 324.79 131.07 324.94 C 130.87 322.75 129.14 321.86 128.79 321.7 C 128.43 321.43 127.99 321.24 127.48 321.15 C 127.35 321.12 127.23 321.19 127.17 321.31 C 127.12 321.42 127.15 321.56 127.25 321.65 C 127.29 321.68 128.18 322.46 127.93 323.93 C 127.6 323.49 127.07 322.97 126.45 322.97 C 126.35 322.97 126.26 323.01 126.21 323.09 C 126.16 323.17 126.15 323.27 126.18 323.35 C 126.19 323.37 126.77 324.9 126.2 325.79 C 126.18 325.84 126.15 325.88 126.12 325.93 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 326px; margin-left: 141px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">WAF(Web ACL)</div></div></div></foreignObject><text x="141" y="330" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">WAF(...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-14"><g/><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-15"><g><rect x="259" y="346" width="110" height="100" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="1 4" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-16"><g><rect x="269" y="356" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 289 356 C 277.97 356 269 364.97 269 376 C 269 387.03 277.97 396 289 396 C 300.03 396 309 387.03 309 376 C 309 364.97 300.03 356 289 356 Z M 289 394.18 C 278.97 394.18 270.82 386.03 270.82 376 C 270.82 365.97 278.97 357.82 289 357.82 C 299.03 357.82 307.18 365.97 307.18 376 C 307.18 386.03 299.03 394.18 289 394.18 Z M 300.85 381.45 L 299.45 381.45 L 299.45 378.39 C 299.45 377.88 299.05 377.48 298.55 377.48 L 296.27 377.48 L 296.27 374.41 C 296.27 373.91 295.87 373.5 295.36 373.5 L 289.91 373.5 L 289.91 371.34 L 295.36 371.34 C 295.87 371.34 296.27 370.93 296.27 370.43 L 296.27 363.27 C 296.27 362.77 295.87 362.36 295.36 362.36 L 282.64 362.36 C 282.13 362.36 281.73 362.77 281.73 363.27 L 281.73 370.43 C 281.73 370.93 282.13 371.34 282.64 371.34 L 288.09 371.34 L 288.09 373.5 L 282.64 373.5 C 282.13 373.5 281.73 373.91 281.73 374.41 L 281.73 377.48 L 279.46 377.48 C 278.95 377.48 278.55 377.88 278.55 378.39 L 278.55 381.45 L 277.15 381.45 C 276.65 381.45 276.24 381.86 276.24 382.36 L 276.24 386.34 C 276.24 386.84 276.65 387.25 277.15 387.25 L 281.05 387.25 C 281.55 387.25 281.96 386.84 281.96 386.34 L 281.96 382.36 C 281.96 381.86 281.55 381.45 281.05 381.45 L 280.36 381.45 L 280.36 379.3 L 284.11 379.3 L 284.11 381.45 L 283.43 381.45 C 282.93 381.45 282.52 381.86 282.52 382.36 L 282.52 386.34 C 282.52 386.84 282.93 387.25 283.43 387.25 L 287.41 387.25 C 287.91 387.25 288.32 386.84 288.32 386.34 L 288.32 382.36 C 288.32 381.86 287.91 381.45 287.41 381.45 L 285.93 381.45 L 285.93 378.39 C 285.93 377.88 285.53 377.48 285.02 377.48 L 283.55 377.48 L 283.55 375.32 L 294.45 375.32 L 294.45 377.48 L 292.98 377.48 C 292.47 377.48 292.07 377.88 292.07 378.39 L 292.07 381.45 L 290.59 381.45 C 290.09 381.45 289.68 381.86 289.68 382.36 L 289.68 386.34 C 289.68 386.84 290.09 387.25 290.59 387.25 L 294.57 387.25 C 295.07 387.25 295.48 386.84 295.48 386.34 L 295.48 382.36 C 295.48 381.86 295.07 381.45 294.57 381.45 L 293.89 381.45 L 293.89 379.3 L 297.64 379.3 L 297.64 381.45 L 296.9 381.45 C 296.4 381.45 295.99 381.86 295.99 382.36 L 295.99 386.34 C 295.99 386.84 296.4 387.25 296.9 387.25 L 300.85 387.25 C 301.35 387.25 301.76 386.84 301.76 386.34 L 301.76 382.36 C 301.76 381.86 301.35 381.45 300.85 381.45 Z M 283.55 369.52 L 283.55 364.18 L 294.45 364.18 L 294.45 369.52 Z M 278.06 385.43 L 278.06 383.27 L 280.14 383.27 L 280.14 385.43 Z M 284.34 385.43 L 284.34 383.27 L 286.5 383.27 L 286.5 385.43 Z M 291.5 385.43 L 291.5 383.27 L 293.66 383.27 L 293.66 385.43 Z M 297.81 385.43 L 297.81 383.27 L 299.94 383.27 L 299.94 385.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 403px; margin-left: 289px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ALB<br />(BG)</div></div></div></foreignObject><text x="289" y="415" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ALB...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-17"><g><rect x="319" y="356" width="40" height="30" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 371px; margin-left: 320px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Blue<br />Listener</div></div></div></foreignObject><text x="339" y="374" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="10px" text-anchor="middle">Blue...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-18"><g><rect x="319" y="406" width="40" height="30" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 421px; margin-left: 320px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Gleen<br />Listener</div></div></div></foreignObject><text x="339" y="424" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="10px" text-anchor="middle">Gleen...</text></switch></g></g></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-19"><g><path d="M 919 356 L 959 356 L 959 396 L 919 396 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 937.6 366.18 L 939.84 366.18 L 939.84 365.05 L 937.6 365.05 Z M 933.67 366.18 L 935.91 366.18 L 935.91 365.05 L 933.67 365.05 Z M 929.74 366.18 L 931.98 366.18 L 931.98 365.05 L 929.74 365.05 Z M 953.15 382.09 C 952.98 382.49 952.62 382.79 952.19 382.93 L 952.19 379.76 C 952.53 379.88 952.83 380.1 953.04 380.41 C 953.36 380.9 953.4 381.51 953.15 382.09 Z M 924.85 382.09 C 924.6 381.51 924.64 380.9 924.96 380.41 C 925.17 380.1 925.47 379.88 925.81 379.76 L 925.81 382.93 C 925.38 382.79 925.02 382.49 924.85 382.09 Z M 953.98 379.79 C 953.56 379.16 952.92 378.74 952.19 378.59 L 952.19 377.59 C 952.19 375.63 950.68 374.04 948.82 374.04 L 938.16 374.04 L 938.16 375.16 L 948.82 375.16 C 950.06 375.16 951.07 376.25 951.07 377.59 L 951.07 385.08 C 951.07 386.42 950.06 387.51 948.82 387.51 L 929.18 387.51 C 927.94 387.51 926.93 386.42 926.93 385.08 L 926.93 377.59 C 926.93 376.25 927.94 375.16 929.18 375.16 L 931.98 375.16 L 931.98 374.04 L 929.18 374.04 C 927.32 374.04 925.81 375.63 925.81 377.59 L 925.81 378.59 C 925.08 378.74 924.44 379.16 924.02 379.79 C 923.49 380.6 923.41 381.6 923.82 382.54 C 924.17 383.35 924.92 383.92 925.81 384.09 L 925.81 385.08 C 925.81 387.04 927.32 388.63 929.18 388.63 L 935.91 388.63 L 935.91 392 L 937.04 392 L 937.04 388.63 L 940.4 388.63 L 940.4 392 L 941.53 392 L 941.53 388.63 L 948.82 388.63 C 950.68 388.63 952.19 387.04 952.19 385.08 L 952.19 384.09 C 953.08 383.92 953.83 383.35 954.18 382.54 C 954.59 381.6 954.51 380.6 953.98 379.79 Z M 934.23 381.33 C 934.23 382.26 933.47 383.02 932.54 383.02 C 931.62 383.02 930.86 382.26 930.86 381.33 C 930.86 380.4 931.62 379.65 932.54 379.65 C 933.47 379.65 934.23 380.4 934.23 381.33 Z M 929.74 381.33 C 929.74 382.88 931 384.14 932.54 384.14 C 934.09 384.14 935.35 382.88 935.35 381.33 C 935.35 379.79 934.09 378.53 932.54 378.53 C 931 378.53 929.74 379.79 929.74 381.33 Z M 943.77 381.33 C 943.77 380.4 944.53 379.65 945.46 379.65 C 946.38 379.65 947.14 380.4 947.14 381.33 C 947.14 382.26 946.38 383.02 945.46 383.02 C 944.53 383.02 943.77 382.26 943.77 381.33 Z M 948.26 381.33 C 948.26 379.79 947 378.53 945.46 378.53 C 943.91 378.53 942.65 379.79 942.65 381.33 C 942.65 382.88 943.91 384.14 945.46 384.14 C 947 384.14 948.26 382.88 948.26 381.33 Z M 927.49 361.6 C 927.49 361.34 927.71 361.12 927.97 361.12 L 942.09 361.12 L 942.09 370.17 C 942.09 370.44 941.86 370.67 941.59 370.67 L 935.91 370.67 C 935.6 370.67 935.35 370.92 935.35 371.23 L 935.35 374.14 L 932.87 370.89 C 932.76 370.75 932.6 370.67 932.42 370.67 L 927.97 370.67 C 927.71 370.67 927.49 370.45 927.49 370.19 Z M 927.97 371.79 L 932.15 371.79 L 935.47 376.14 C 935.57 376.28 935.74 376.36 935.91 376.36 C 935.97 376.36 936.03 376.35 936.09 376.33 C 936.32 376.26 936.47 376.04 936.47 375.8 L 936.47 371.79 L 941.59 371.79 C 942.48 371.79 943.21 371.06 943.21 370.17 L 943.21 360.86 C 943.21 360.38 942.83 360 942.35 360 L 927.97 360 C 927.09 360 926.37 360.72 926.37 361.6 L 926.37 370.19 C 926.37 371.07 927.09 371.79 927.97 371.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 376px; margin-left: 961px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ChatBot</div></div></div></foreignObject><text x="961" y="380" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">ChatBot</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-20"><g><path d="M 919 436 L 959 436 L 959 476 L 919 476 Z" fill="url(#mx-gradient-ff4f8b-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 925.33 454.84 C 925.97 454.84 926.49 455.36 926.49 456 C 926.49 456.64 925.97 457.16 925.33 457.16 C 924.69 457.16 924.16 456.64 924.16 456 C 924.16 455.36 924.69 454.84 925.33 454.84 Z M 939.29 469.96 C 932.49 469.96 926.66 465 925.44 458.32 C 926.47 458.27 927.32 457.55 927.57 456.58 L 931.15 456.58 L 931.15 455.42 L 927.57 455.42 C 927.32 454.45 926.47 453.73 925.44 453.68 C 926.66 447.1 932.61 442.04 939.29 442.04 C 941.8 442.04 944.43 442.98 947.1 444.84 L 947.77 443.89 C 944.9 441.89 942.04 440.87 939.29 440.87 C 931.88 440.87 925.3 446.61 924.2 453.98 C 923.49 454.37 923 455.13 923 456 C 923 456.87 923.49 457.63 924.2 458.02 C 925.31 465.51 931.76 471.13 939.29 471.13 C 942.44 471.13 945.66 470.05 948.36 468.11 L 947.68 467.16 C 945.17 468.97 942.19 469.96 939.29 469.96 Z M 932.95 453.93 C 934.03 454.19 935.38 454.25 936.38 454.25 C 937.34 454.25 938.61 454.2 939.66 453.97 L 937.02 459.23 C 936.98 459.31 936.96 459.4 936.96 459.49 L 936.96 462.08 C 936.5 462.38 935.7 462.87 935.22 462.97 L 935.22 459.49 C 935.22 459.41 935.2 459.34 935.17 459.27 Z M 936.38 451.93 C 938.72 451.93 939.97 452.28 940.36 452.51 C 939.97 452.74 938.72 453.09 936.38 453.09 C 934.04 453.09 932.79 452.74 932.4 452.51 C 932.79 452.28 934.04 451.93 936.38 451.93 Z M 934.64 464.15 L 935.22 464.15 C 935.24 464.15 935.27 464.14 935.29 464.14 C 936 464.05 936.91 463.5 937.7 462.99 L 937.86 462.89 C 938.02 462.78 938.13 462.6 938.13 462.4 L 938.13 459.63 L 941.36 453.16 C 941.52 452.98 941.62 452.76 941.62 452.51 C 941.62 450.99 938.34 450.76 936.38 450.76 C 934.43 450.76 931.15 450.99 931.15 452.51 C 931.15 452.72 931.22 452.91 931.33 453.07 L 934.05 459.61 L 934.05 463.56 C 934.05 463.88 934.31 464.15 934.64 464.15 Z M 951.51 463.56 C 952.15 463.56 952.67 464.09 952.67 464.73 C 952.67 465.37 952.15 465.89 951.51 465.89 C 950.87 465.89 950.35 465.37 950.35 464.73 C 950.35 464.09 950.87 463.56 951.51 463.56 Z M 951.51 446.11 C 952.15 446.11 952.67 446.63 952.67 447.27 C 952.67 447.91 952.15 448.44 951.51 448.44 C 950.87 448.44 950.35 447.91 950.35 447.27 C 950.35 446.63 950.87 446.11 951.51 446.11 Z M 952.67 454.84 C 953.31 454.84 953.84 455.36 953.84 456 C 953.84 456.64 953.31 457.16 952.67 457.16 C 952.03 457.16 951.51 456.64 951.51 456 C 951.51 455.36 952.03 454.84 952.67 454.84 Z M 946.85 456.58 L 950.43 456.58 C 950.69 457.58 951.59 458.33 952.67 458.33 C 953.96 458.33 955 457.28 955 456 C 955 454.72 953.96 453.67 952.67 453.67 C 951.59 453.67 950.69 454.42 950.43 455.42 L 946.85 455.42 L 946.85 447.85 L 949.27 447.85 C 949.52 448.86 950.43 449.6 951.51 449.6 C 952.79 449.6 953.84 448.56 953.84 447.27 C 953.84 445.99 952.79 444.95 951.51 444.95 C 950.43 444.95 949.52 445.69 949.27 446.69 L 946.27 446.69 C 945.95 446.69 945.69 446.95 945.69 447.27 L 945.69 455.42 L 941.62 455.42 L 941.62 456.58 L 945.69 456.58 L 945.69 464.73 C 945.69 465.05 945.95 465.31 946.27 465.31 L 949.27 465.31 C 949.52 466.31 950.43 467.05 951.51 467.05 C 952.79 467.05 953.84 466.01 953.84 464.73 C 953.84 463.44 952.79 462.4 951.51 462.4 C 950.43 462.4 949.52 463.14 949.27 464.15 L 946.85 464.15 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 456px; margin-left: 961px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">SNS Topic</div></div></div></foreignObject><text x="961" y="460" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">SNS Top...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-21"><g><path d="M 1039 356 L 1079 356 L 1079 396 L 1039 396 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 1066.38 383.56 L 1070.69 383.56 L 1070.69 382.33 L 1066.38 382.33 Z M 1060.85 383.56 L 1065.15 383.56 L 1065.15 382.33 L 1060.85 382.33 Z M 1055.31 383.56 L 1059.61 383.56 L 1059.61 382.33 L 1055.31 382.33 Z M 1062.08 378.63 C 1062.08 377.96 1062.63 377.4 1063.31 377.4 C 1063.99 377.4 1064.54 377.96 1064.54 378.63 C 1064.54 379.31 1063.99 379.86 1063.31 379.86 C 1062.63 379.86 1062.08 379.31 1062.08 378.63 Z M 1065.77 378.63 C 1065.77 377.28 1064.67 376.17 1063.31 376.17 C 1061.95 376.17 1060.85 377.28 1060.85 378.63 C 1060.85 379.99 1061.95 381.1 1063.31 381.1 C 1064.67 381.1 1065.77 379.99 1065.77 378.63 Z M 1057.15 377.4 C 1057.83 377.4 1058.38 377.96 1058.38 378.63 C 1058.38 379.31 1057.83 379.86 1057.15 379.86 C 1056.47 379.86 1055.92 379.31 1055.92 378.63 C 1055.92 377.96 1056.47 377.4 1057.15 377.4 Z M 1057.15 381.1 C 1058.51 381.1 1059.61 379.99 1059.61 378.63 C 1059.61 377.28 1058.51 376.17 1057.15 376.17 C 1055.79 376.17 1054.69 377.28 1054.69 378.63 C 1054.69 379.99 1055.79 381.1 1057.15 381.1 Z M 1075 371.86 L 1075 386.02 C 1075 386.36 1074.72 386.63 1074.38 386.63 L 1055.31 386.63 L 1055.31 385.4 L 1073.77 385.4 L 1073.77 372.48 L 1060.23 372.48 L 1060.23 371.25 L 1074.38 371.25 C 1074.72 371.25 1075 371.52 1075 371.86 Z M 1053.27 374.97 C 1053.02 375.05 1052.84 375.29 1052.84 375.56 L 1052.84 388.51 L 1051.37 389.77 L 1049.77 387.94 L 1049.77 386.33 C 1049.77 386.16 1049.7 386.01 1049.59 385.89 L 1048.18 384.48 L 1049.59 383.07 C 1049.7 382.95 1049.77 382.8 1049.77 382.63 L 1049.77 381.4 C 1049.77 381.24 1049.7 381.08 1049.59 380.97 L 1048.18 379.56 L 1049.59 378.15 C 1049.7 378.03 1049.77 377.87 1049.77 377.71 L 1049.77 375.56 C 1049.77 375.29 1049.59 375.06 1049.34 374.98 C 1046.17 373.97 1044.3 370.72 1044.99 367.4 C 1045.48 365.02 1047.32 363.1 1049.67 362.53 C 1051.66 362.04 1053.72 362.46 1055.29 363.7 C 1056.87 364.94 1057.77 366.79 1057.77 368.79 C 1057.77 371.59 1055.92 374.13 1053.27 374.97 Z M 1059 368.79 C 1059 366.41 1057.92 364.2 1056.05 362.73 C 1054.18 361.26 1051.74 360.75 1049.37 361.33 C 1046.57 362.02 1044.37 364.3 1043.78 367.16 L 1043.78 367.16 C 1043 370.94 1045.03 374.66 1048.54 376 L 1048.54 377.46 L 1046.87 379.12 C 1046.63 379.36 1046.63 379.75 1046.87 379.99 L 1048.54 381.66 L 1048.54 382.38 L 1046.87 384.05 C 1046.63 384.29 1046.63 384.68 1046.87 384.92 L 1048.54 386.58 L 1048.54 388.17 C 1048.54 388.32 1048.59 388.47 1048.69 388.58 L 1050.84 391.04 C 1050.96 391.18 1051.13 391.25 1051.31 391.25 C 1051.45 391.25 1051.59 391.2 1051.71 391.1 L 1053.86 389.26 C 1054 389.14 1054.08 388.97 1054.08 388.79 L 1054.08 375.99 C 1057 374.87 1059 371.97 1059 368.79 Z M 1051.31 370.25 C 1050.46 370.25 1049.77 369.56 1049.77 368.71 C 1049.77 367.86 1050.46 367.17 1051.31 367.17 C 1052.15 367.17 1052.84 367.86 1052.84 368.71 C 1052.84 369.56 1052.15 370.25 1051.31 370.25 Z M 1051.31 365.94 C 1049.78 365.94 1048.54 367.18 1048.54 368.71 C 1048.54 370.23 1049.78 371.48 1051.31 371.48 C 1052.83 371.48 1054.08 370.23 1054.08 368.71 C 1054.08 367.18 1052.83 365.94 1051.31 365.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 376px; margin-left: 1081px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">KMS Key</div></div></div></foreignObject><text x="1081" y="380" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">KMS Key</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-22"><g><path d="M 1039 436 L 1079 436 L 1079 476 L 1039 476 Z" fill="url(#mx-gradient-ff4f8b-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 1068.14 463.43 L 1049.86 463.43 L 1049.86 461.14 L 1055 461.14 C 1055.32 461.14 1055.57 460.89 1055.57 460.57 L 1055.57 457.14 C 1055.57 456.83 1055.32 456.57 1055 456.57 L 1049.86 456.57 L 1049.86 455.43 L 1055 455.43 C 1055.32 455.43 1055.57 455.17 1055.57 454.86 L 1055.57 451.43 C 1055.57 451.11 1055.32 450.86 1055 450.86 L 1049.86 450.86 L 1049.86 449.14 L 1068.14 449.14 L 1068.14 451.43 L 1069.29 451.43 L 1069.29 448.57 C 1069.29 448.26 1069.03 448 1068.71 448 L 1059.57 448 L 1059.57 446.23 C 1061.03 445.96 1062.14 444.68 1062.14 443.14 C 1062.14 441.41 1060.73 440 1059 440 C 1057.27 440 1055.86 441.41 1055.86 443.14 C 1055.86 444.68 1056.97 445.96 1058.43 446.23 L 1058.43 448 L 1049.29 448 C 1048.97 448 1048.71 448.26 1048.71 448.57 L 1048.71 450.86 L 1043.57 450.86 C 1043.26 450.86 1043 451.11 1043 451.43 L 1043 454.86 C 1043 455.17 1043.26 455.43 1043.57 455.43 L 1048.71 455.43 L 1048.71 456.57 L 1043.57 456.57 C 1043.26 456.57 1043 456.83 1043 457.14 L 1043 460.57 C 1043 460.89 1043.26 461.14 1043.57 461.14 L 1048.71 461.14 L 1048.71 464 C 1048.71 464.32 1048.97 464.57 1049.29 464.57 L 1058.43 464.57 L 1058.43 465.77 C 1056.97 466.04 1055.86 467.32 1055.86 468.86 C 1055.86 470.59 1057.27 472 1059 472 C 1060.73 472 1062.14 470.59 1062.14 468.86 C 1062.14 467.32 1061.03 466.04 1059.57 465.77 L 1059.57 464.57 L 1068.71 464.57 C 1069.03 464.57 1069.29 464.32 1069.29 464 L 1069.29 461.16 L 1068.14 461.16 Z M 1057 443.14 C 1057 442.04 1057.9 441.14 1059 441.14 C 1060.1 441.14 1061 442.04 1061 443.14 C 1061 444.25 1060.1 445.14 1059 445.14 C 1057.9 445.14 1057 444.25 1057 443.14 Z M 1044.14 454.29 L 1044.14 452 L 1054.43 452 L 1054.43 454.29 Z M 1044.14 460 L 1044.14 457.71 L 1054.43 457.71 L 1054.43 460 Z M 1061 468.86 C 1061 469.96 1060.1 470.86 1059 470.86 C 1057.9 470.86 1057 469.96 1057 468.86 C 1057 467.75 1057.9 466.86 1059 466.86 C 1060.1 466.86 1061 467.75 1061 468.86 Z M 1074.43 453.14 L 1061.29 453.14 C 1060.97 453.14 1060.71 453.4 1060.71 453.71 L 1060.71 458.86 C 1060.71 459.17 1060.97 459.43 1061.29 459.43 L 1074.43 459.43 C 1074.74 459.43 1075 459.17 1075 458.86 L 1075 453.71 C 1075 453.4 1074.74 453.14 1074.43 453.14 Z M 1061.86 458.29 L 1061.86 454.29 L 1073.86 454.29 L 1073.86 458.29 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 456px; margin-left: 1081px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">StepFunctions</div></div></div></foreignObject><text x="1081" y="460" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">StepFun...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-23"><g><path d="M 919 276 L 959 276 L 959 316 L 919 316 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/><path d="M 938.04 294.38 C 937.76 294.54 937.59 294.84 937.59 295.16 L 937.59 310.72 L 925.82 304.14 L 925.82 288.85 L 939.03 281.19 L 950.77 287.01 Z M 952.13 286.99 C 952.13 286.67 951.96 286.37 951.65 286.19 L 939.47 280.16 C 939.2 280 938.85 280 938.57 280.16 L 925.15 287.94 C 924.87 288.1 924.7 288.4 924.7 288.72 L 924.7 304.27 C 924.7 304.59 924.87 304.89 925.16 305.06 L 937.36 311.88 C 937.5 311.96 937.65 312 937.81 312 C 937.97 312 938.12 311.96 938.26 311.88 C 938.54 311.72 938.71 311.42 938.71 311.1 L 938.71 295.29 L 951.68 287.78 C 951.96 287.62 952.13 287.32 952.13 286.99 Z M 952.16 304.18 L 941.51 310.69 L 941.51 304.86 L 947.06 301.28 L 947.1 301.02 C 947.11 300.96 947.12 300.96 947.12 300.66 L 947.13 293.7 L 952.18 290.78 Z M 952.85 289.61 C 952.57 289.45 952.22 289.45 951.95 289.61 L 946.58 292.72 C 946.41 292.81 946.01 293.03 946.01 293.47 L 946 300.63 L 940.88 303.93 C 940.57 304.11 940.39 304.4 940.39 304.7 L 940.39 311.05 C 940.39 311.37 940.56 311.66 940.85 311.82 C 941 311.91 941.17 311.95 941.33 311.95 C 941.49 311.95 941.66 311.91 941.8 311.83 L 952.83 305.08 C 953.11 304.92 953.28 304.62 953.28 304.3 L 953.3 290.39 C 953.3 290.07 953.13 289.77 952.85 289.61 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 296px; margin-left: 961px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ECR</div></div></div></foreignObject><text x="961" y="300" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">ECR</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-24"><g/><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px"><text x="1080.5" y="300.5">CloudWatch Logs</text></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-25"><g><rect x="1039" y="281.13" width="40" height="29.75" fill="none" stroke="none" pointer-events="all"/><path d="M 1061.7 307.49 L 1066.44 307.49 L 1066.44 306.14 L 1061.7 306.14 Z M 1051.56 307.49 L 1060.35 307.49 L 1060.35 306.14 L 1051.56 306.14 Z M 1057.65 304.11 L 1066.44 304.11 L 1066.44 302.76 L 1057.65 302.76 Z M 1051.56 304.11 L 1056.3 304.11 L 1056.3 302.76 L 1051.56 302.76 Z M 1058.29 300.27 L 1057.01 299.84 L 1059.71 291.73 L 1060.99 292.16 Z M 1066.18 295.47 C 1066.34 295.6 1066.44 295.8 1066.44 296 C 1066.44 296.21 1066.34 296.4 1066.18 296.53 L 1062.8 299.23 L 1061.96 298.18 L 1064.68 296 L 1061.96 293.82 L 1062.8 292.77 Z M 1051.82 296.53 C 1051.66 296.4 1051.56 296.21 1051.56 296 C 1051.56 295.8 1051.66 295.6 1051.82 295.47 L 1055.2 292.77 L 1056.04 293.82 L 1053.32 296 L 1056.04 298.18 L 1055.2 299.23 Z M 1049.53 309.52 L 1049.54 282.48 L 1061.7 282.48 L 1061.7 288.56 C 1061.7 288.94 1062.01 289.24 1062.38 289.24 L 1068.47 289.24 L 1068.47 309.52 Z M 1063.06 283.43 L 1067.51 287.89 L 1063.06 287.89 Z M 1069.62 288.08 L 1062.86 281.32 C 1062.73 281.2 1062.56 281.13 1062.38 281.13 L 1049.53 281.13 C 1048.74 281.13 1048.18 281.68 1048.18 282.48 L 1048.18 309.52 C 1048.18 310.29 1048.76 310.88 1049.53 310.88 L 1068.47 310.88 C 1069.24 310.88 1069.82 310.29 1069.82 309.52 L 1069.82 288.56 C 1069.82 288.38 1069.75 288.21 1069.62 288.08 Z" fill="#b0084d" stroke="none" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-26"><g><rect x="1039" y="276" width="40" height="40" fill="none" stroke="none" pointer-events="all"/></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-27"><g><path d="M 919 516 L 959 516 L 959 556 L 919 556 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 947.43 540.18 C 947.43 537.96 945.62 536.16 943.38 536.16 C 941.15 536.16 939.34 537.96 939.34 540.18 C 939.34 542.4 941.15 544.21 943.38 544.21 C 945.62 544.21 947.43 542.4 947.43 540.18 M 948.58 540.18 C 948.58 543.03 946.25 545.34 943.38 545.34 C 940.52 545.34 938.19 543.03 938.19 540.18 C 938.19 537.34 940.52 535.03 943.38 535.03 C 946.25 535.03 948.58 537.34 948.58 540.18 M 953.37 547.95 L 949.39 544.4 C 949.07 544.84 948.7 545.26 948.28 545.62 L 952.25 549.18 C 952.59 549.48 953.12 549.46 953.43 549.12 C 953.73 548.78 953.7 548.26 953.37 547.95 M 943.38 546.36 C 946.81 546.36 949.6 543.59 949.6 540.18 C 949.6 536.78 946.81 534.01 943.38 534.01 C 939.96 534.01 937.17 536.78 937.17 540.18 C 937.17 543.59 939.96 546.36 943.38 546.36 M 954.27 549.88 C 953.89 550.31 953.35 550.52 952.81 550.52 C 952.34 550.52 951.87 550.36 951.49 550.02 L 947.36 546.32 C 946.21 547.06 944.85 547.49 943.38 547.49 C 939.33 547.49 936.03 544.21 936.03 540.18 C 936.03 536.15 939.33 532.87 943.38 532.87 C 947.44 532.87 950.74 536.15 950.74 540.18 C 950.74 541.34 950.47 542.43 949.98 543.4 L 954.13 547.11 C 954.93 547.84 955 549.08 954.27 549.88 M 928.27 530.32 C 928.27 530.61 928.28 530.9 928.32 531.19 C 928.34 531.35 928.29 531.51 928.18 531.63 C 928.09 531.73 927.98 531.79 927.85 531.82 C 926.45 532.18 924.14 533.27 924.14 536.53 C 924.14 539 925.51 540.36 926.66 541.07 C 927.05 541.32 927.52 541.45 928.01 541.45 L 934.89 541.46 L 934.88 542.59 L 928 542.58 C 927.29 542.58 926.62 542.39 926.06 542.03 C 924.92 541.33 923 539.68 923 536.53 C 923 532.74 925.61 531.34 927.14 530.84 C 927.13 530.67 927.13 530.49 927.13 530.32 C 927.13 527.22 929.24 524 932.05 522.84 C 935.33 521.48 938.8 522.15 941.34 524.65 C 942.13 525.43 942.78 526.37 943.28 527.45 C 943.94 526.9 944.77 526.6 945.63 526.6 C 947.34 526.6 949.25 527.89 949.6 530.7 C 951.19 531.06 954.56 532.34 954.56 536.58 C 954.56 538.27 954.03 539.67 952.98 540.73 L 952.16 539.93 C 953 539.09 953.42 537.96 953.42 536.58 C 953.42 532.87 950.3 531.95 948.95 531.72 C 948.8 531.7 948.67 531.61 948.58 531.48 C 948.49 531.36 948.46 531.21 948.49 531.07 C 948.3 528.77 946.93 527.73 945.63 527.73 C 944.82 527.73 944.05 528.13 943.54 528.82 C 943.41 528.98 943.2 529.07 942.99 529.04 C 942.79 529.01 942.61 528.87 942.54 528.67 C 942.08 527.38 941.4 526.3 940.54 525.46 C 938.34 523.29 935.33 522.71 932.49 523.89 C 930.12 524.87 928.27 527.69 928.27 530.32" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 536px; margin-left: 961px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">CloudWatch<br />Dashboard</div></div></div></foreignObject><text x="961" y="540" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">CloudWa...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-28"><g><rect x="919" y="596" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 926.54 615.05 L 924.73 615.06 C 924.76 619.62 926.87 623.79 930.52 626.51 C 933.09 628.43 936.11 629.36 939.1 629.36 C 943.49 629.36 947.83 627.36 950.65 623.57 C 955.4 617.21 954.08 608.18 947.72 603.44 C 941.56 598.85 932.91 599.95 928.06 605.8 L 928.09 603.29 L 926.27 603.26 L 926.21 607.81 C 926.21 608.05 926.3 608.28 926.47 608.45 C 926.64 608.63 926.87 608.72 927.11 608.73 L 931.71 608.79 L 931.74 606.97 L 929.47 606.94 C 933.71 601.85 941.25 600.89 946.63 604.89 C 952.19 609.04 953.34 616.93 949.2 622.49 C 945.05 628.05 937.16 629.2 931.6 625.05 C 928.42 622.68 926.57 619.03 926.54 615.05 Z M 939 611.45 C 936.99 611.45 935.36 613.09 935.36 615.09 C 935.36 617.1 936.99 618.73 939 618.73 C 941.01 618.73 942.64 617.1 942.64 615.09 C 942.64 613.09 941.01 611.45 939 611.45 Z M 932.59 620.35 L 934.61 618.32 C 933.95 617.41 933.55 616.3 933.55 615.09 C 933.55 613.84 933.98 612.68 934.69 611.76 L 932.59 609.81 L 933.83 608.48 L 936.03 610.52 C 936.88 609.97 937.9 609.64 939 609.64 C 940.22 609.64 941.34 610.04 942.25 610.72 L 944.44 608.48 L 945.74 609.75 L 943.51 612.03 C 944.11 612.91 944.45 613.96 944.45 615.09 C 944.45 616.23 944.1 617.29 943.5 618.16 L 945.74 620.32 L 944.48 621.63 L 942.24 619.47 C 941.33 620.14 940.21 620.55 939 620.55 C 937.86 620.55 936.79 620.19 935.92 619.59 L 933.87 621.63 Z M 949 634.18 L 952.64 634.18 L 952.64 633.27 L 949 633.27 Z M 925.36 634.18 L 929 634.18 L 929 633.27 L 925.36 633.27 Z M 959 596.91 L 959 632.36 C 959 632.87 958.59 633.27 958.09 633.27 L 954.45 633.27 L 954.45 635.09 C 954.45 635.59 954.05 636 953.55 636 L 948.09 636 C 947.59 636 947.18 635.59 947.18 635.09 L 947.18 633.27 L 930.82 633.27 L 930.82 635.09 C 930.82 635.59 930.41 636 929.91 636 L 924.45 636 C 923.95 636 923.55 635.59 923.55 635.09 L 923.55 633.27 L 919.91 633.27 C 919.41 633.27 919 632.87 919 632.36 L 919 624.18 L 920.82 624.18 L 920.82 631.45 L 957.18 631.45 L 957.18 597.82 L 920.82 597.82 L 920.82 604.18 L 919 604.18 L 919 596.91 C 919 596.41 919.41 596 919.91 596 L 958.09 596 C 958.59 596 959 596.41 959 596.91 Z M 919 618.73 L 920.82 618.73 L 920.82 609.64 L 919 609.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 616px; margin-left: 961px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Backup Vault</div></div></div></foreignObject><text x="961" y="620" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">Backup...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-29"><g/><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-30"><g><rect x="919" y="676" width="40" height="40" fill="none" stroke="none" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-31"><g><rect x="919.77" y="676" width="38.46" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 949.54 707.71 C 949.54 708.05 949.82 708.33 950.16 708.33 L 950.16 708.33 C 950.33 708.33 950.49 708.26 950.6 708.15 C 950.72 708.03 950.78 707.87 950.78 707.71 L 950.78 707.7 C 950.78 707.54 950.72 707.38 950.6 707.27 C 950.49 707.15 950.33 707.08 950.16 707.08 L 950.16 707.08 C 950 707.08 949.84 707.15 949.72 707.27 C 949.61 707.38 949.54 707.54 949.54 707.71 Z M 947.72 707.7 C 947.72 707.05 947.97 706.44 948.44 705.98 C 948.9 705.51 949.51 705.26 950.16 705.26 L 950.17 705.26 C 950.82 705.26 951.43 705.52 951.9 705.98 C 952.36 706.44 952.61 707.05 952.61 707.71 C 952.61 708.36 952.35 708.97 951.89 709.44 C 951.65 709.67 951.38 709.85 951.08 709.97 L 951.08 712.52 L 949.25 712.52 L 949.25 709.97 C 948.35 709.61 947.72 708.73 947.72 707.7 Z M 956.41 704.07 L 943.93 704.07 L 943.92 714.16 L 956.4 714.17 Z M 947.09 702.25 L 953.21 702.25 L 953.21 699.99 C 953.22 698.15 951.86 696.65 950.18 696.65 L 950.17 696.65 C 949.36 696.65 948.6 696.98 948.03 697.59 C 947.43 698.22 947.11 699.06 947.1 699.96 Z M 958.23 703.16 L 958.22 715.08 C 958.22 715.58 957.82 715.99 957.31 715.99 L 943.01 715.98 C 942.51 715.98 942.1 715.58 942.1 715.07 L 942.1 703.16 C 942.1 702.91 942.2 702.68 942.37 702.51 C 942.54 702.34 942.77 702.24 943.01 702.24 L 945.27 702.24 L 945.28 699.95 C 945.28 698.59 945.79 697.3 946.7 696.34 C 947.63 695.36 948.86 694.82 950.17 694.82 L 950.18 694.82 C 952.87 694.83 955.05 697.15 955.04 700 L 955.04 702.25 L 957.32 702.25 C 957.56 702.25 957.79 702.34 957.96 702.52 C 958.13 702.69 958.23 702.92 958.23 703.16 Z M 930.29 702.48 L 936.65 702.48 L 936.65 700.65 L 930.29 700.65 Z M 930.29 699.3 L 936.65 699.3 L 936.65 697.47 L 930.29 697.47 Z M 930.29 696.12 L 936.65 696.12 L 936.65 694.29 L 930.29 694.29 Z M 928.86 704.63 L 938.16 704.63 L 938.16 692.14 L 928.86 692.14 Z M 939.99 691.23 L 939.99 705.54 C 939.99 706.04 939.58 706.45 939.07 706.45 L 927.95 706.45 C 927.44 706.45 927.03 706.04 927.03 705.54 L 927.03 691.23 C 927.03 690.73 927.44 690.32 927.95 690.32 L 939.07 690.32 C 939.58 690.32 939.99 690.73 939.99 691.23 Z M 922.51 682.37 L 944.46 682.37 L 941.23 677.83 L 925.74 677.83 Z M 919.93 683.7 C 919.77 683.4 919.8 683.03 919.99 682.75 L 924.53 676.39 C 924.7 676.15 924.98 676.01 925.27 676.01 L 941.7 676.01 C 941.99 676.01 942.27 676.15 942.44 676.39 L 946.97 682.75 C 947.17 683.03 947.2 683.4 947.04 683.7 C 946.89 684 946.57 684.19 946.23 684.19 L 920.74 684.19 C 920.4 684.19 920.08 684 919.93 683.7 Z M 921.69 710.19 L 939.83 710.19 L 939.83 712.02 L 920.78 712.02 C 920.28 712.02 919.87 711.61 919.87 711.11 L 919.87 686.46 C 919.87 685.96 920.28 685.55 920.78 685.55 L 946.23 685.55 C 946.73 685.55 947.14 685.96 947.14 686.46 L 947.14 694.41 L 945.32 694.41 L 945.32 687.37 L 921.69 687.37 Z" fill="#b0084d" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 696px; margin-left: 960px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Parameter Store</div></div></div></foreignObject><text x="960" y="700" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">Paramet...</text></switch></g></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-32"><g><path d="M 99 676 L 519 676 L 519 816 L 99 816 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/><path d="M 99 676 L 124 676 L 124 701 L 99 701 Z M 111.52 679.21 C 110.4 679.21 109.31 679.63 108.48 680.39 C 107.67 681.11 107.2 682.15 107.2 683.24 L 107.2 685.78 L 104.89 685.78 C 104.8 685.78 104.7 685.82 104.64 685.89 C 104.57 685.95 104.54 686.04 104.54 686.13 L 104.54 697.43 C 104.54 697.63 104.7 697.79 104.89 697.79 L 118.11 697.79 C 118.3 697.79 118.46 697.63 118.46 697.43 L 118.46 686.15 C 118.47 686.06 118.43 685.97 118.36 685.9 C 118.3 685.83 118.21 685.79 118.11 685.79 L 115.81 685.79 L 115.81 683.29 C 115.8 682.21 115.35 681.18 114.56 680.44 C 113.74 679.65 112.65 679.22 111.52 679.21 Z M 111.51 679.93 C 112.46 679.92 113.37 680.28 114.06 680.93 C 114.72 681.54 115.1 682.4 115.1 683.29 L 115.1 685.79 L 107.88 685.79 L 107.89 683.26 C 107.9 682.36 108.28 681.51 108.95 680.91 C 109.65 680.27 110.57 679.92 111.51 679.93 Z M 105.24 686.5 L 117.76 686.5 L 117.75 697.07 L 105.24 697.07 Z M 111.51 688.74 C 110.48 688.73 109.61 689.51 109.51 690.53 C 109.42 691.56 110.13 692.48 111.14 692.66 L 111.14 695.44 L 111.86 695.44 L 111.86 692.66 C 112.79 692.49 113.47 691.67 113.48 690.72 C 113.48 689.63 112.6 688.75 111.51 688.74 Z M 111.39 689.45 C 111.43 689.45 111.47 689.45 111.51 689.46 C 111.84 689.46 112.16 689.59 112.4 689.83 C 112.64 690.07 112.77 690.39 112.76 690.72 C 112.77 691.06 112.64 691.38 112.4 691.61 C 112.16 691.85 111.84 691.98 111.51 691.98 C 111.04 692.02 110.6 691.8 110.34 691.42 C 110.08 691.03 110.06 690.53 110.28 690.12 C 110.5 689.71 110.93 689.46 111.39 689.45 Z" fill="#147eba" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 388px; height: 1px; padding-top: 683px; margin-left: 131px;"><div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Protected subnet</div></div></div></foreignObject><text x="131" y="695" fill="#147EBA" font-family="&quot;Helvetica&quot;" font-size="12px">Protected subnet</text></switch></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-33"><g><path d="M 222 726 L 262 726 L 262 766 L 222 766 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 237.08 736.87 C 240.66 735.37 243.2 737.34 244.12 738.26 C 244.87 739.01 245.46 739.96 245.87 741.1 C 245.94 741.29 246.11 741.44 246.32 741.47 C 246.53 741.5 246.74 741.42 246.86 741.25 C 247.31 740.65 247.97 740.31 248.67 740.31 C 249.71 740.31 250.98 741.1 251.11 743.33 C 251.12 743.59 251.31 743.8 251.56 743.85 C 254.15 744.4 255.46 745.83 255.46 748.12 C 255.46 750.25 254.49 751.56 252.57 752.03 L 252.84 753.14 C 255.27 752.55 256.61 750.77 256.61 748.12 C 256.61 745.43 255.05 743.56 252.21 742.83 C 251.9 740.3 250.24 739.16 248.67 739.16 C 247.92 739.16 247.2 739.42 246.61 739.89 C 246.17 738.94 245.61 738.13 244.93 737.45 C 242.66 735.2 239.57 734.59 236.64 735.82 C 234.14 736.87 232.26 739.76 232.26 742.55 C 232.26 742.68 232.26 742.82 232.27 742.97 C 229.97 743.73 228.62 745.61 228.62 748.09 C 228.62 748.22 228.62 748.34 228.63 748.46 C 228.75 750.61 230.18 752.47 232.29 753.19 L 232.66 752.11 C 231 751.54 229.86 750.08 229.77 748.4 C 229.77 748.3 229.76 748.2 229.76 748.09 C 229.76 745.19 231.8 744.24 233.02 743.94 C 233.3 743.87 233.48 743.61 233.45 743.32 C 233.41 743.04 233.4 742.78 233.4 742.55 C 233.4 740.23 235.02 737.74 237.08 736.87 Z M 248.86 748.86 C 248.86 748.63 248.81 748.46 248.72 748.38 C 248.62 748.29 248.45 748.28 248.32 748.29 L 240.86 748.29 C 240.54 748.29 240.29 748.03 240.29 747.71 C 240.29 747.56 240.23 747.13 240.16 746.86 L 238.18 746.86 C 238.1 747.12 238 747.57 238 747.72 C 237.99 748.03 237.74 748.29 237.43 748.29 L 235.71 748.29 C 235.55 748.28 235.38 748.29 235.28 748.38 C 235.19 748.46 235.14 748.63 235.14 748.86 L 235.14 756.86 L 248.86 756.86 Z M 250 750 L 250 754.13 L 251.48 750 Z M 249.97 757.62 L 249.96 757.62 C 249.88 757.84 249.68 758 249.43 758 L 234.57 758 C 234.26 758 234 757.75 234 757.43 L 234 748.86 C 234 748.16 234.27 747.76 234.5 747.54 C 234.72 747.34 235.11 747.1 235.75 747.15 L 236.92 747.14 C 237.03 746.55 237.28 745.71 237.89 745.71 L 240.49 745.71 C 240.53 745.71 240.56 745.72 240.6 745.72 C 241.13 745.82 241.32 746.59 241.39 747.14 L 248.29 747.14 C 248.89 747.1 249.28 747.34 249.5 747.54 C 249.73 747.76 250 748.16 250 748.86 L 252.29 748.86 C 252.47 748.86 252.65 748.95 252.75 749.1 C 252.86 749.25 252.89 749.45 252.82 749.62 Z M 256.86 760.05 L 252.51 755.7 L 251.7 756.51 L 256.05 760.86 L 252.9 760.86 L 252.9 762 L 257.43 762 C 257.74 762 258 761.74 258 761.43 L 258 756.9 L 256.86 756.9 Z M 232.08 755.68 L 227.14 760.14 L 227.14 756.9 L 226 756.9 L 226 761.43 C 226 761.74 226.26 762 226.57 762 L 231.67 762 L 231.67 760.86 L 228.06 760.86 L 232.85 756.53 Z M 257.43 730 L 252.33 730 L 252.33 731.14 L 256.14 731.14 L 251.68 736.08 L 252.53 736.85 L 256.86 732.06 L 256.86 735.67 L 258 735.67 L 258 730.57 C 258 730.26 257.74 730 257.43 730 Z M 227.14 735.67 L 226 735.67 L 226 730.57 C 226 730.26 226.26 730 226.57 730 L 231.67 730 L 231.67 731.14 L 227.95 731.14 L 232.87 736.06 L 232.06 736.87 L 227.14 731.95 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 773px; margin-left: 242px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">EFS</div></div></div></foreignObject><text x="242" y="785" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">EFS</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-34"><g><path d="M 119 726 L 159 726 L 159 766 L 119 766 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 141.81 733.89 L 140.15 733.89 L 140.15 732.77 L 141.81 732.77 L 141.81 731.1 L 142.92 731.1 L 142.92 732.77 L 144.58 732.77 L 144.58 733.89 L 142.92 733.89 L 142.92 735.56 L 141.81 735.56 Z M 148.45 740.01 L 146.79 740.01 L 146.79 738.9 L 148.45 738.9 L 148.45 737.23 L 149.55 737.23 L 149.55 738.9 L 151.21 738.9 L 151.21 740.01 L 149.55 740.01 L 149.55 741.68 L 148.45 741.68 Z M 145.57 757.6 C 144.51 754.92 142.07 752.47 139.4 751.4 C 142.07 750.34 144.51 747.89 145.57 745.2 C 146.62 747.89 149.06 750.34 151.73 751.4 C 149.06 752.47 146.62 754.92 145.57 757.6 Z M 154.45 750.85 C 150.56 750.85 146.12 746.38 146.12 742.47 C 146.12 742.16 145.87 741.91 145.57 741.91 C 145.26 741.91 145.01 742.16 145.01 742.47 C 145.01 746.38 140.58 750.85 136.68 750.85 C 136.38 750.85 136.13 751.1 136.13 751.4 C 136.13 751.71 136.38 751.96 136.68 751.96 C 140.58 751.96 145.01 756.42 145.01 760.34 C 145.01 760.65 145.26 760.9 145.57 760.9 C 145.87 760.9 146.12 760.65 146.12 760.34 C 146.12 756.42 150.56 751.96 154.45 751.96 C 154.75 751.96 155 751.71 155 751.4 C 155 751.1 154.75 750.85 154.45 750.85 Z M 124.11 739.88 C 125.72 741.06 128.85 741.68 131.85 741.68 C 134.85 741.68 137.99 741.06 139.6 739.88 L 139.6 745.21 C 138.8 746.28 135.87 747.33 131.96 747.33 C 127.47 747.33 124.11 745.91 124.11 744.65 Z M 131.85 735.56 C 136.65 735.56 139.6 737.02 139.6 738.06 C 139.6 739.11 136.65 740.57 131.85 740.57 C 127.05 740.57 124.11 739.11 124.11 738.06 C 124.11 737.02 127.05 735.56 131.85 735.56 Z M 139.6 756.45 C 139.6 757.73 136.28 759.17 131.85 759.17 C 127.42 759.17 124.11 757.73 124.11 756.45 L 124.11 752.89 C 125.74 754.14 128.93 754.8 131.99 754.8 C 134.12 754.8 136.18 754.49 137.79 753.94 L 137.44 752.89 C 135.94 753.4 134.01 753.68 131.99 753.68 C 127.48 753.68 124.11 752.27 124.11 751.01 L 124.11 746.54 C 125.73 747.78 128.91 748.44 131.96 748.44 C 135.23 748.44 138.03 747.76 139.6 746.69 L 139.6 748.36 L 140.7 748.36 L 140.7 738.06 C 140.7 735.71 136.14 734.44 131.85 734.44 C 127.73 734.44 123.38 735.61 123.03 737.78 L 123 737.78 L 123 756.45 C 123 758.94 127.56 760.28 131.85 760.28 C 136.14 760.28 140.7 758.94 140.7 756.45 L 140.7 754.48 L 139.6 754.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 773px; margin-left: 139px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Aurora</div></div></div></foreignObject><text x="139" y="785" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Aurora</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-39"><g><path d="M 326 726 L 366 726 L 366 766 L 326 766 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/><path d="M 336.47 739.53 L 330.59 739.53 C 330.26 739.53 330 739.8 330 740.12 L 330 761.29 C 330 761.61 330.26 761.88 330.59 761.88 L 336.47 761.88 C 336.79 761.88 337.06 761.61 337.06 761.29 L 337.06 740.12 C 337.06 739.8 336.79 739.53 336.47 739.53 Z M 331.18 760.7 L 331.18 740.71 L 335.88 740.71 L 335.88 760.7 Z M 339.41 743.65 L 338.23 743.65 L 338.23 736 C 338.23 735.68 338.5 735.42 338.82 735.42 L 344.7 735.42 C 345.03 735.42 345.29 735.68 345.29 736 L 345.29 738.36 L 344.11 738.36 L 344.11 736.59 L 339.41 736.59 Z M 344.11 757.17 L 345.29 757.17 L 345.29 761.29 C 345.29 761.61 345.03 761.88 344.7 761.88 L 338.82 761.88 C 338.5 761.88 338.23 761.61 338.23 761.29 L 338.23 752.47 L 339.41 752.47 L 339.41 760.7 L 344.11 760.7 Z M 347.64 737.77 L 346.46 737.77 L 346.46 730.71 C 346.46 730.39 346.73 730.12 347.05 730.12 L 352.93 730.12 C 353.26 730.12 353.52 730.39 353.52 730.71 L 353.52 738.94 L 352.34 738.94 L 352.34 731.3 L 347.64 731.3 Z M 352.34 758.35 L 353.52 758.35 L 353.52 761.29 C 353.52 761.61 353.26 761.88 352.93 761.88 L 347.05 761.88 C 346.73 761.88 346.46 761.61 346.46 761.29 L 346.46 757.76 L 347.64 757.76 L 347.64 760.7 L 352.34 760.7 Z M 361.75 734.24 L 361.75 754.23 L 360.58 754.23 L 360.58 734.83 L 355.87 734.83 L 355.87 741.3 L 354.7 741.3 L 354.7 734.24 C 354.7 733.92 354.96 733.65 355.28 733.65 L 361.17 733.65 C 361.49 733.65 361.75 733.92 361.75 734.24 Z M 355.91 751.61 C 356.45 750.5 356.76 749.26 356.76 747.95 C 356.76 743.33 353 739.57 348.38 739.57 C 343.76 739.57 340 743.33 340 747.95 C 340 752.57 343.76 756.33 348.38 756.33 C 350.09 756.33 351.67 755.82 353 754.94 L 358.06 759.5 C 358.48 759.88 359.01 760.07 359.54 760.07 C 360.14 760.07 360.75 759.82 361.19 759.34 C 362 758.43 361.93 757.03 361.02 756.21 Z M 341.18 747.95 C 341.18 743.98 344.41 740.75 348.38 740.75 C 352.35 740.75 355.58 743.98 355.58 747.95 C 355.58 751.92 352.35 755.16 348.38 755.16 C 344.41 755.16 341.18 751.92 341.18 747.95 Z M 360.31 758.55 C 359.93 758.98 359.27 759.01 358.84 758.63 L 353.94 754.21 C 354.46 753.74 354.92 753.23 355.31 752.65 L 360.23 757.08 C 360.66 757.47 360.69 758.13 360.31 758.55 Z M 348.38 742.07 C 345.14 742.07 342.5 744.71 342.5 747.95 C 342.5 751.2 345.14 753.84 348.38 753.84 C 351.63 753.84 354.27 751.2 354.27 747.95 C 354.27 744.71 351.63 742.07 348.38 742.07 Z M 348.38 752.66 C 345.78 752.66 343.67 750.55 343.67 747.95 C 343.67 745.36 345.78 743.24 348.38 743.24 C 350.98 743.24 353.09 745.36 353.09 747.95 C 353.09 750.55 350.98 752.66 348.38 752.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 773px; margin-left: 346px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">OpenSearch</div></div></div></foreignObject><text x="346" y="785" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">OpenSe...</text></switch></g></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-40"><g><path d="M 1039 516 L 1079 516 L 1079 556 L 1039 556 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 1070.94 537.65 L 1071.16 536.11 C 1073.18 537.32 1073.21 537.82 1073.21 537.83 C 1073.2 537.84 1072.86 538.13 1070.94 537.65 Z M 1069.83 537.34 C 1066.33 536.29 1061.46 534.05 1059.49 533.12 C 1059.49 533.11 1059.49 533.11 1059.49 533.1 C 1059.49 532.34 1058.88 531.72 1058.12 531.72 C 1057.36 531.72 1056.75 532.34 1056.75 533.1 C 1056.75 533.85 1057.36 534.47 1058.12 534.47 C 1058.45 534.47 1058.75 534.35 1058.99 534.15 C 1061.31 535.25 1066.14 537.45 1069.67 538.49 L 1068.27 548.32 C 1068.27 548.35 1068.27 548.37 1068.27 548.4 C 1068.27 549.27 1064.43 550.86 1058.17 550.86 C 1051.84 550.86 1047.97 549.27 1047.97 548.4 C 1047.97 548.37 1047.97 548.35 1047.97 548.32 L 1045.05 527.06 C 1047.57 528.8 1052.99 529.71 1058.18 529.71 C 1063.35 529.71 1068.76 528.8 1071.29 527.07 Z M 1044.75 524.84 C 1044.79 524.09 1049.11 521.14 1058.18 521.14 C 1067.24 521.14 1071.56 524.09 1071.6 524.84 L 1071.6 525.1 C 1071.11 526.79 1065.51 528.57 1058.18 528.57 C 1050.83 528.57 1045.23 526.78 1044.75 525.09 Z M 1072.75 524.86 C 1072.75 522.88 1067.07 520 1058.18 520 C 1049.28 520 1043.6 522.88 1043.6 524.86 L 1043.66 525.29 L 1046.83 548.44 C 1046.9 551.03 1053.81 552 1058.17 552 C 1063.59 552 1069.34 550.76 1069.41 548.45 L 1070.78 538.79 C 1071.54 538.97 1072.17 539.07 1072.67 539.07 C 1073.35 539.07 1073.8 538.9 1074.08 538.57 C 1074.31 538.3 1074.4 537.97 1074.33 537.62 C 1074.18 536.83 1073.24 535.98 1071.33 534.89 L 1072.69 525.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 536px; margin-left: 1081px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3 Bucket<br />(Log Bucket)</div></div></div></foreignObject><text x="1081" y="540" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">S3 Buck...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-41"><g><rect x="619" y="716" width="240" height="120" fill="none" stroke="#0000ff" stroke-dasharray="3 3" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-42"><g><rect x="619" y="556" width="240" height="120" fill="none" stroke="#0000ff" stroke-dasharray="3 3" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-43"><g><rect x="619" y="396" width="240" height="120" fill="none" stroke="#0000ff" stroke-dasharray="3 3" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-44"><g><path d="M 719 436 L 759 436 L 759 476 L 719 476 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 750.94 457.65 L 751.16 456.11 C 753.18 457.32 753.21 457.82 753.21 457.83 C 753.2 457.84 752.86 458.13 750.94 457.65 Z M 749.83 457.34 C 746.33 456.29 741.46 454.05 739.49 453.12 C 739.49 453.11 739.49 453.11 739.49 453.1 C 739.49 452.34 738.88 451.72 738.12 451.72 C 737.36 451.72 736.75 452.34 736.75 453.1 C 736.75 453.85 737.36 454.47 738.12 454.47 C 738.45 454.47 738.75 454.35 738.99 454.15 C 741.31 455.25 746.14 457.45 749.67 458.49 L 748.27 468.32 C 748.27 468.35 748.27 468.37 748.27 468.4 C 748.27 469.27 744.43 470.86 738.17 470.86 C 731.84 470.86 727.97 469.27 727.97 468.4 C 727.97 468.37 727.97 468.35 727.97 468.32 L 725.05 447.06 C 727.57 448.8 732.99 449.71 738.18 449.71 C 743.35 449.71 748.76 448.8 751.29 447.07 Z M 724.75 444.84 C 724.79 444.09 729.11 441.14 738.18 441.14 C 747.24 441.14 751.56 444.09 751.6 444.84 L 751.6 445.1 C 751.11 446.79 745.51 448.57 738.18 448.57 C 730.83 448.57 725.23 446.78 724.75 445.09 Z M 752.75 444.86 C 752.75 442.88 747.07 440 738.18 440 C 729.28 440 723.6 442.88 723.6 444.86 L 723.66 445.29 L 726.83 468.44 C 726.9 471.03 733.81 472 738.17 472 C 743.59 472 749.34 470.76 749.41 468.45 L 750.78 458.79 C 751.54 458.97 752.17 459.07 752.67 459.07 C 753.35 459.07 753.8 458.9 754.08 458.57 C 754.31 458.3 754.4 457.97 754.33 457.62 C 754.18 456.83 753.24 455.98 751.33 454.89 L 752.69 445.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 483px; margin-left: 739px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3 Bucket<br />(SourceBucket)</div></div></div></foreignObject><text x="739" y="495" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3 Buc...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-45"><g><path d="M 639 436 L 679 436 L 679 476 L 639 476 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 672.99 446.08 L 669.71 443.08 L 666.43 446.08 Z M 668.46 442.74 L 661.8 442.75 L 665.35 445.58 Z M 665.45 452.42 L 660.22 454.35 L 670.29 454.35 Z M 657.76 465.37 L 672.53 465.37 L 672.53 455.45 L 657.76 455.45 Z M 664.22 446.08 L 660.6 443.2 L 656.99 446.08 Z M 655.84 445.6 L 659.39 442.76 L 652.32 442.77 Z M 654.69 446.08 L 651.07 443.18 L 647.45 446.08 L 652.84 446.08 Z M 652.3 447.18 L 647.87 447.18 L 652.3 451.57 Z M 652.3 453.29 L 647.1 458.02 L 652.3 463.1 Z M 652.3 464.89 L 646.83 470.24 L 646.83 470.32 L 652.3 470.32 Z M 646.83 459.29 L 646.83 468.7 L 651.64 464 Z M 646.83 456.78 L 651.61 452.43 L 646.83 447.7 Z M 646.28 445.61 L 649.82 442.77 L 646.28 442.78 Z M 645.19 441.68 L 644.09 441.68 L 644.09 447.18 L 645.19 447.18 L 645.19 446.63 L 645.19 442.23 Z M 674.92 446.83 C 674.84 447.04 674.63 447.18 674.41 447.18 L 665.97 447.18 L 665.97 451.59 L 673.28 454.39 L 673.28 454.39 C 673.48 454.47 673.62 454.67 673.62 454.9 L 673.62 465.92 C 673.62 466.22 673.38 466.47 673.08 466.47 L 657.22 466.47 C 656.92 466.47 656.67 466.22 656.67 465.92 L 656.67 454.9 C 656.67 454.66 656.82 454.46 657.03 454.39 L 657.03 454.38 L 664.87 451.59 L 664.87 447.18 L 653.39 447.18 L 653.39 470.88 C 653.39 471.18 653.15 471.43 652.84 471.43 L 646.28 471.43 C 645.98 471.43 645.73 471.18 645.73 470.88 L 645.73 448.29 L 643.55 448.29 C 643.25 448.29 643 448.04 643 447.74 L 643 441.12 C 643 440.82 643.25 440.57 643.55 440.57 L 645.73 440.57 C 646.04 440.57 646.28 440.82 646.28 441.12 L 646.28 441.67 L 669.79 441.68 L 674.78 446.23 C 674.94 446.38 675 446.62 674.92 446.83 Z M 663.57 464.43 L 666.75 457.15 L 665.75 456.7 L 662.57 463.99 Z M 666.56 462 L 667.27 462.84 L 669.63 460.82 C 669.75 460.73 669.82 460.58 669.82 460.43 C 669.83 460.28 669.77 460.13 669.67 460.02 L 667.69 458 L 666.92 458.78 L 668.47 460.37 Z M 659.46 460.4 C 659.34 460.29 659.27 460.13 659.28 459.96 C 659.29 459.8 659.37 459.65 659.5 459.55 L 662.19 457.55 L 662.84 458.43 L 660.69 460.04 L 662.49 461.7 L 661.75 462.52 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 483px; margin-left: 659px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">CodeBuild</div></div></div></foreignObject><text x="659" y="495" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">CodeBu...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-46"><g><path d="M 799 436 L 839 436 L 839 476 L 799 476 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 817.6 446.18 L 819.84 446.18 L 819.84 445.05 L 817.6 445.05 Z M 813.67 446.18 L 815.91 446.18 L 815.91 445.05 L 813.67 445.05 Z M 809.74 446.18 L 811.98 446.18 L 811.98 445.05 L 809.74 445.05 Z M 833.15 462.09 C 832.98 462.49 832.62 462.79 832.19 462.93 L 832.19 459.76 C 832.53 459.88 832.83 460.1 833.04 460.41 C 833.36 460.9 833.4 461.51 833.15 462.09 Z M 804.85 462.09 C 804.6 461.51 804.64 460.9 804.96 460.41 C 805.17 460.1 805.47 459.88 805.81 459.76 L 805.81 462.93 C 805.38 462.79 805.02 462.49 804.85 462.09 Z M 833.98 459.79 C 833.56 459.16 832.92 458.74 832.19 458.59 L 832.19 457.59 C 832.19 455.63 830.68 454.04 828.82 454.04 L 818.16 454.04 L 818.16 455.16 L 828.82 455.16 C 830.06 455.16 831.07 456.25 831.07 457.59 L 831.07 465.08 C 831.07 466.42 830.06 467.51 828.82 467.51 L 809.18 467.51 C 807.94 467.51 806.93 466.42 806.93 465.08 L 806.93 457.59 C 806.93 456.25 807.94 455.16 809.18 455.16 L 811.98 455.16 L 811.98 454.04 L 809.18 454.04 C 807.32 454.04 805.81 455.63 805.81 457.59 L 805.81 458.59 C 805.08 458.74 804.44 459.16 804.02 459.79 C 803.49 460.6 803.41 461.6 803.82 462.54 C 804.17 463.35 804.92 463.92 805.81 464.09 L 805.81 465.08 C 805.81 467.04 807.32 468.63 809.18 468.63 L 815.91 468.63 L 815.91 472 L 817.04 472 L 817.04 468.63 L 820.4 468.63 L 820.4 472 L 821.53 472 L 821.53 468.63 L 828.82 468.63 C 830.68 468.63 832.19 467.04 832.19 465.08 L 832.19 464.09 C 833.08 463.92 833.83 463.35 834.18 462.54 C 834.59 461.6 834.51 460.6 833.98 459.79 Z M 814.23 461.33 C 814.23 462.26 813.47 463.02 812.54 463.02 C 811.62 463.02 810.86 462.26 810.86 461.33 C 810.86 460.4 811.62 459.65 812.54 459.65 C 813.47 459.65 814.23 460.4 814.23 461.33 Z M 809.74 461.33 C 809.74 462.88 811 464.14 812.54 464.14 C 814.09 464.14 815.35 462.88 815.35 461.33 C 815.35 459.79 814.09 458.53 812.54 458.53 C 811 458.53 809.74 459.79 809.74 461.33 Z M 823.77 461.33 C 823.77 460.4 824.53 459.65 825.46 459.65 C 826.38 459.65 827.14 460.4 827.14 461.33 C 827.14 462.26 826.38 463.02 825.46 463.02 C 824.53 463.02 823.77 462.26 823.77 461.33 Z M 828.26 461.33 C 828.26 459.79 827 458.53 825.46 458.53 C 823.91 458.53 822.65 459.79 822.65 461.33 C 822.65 462.88 823.91 464.14 825.46 464.14 C 827 464.14 828.26 462.88 828.26 461.33 Z M 807.49 441.6 C 807.49 441.34 807.71 441.12 807.97 441.12 L 822.09 441.12 L 822.09 450.17 C 822.09 450.44 821.86 450.67 821.59 450.67 L 815.91 450.67 C 815.6 450.67 815.35 450.92 815.35 451.23 L 815.35 454.14 L 812.87 450.89 C 812.76 450.75 812.6 450.67 812.42 450.67 L 807.97 450.67 C 807.71 450.67 807.49 450.45 807.49 450.19 Z M 807.97 451.79 L 812.15 451.79 L 815.47 456.14 C 815.57 456.28 815.74 456.36 815.91 456.36 C 815.97 456.36 816.03 456.35 816.09 456.33 C 816.32 456.26 816.47 456.04 816.47 455.8 L 816.47 451.79 L 821.59 451.79 C 822.48 451.79 823.21 451.06 823.21 450.17 L 823.21 440.86 C 823.21 440.38 822.83 440 822.35 440 L 807.97 440 C 807.09 440 806.37 440.72 806.37 441.6 L 806.37 450.19 C 806.37 451.07 807.09 451.79 807.97 451.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 483px; margin-left: 819px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ChatBot</div></div></div></foreignObject><text x="819" y="495" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ChatBot</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-47"><g><path d="M 665.5 596 L 705.5 596 L 705.5 636 L 665.5 636 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 699.49 606.08 L 696.21 603.08 L 692.93 606.08 Z M 694.96 602.74 L 688.3 602.75 L 691.85 605.58 Z M 691.95 612.42 L 686.72 614.35 L 696.79 614.35 Z M 684.26 625.37 L 699.03 625.37 L 699.03 615.45 L 684.26 615.45 Z M 690.72 606.08 L 687.1 603.2 L 683.49 606.08 Z M 682.34 605.6 L 685.89 602.76 L 678.82 602.77 Z M 681.19 606.08 L 677.57 603.18 L 673.95 606.08 L 679.34 606.08 Z M 678.8 607.18 L 674.37 607.18 L 678.8 611.57 Z M 678.8 613.29 L 673.6 618.02 L 678.8 623.1 Z M 678.8 624.89 L 673.33 630.24 L 673.33 630.32 L 678.8 630.32 Z M 673.33 619.29 L 673.33 628.7 L 678.14 624 Z M 673.33 616.78 L 678.11 612.43 L 673.33 607.7 Z M 672.78 605.61 L 676.32 602.77 L 672.78 602.78 Z M 671.69 601.68 L 670.59 601.68 L 670.59 607.18 L 671.69 607.18 L 671.69 606.63 L 671.69 602.23 Z M 701.42 606.83 C 701.34 607.04 701.13 607.18 700.91 607.18 L 692.47 607.18 L 692.47 611.59 L 699.78 614.39 L 699.78 614.39 C 699.98 614.47 700.12 614.67 700.12 614.9 L 700.12 625.92 C 700.12 626.22 699.88 626.47 699.58 626.47 L 683.72 626.47 C 683.42 626.47 683.17 626.22 683.17 625.92 L 683.17 614.9 C 683.17 614.66 683.32 614.46 683.53 614.39 L 683.53 614.38 L 691.37 611.59 L 691.37 607.18 L 679.89 607.18 L 679.89 630.88 C 679.89 631.18 679.65 631.43 679.34 631.43 L 672.78 631.43 C 672.48 631.43 672.23 631.18 672.23 630.88 L 672.23 608.29 L 670.05 608.29 C 669.75 608.29 669.5 608.04 669.5 607.74 L 669.5 601.12 C 669.5 600.82 669.75 600.57 670.05 600.57 L 672.23 600.57 C 672.54 600.57 672.78 600.82 672.78 601.12 L 672.78 601.67 L 696.29 601.68 L 701.28 606.23 C 701.44 606.38 701.5 606.62 701.42 606.83 Z M 690.07 624.43 L 693.25 617.15 L 692.25 616.7 L 689.07 623.99 Z M 693.06 622 L 693.77 622.84 L 696.13 620.82 C 696.25 620.73 696.32 620.58 696.32 620.43 C 696.33 620.28 696.27 620.13 696.17 620.02 L 694.19 618 L 693.42 618.78 L 694.97 620.37 Z M 685.96 620.4 C 685.84 620.29 685.77 620.13 685.78 619.96 C 685.79 619.8 685.87 619.65 686 619.55 L 688.69 617.55 L 689.34 618.43 L 687.19 620.04 L 688.99 621.7 L 688.25 622.52 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 643px; margin-left: 686px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">CodeBuild</div></div></div></foreignObject><text x="686" y="655" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">CodeBu...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-48"><g><path d="M 769 596 L 809 596 L 809 636 L 769 636 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 800.94 617.65 L 801.16 616.11 C 803.18 617.32 803.21 617.82 803.21 617.83 C 803.2 617.84 802.86 618.13 800.94 617.65 Z M 799.83 617.34 C 796.33 616.29 791.46 614.05 789.49 613.12 C 789.49 613.11 789.49 613.11 789.49 613.1 C 789.49 612.34 788.88 611.72 788.12 611.72 C 787.36 611.72 786.75 612.34 786.75 613.1 C 786.75 613.85 787.36 614.47 788.12 614.47 C 788.45 614.47 788.75 614.35 788.99 614.15 C 791.31 615.25 796.14 617.45 799.67 618.49 L 798.27 628.32 C 798.27 628.35 798.27 628.37 798.27 628.4 C 798.27 629.27 794.43 630.86 788.17 630.86 C 781.84 630.86 777.97 629.27 777.97 628.4 C 777.97 628.37 777.97 628.35 777.97 628.32 L 775.05 607.06 C 777.57 608.8 782.99 609.71 788.18 609.71 C 793.35 609.71 798.76 608.8 801.29 607.07 Z M 774.75 604.84 C 774.79 604.09 779.11 601.14 788.18 601.14 C 797.24 601.14 801.56 604.09 801.6 604.84 L 801.6 605.1 C 801.11 606.79 795.51 608.57 788.18 608.57 C 780.83 608.57 775.23 606.78 774.75 605.09 Z M 802.75 604.86 C 802.75 602.88 797.07 600 788.18 600 C 779.28 600 773.6 602.88 773.6 604.86 L 773.66 605.29 L 776.83 628.44 C 776.9 631.03 783.81 632 788.17 632 C 793.59 632 799.34 630.76 799.41 628.45 L 800.78 618.79 C 801.54 618.97 802.17 619.07 802.67 619.07 C 803.35 619.07 803.8 618.9 804.08 618.57 C 804.31 618.3 804.4 617.97 804.33 617.62 C 804.18 616.83 803.24 615.98 801.33 614.89 L 802.69 605.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 643px; margin-left: 789px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3 Bucket<br />(SourceBucket)</div></div></div></foreignObject><text x="789" y="655" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3 Buc...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-49"><g><path d="M 769 756 L 809 756 L 809 796 L 769 796 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/><path d="M 800.94 777.65 L 801.16 776.11 C 803.18 777.32 803.21 777.82 803.21 777.83 C 803.2 777.84 802.86 778.13 800.94 777.65 Z M 799.83 777.34 C 796.33 776.29 791.46 774.05 789.49 773.12 C 789.49 773.11 789.49 773.11 789.49 773.1 C 789.49 772.34 788.88 771.72 788.12 771.72 C 787.36 771.72 786.75 772.34 786.75 773.1 C 786.75 773.85 787.36 774.47 788.12 774.47 C 788.45 774.47 788.75 774.35 788.99 774.15 C 791.31 775.25 796.14 777.45 799.67 778.49 L 798.27 788.32 C 798.27 788.35 798.27 788.37 798.27 788.4 C 798.27 789.27 794.43 790.86 788.17 790.86 C 781.84 790.86 777.97 789.27 777.97 788.4 C 777.97 788.37 777.97 788.35 777.97 788.32 L 775.05 767.06 C 777.57 768.8 782.99 769.71 788.18 769.71 C 793.35 769.71 798.76 768.8 801.29 767.07 Z M 774.75 764.84 C 774.79 764.09 779.11 761.14 788.18 761.14 C 797.24 761.14 801.56 764.09 801.6 764.84 L 801.6 765.1 C 801.11 766.79 795.51 768.57 788.18 768.57 C 780.83 768.57 775.23 766.78 774.75 765.09 Z M 802.75 764.86 C 802.75 762.88 797.07 760 788.18 760 C 779.28 760 773.6 762.88 773.6 764.86 L 773.66 765.29 L 776.83 788.44 C 776.9 791.03 783.81 792 788.17 792 C 793.59 792 799.34 790.76 799.41 788.45 L 800.78 778.79 C 801.54 778.97 802.17 779.07 802.67 779.07 C 803.35 779.07 803.8 778.9 804.08 778.57 C 804.31 778.3 804.4 777.97 804.33 777.62 C 804.18 776.83 803.24 775.98 801.33 774.89 L 802.69 765.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 803px; margin-left: 789px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">S3 Bucket<br />(SourceBucket)</div></div></div></foreignObject><text x="789" y="815" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">S3 Buc...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-50"><g><path d="M 665.5 756 L 705.5 756 L 705.5 796 L 665.5 796 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 685.52 760 C 676.72 760 669.85 766.71 669.51 775.6 C 669.5 775.81 669.62 776 669.79 776.11 L 675.54 779.89 L 676.15 778.94 L 670.72 775.37 C 671 774.16 671.89 773.24 673.16 772.78 C 673.76 772.56 674.45 772.44 675.21 772.44 C 676.45 772.44 677.61 772.91 678.47 773.76 L 678.57 773.85 C 679.35 774.61 679.81 775.99 679.81 775.99 C 679.81 776.03 680.57 778.88 680.57 778.88 L 681.66 778.59 L 680.93 775.87 L 680.93 775.81 C 681.2 773.33 683.47 772.44 685.51 772.44 C 686.74 772.44 687.9 772.91 688.77 773.76 C 688.77 773.76 689.99 774.7 690.08 775.8 L 690.09 775.88 L 689.35 778.59 L 690.43 778.89 L 691.2 776.06 C 691.2 776.04 691.21 776.02 691.21 775.99 C 691.36 773.83 693.16 772.44 695.81 772.44 C 697.04 772.44 698.19 772.91 699.06 773.76 C 699.73 774.41 700.21 774.91 700.34 775.4 L 695.49 778.8 L 696.13 779.73 L 701.26 776.13 C 701.4 776.02 701.5 775.62 701.5 775.6 C 701.34 766.71 694.48 760.01 685.52 760 Z M 679.83 773.51 C 679.68 773.36 679.52 773.2 679.35 773.04 L 679.25 772.94 C 678.17 771.89 676.74 771.31 675.21 771.31 C 673.47 771.31 672.01 771.84 671 772.76 C 672.33 766.74 677.05 762.27 683.1 761.33 C 681.29 763.45 680.04 767.94 679.83 773.51 Z M 689.75 773.14 L 689.55 772.94 C 688.47 771.89 687.04 771.31 685.51 771.31 C 683.56 771.31 681.98 771.97 680.97 773.08 C 681.34 765.95 683.53 761.14 685.51 761.14 C 685.52 761.14 685.52 761.14 685.52 761.14 C 687.54 761.15 689.76 766.12 690.07 773.46 C 689.97 773.35 689.86 773.25 689.75 773.14 Z M 699.84 772.94 C 698.76 771.89 697.33 771.31 695.81 771.31 C 693.82 771.31 692.19 772.02 691.19 773.2 C 690.94 767.77 689.7 763.41 687.93 761.33 C 694.27 762.29 699.09 766.98 700.16 773.25 C 700.05 773.15 699.95 773.04 699.84 772.94 Z M 680.39 785.91 C 680.27 785.8 680.21 785.63 680.22 785.47 C 680.23 785.3 680.31 785.15 680.45 785.05 L 683.11 783.04 L 683.78 783.95 L 681.64 785.56 L 683.56 787.44 L 682.77 788.25 Z M 690.01 786.11 L 688.1 784.23 L 688.88 783.42 L 691.27 785.76 C 691.39 785.87 691.45 786.04 691.44 786.2 C 691.43 786.37 691.34 786.52 691.21 786.62 L 688.55 788.63 L 687.88 787.72 Z M 683.59 789.86 L 686.99 782.08 L 688.02 782.53 L 684.62 790.32 Z M 693.64 780.11 L 677.93 780.11 C 677.62 780.11 677.37 780.36 677.37 780.67 L 677.37 791.43 C 677.37 791.74 677.62 792 677.93 792 L 693.64 792 C 693.95 792 694.2 791.74 694.2 791.43 L 694.2 780.67 C 694.2 780.36 693.95 780.11 693.64 780.11 Z M 678.49 790.86 L 678.49 781.24 L 693.08 781.24 L 693.08 790.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 803px; margin-left: 686px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;"><span><font style="font-size: 13px;">CodeDeploy</font></span></div></div></div></foreignObject><text x="686" y="815" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">CodeDe...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-51"><g><rect x="619" y="236" width="240" height="120" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-52"><g/><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px"><text x="660.5" y="262.32">OpenID Connect</text></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-53"><g><rect x="619" y="236" width="40" height="40" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-54"><g><image x="621.11" y="236.01" width="34.77" height="39.07" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-55"><g/><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-56"><g><rect x="719" y="289.15" width="40" height="22.56" fill="none" stroke="none" pointer-events="all"/><path d="M 758.1 304.06 C 757.97 303.95 757.82 303.86 757.66 303.8 C 756.73 295.53 749.68 289.15 741.28 289.15 C 737.49 289.15 733.79 290.47 730.86 292.86 C 726.99 296 724.78 300.67 724.78 305.65 C 724.78 305.69 724.78 305.72 724.78 305.76 C 724.19 306.06 723.56 306.34 722.94 306.61 C 721.04 307.44 719.67 308.04 719.47 309.12 C 719.42 309.4 719.41 309.94 719.93 310.46 C 720.85 311.38 722.62 311.71 724.41 311.71 C 725.16 311.71 725.91 311.65 726.61 311.56 C 727.8 311.39 730.02 310.96 731.53 309.86 C 733.9 308.13 739.28 307.49 742.08 307.7 C 743.75 307.83 745.77 308.71 747.4 309.41 C 748.64 309.95 749.62 310.38 750.39 310.49 C 751.44 310.63 753.17 310.45 755 310.27 L 755.15 310.25 C 755.63 310.2 756.1 310.16 756.57 310.12 C 756.76 310.1 756.95 310.09 757.2 310.07 C 757.98 310.01 758.59 309.36 758.59 308.58 L 758.59 305.18 C 758.59 304.75 758.41 304.35 758.1 304.06 Z M 741.28 290.93 C 748.71 290.93 754.95 296.52 755.87 303.8 C 755.61 303.82 755.34 303.85 755.06 303.87 L 754.9 303.89 C 754.87 303.07 754.21 302.41 753.39 302.41 L 752.75 302.41 C 750.8 296.66 745.14 292.69 738.73 292.69 C 736.35 292.69 734.03 293.24 731.94 294.27 C 731.96 294.26 731.97 294.24 731.98 294.23 C 734.59 292.1 737.89 290.93 741.28 290.93 Z M 750.41 305.4 L 750.41 304.19 L 753.12 304.19 L 753.12 305.4 Z M 726.37 309.8 C 724.02 310.12 722.16 309.83 721.39 309.36 C 721.86 309.03 722.91 308.57 723.65 308.24 C 724.29 307.96 724.94 307.67 725.57 307.35 C 726.59 308.13 727.86 308.72 729.27 309.07 C 728.45 309.39 727.45 309.65 726.37 309.8 Z M 756.82 308.31 C 756.68 308.32 756.54 308.34 756.41 308.35 C 755.94 308.39 755.46 308.43 754.97 308.48 L 754.82 308.5 C 753.25 308.66 751.48 308.84 750.63 308.73 C 750.11 308.65 749.13 308.23 748.1 307.78 C 746.36 307.03 744.18 306.08 742.22 305.93 C 739.51 305.73 734.84 306.24 731.82 307.65 C 730.08 307.57 728.47 307.1 727.21 306.33 C 727.2 306.09 727.2 305.84 727.2 305.75 C 727.2 302.08 728.92 298.63 731.92 296.29 C 733.96 295.1 736.31 294.47 738.73 294.47 C 744.16 294.47 748.99 297.68 750.87 302.41 L 750.15 302.41 C 749.42 302.41 748.82 302.92 748.67 303.6 C 748.55 303.56 748.44 303.51 748.3 303.46 C 746.5 302.77 744.03 301.82 741.17 301.99 C 736.99 302.23 734.94 302.48 733.32 302.95 L 733.81 304.65 C 735.29 304.23 737.24 304 741.28 303.76 C 743.75 303.62 746.02 304.49 747.66 305.12 C 748.02 305.26 748.33 305.37 748.63 305.48 L 748.63 305.66 C 748.63 306.5 749.31 307.18 750.15 307.18 L 753.39 307.18 C 754.22 307.18 754.9 306.5 754.9 305.67 L 755.21 305.64 C 755.74 305.59 756.22 305.55 756.63 305.51 L 756.82 305.49 Z" fill="#bf0816" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 319px; margin-left: 739px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Role</div></div></div></foreignObject><text x="739" y="331" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Role</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-57"><g><rect x="719" y="279.64" width="40" height="43.64" fill="none" stroke="none" pointer-events="all"/></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-58"><g><path d="M 619 396 L 639 396 L 639 416 L 619 416 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 635.41 398 L 622.59 398 C 621.71 398 621 398.71 621 399.59 L 621 403.39 C 621 404.27 621.71 404.98 622.59 404.98 L 623.04 404.98 L 623.04 413.71 C 623.04 413.87 623.17 414 623.33 414 L 634.38 414 C 634.54 414 634.67 413.87 634.67 413.71 L 634.67 404.98 L 635.41 404.98 C 636.29 404.98 637 404.27 637 403.39 L 637 399.59 C 637 398.71 636.29 398 635.41 398 Z M 623.62 413.42 L 623.62 404.98 L 634.09 404.98 L 634.09 413.42 Z M 635.41 404.4 L 622.59 404.4 C 622.03 404.4 621.58 403.95 621.58 403.39 L 621.58 403.24 L 625.07 403.24 L 625.07 402.65 L 621.58 402.65 L 621.58 399.59 C 621.58 399.03 622.03 398.58 622.59 398.58 L 635.41 398.58 C 635.97 398.58 636.42 399.03 636.42 399.59 L 636.42 402.65 L 628.27 402.65 L 628.27 403.24 L 636.42 403.24 L 636.42 403.39 C 636.42 403.95 635.97 404.4 635.41 404.4 Z M 624.64 409.07 C 624.64 408.98 624.67 408.9 624.74 408.85 L 626.58 407.22 L 626.97 407.66 L 625.37 409.06 L 626.96 410.43 L 626.58 410.87 L 624.74 409.29 C 624.68 409.23 624.64 409.15 624.64 409.07 Z M 630.48 410.45 L 632.07 409.04 L 630.48 407.66 L 630.86 407.22 L 632.71 408.82 C 632.77 408.88 632.81 408.96 632.81 409.04 C 632.81 409.12 632.77 409.2 632.71 409.26 L 630.86 410.88 Z M 627.88 412.01 L 627.35 411.79 L 629.56 406.4 L 630.09 406.62 Z M 625.95 403.24 L 627.4 403.24 L 627.4 402.65 L 625.95 402.65 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 406px; margin-left: 641px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">InfraResource Pipeline</div></div></div></foreignObject><text x="641" y="410" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">Infr...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-59"><g><path d="M 619 556 L 639 556 L 639 576 L 619 576 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 635.41 558 L 622.59 558 C 621.71 558 621 558.71 621 559.59 L 621 563.39 C 621 564.27 621.71 564.98 622.59 564.98 L 623.04 564.98 L 623.04 573.71 C 623.04 573.87 623.17 574 623.33 574 L 634.38 574 C 634.54 574 634.67 573.87 634.67 573.71 L 634.67 564.98 L 635.41 564.98 C 636.29 564.98 637 564.27 637 563.39 L 637 559.59 C 637 558.71 636.29 558 635.41 558 Z M 623.62 573.42 L 623.62 564.98 L 634.09 564.98 L 634.09 573.42 Z M 635.41 564.4 L 622.59 564.4 C 622.03 564.4 621.58 563.95 621.58 563.39 L 621.58 563.24 L 625.07 563.24 L 625.07 562.65 L 621.58 562.65 L 621.58 559.59 C 621.58 559.03 622.03 558.58 622.59 558.58 L 635.41 558.58 C 635.97 558.58 636.42 559.03 636.42 559.59 L 636.42 562.65 L 628.27 562.65 L 628.27 563.24 L 636.42 563.24 L 636.42 563.39 C 636.42 563.95 635.97 564.4 635.41 564.4 Z M 624.64 569.07 C 624.64 568.98 624.67 568.9 624.74 568.85 L 626.58 567.22 L 626.97 567.66 L 625.37 569.06 L 626.96 570.43 L 626.58 570.87 L 624.74 569.29 C 624.68 569.23 624.64 569.15 624.64 569.07 Z M 630.48 570.45 L 632.07 569.04 L 630.48 567.66 L 630.86 567.22 L 632.71 568.82 C 632.77 568.88 632.81 568.96 632.81 569.04 C 632.81 569.12 632.77 569.2 632.71 569.26 L 630.86 570.88 Z M 627.88 572.01 L 627.35 571.79 L 629.56 566.4 L 630.09 566.62 Z M 625.95 563.24 L 627.4 563.24 L 627.4 562.65 L 625.95 562.65 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 566px; margin-left: 641px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">BlueGreen Pipeline</div></div></div></foreignObject><text x="641" y="570" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">Blue...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-60"><g><path d="M 619 716 L 639 716 L 639 736 L 619 736 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/><path d="M 635.41 718 L 622.59 718 C 621.71 718 621 718.71 621 719.59 L 621 723.39 C 621 724.27 621.71 724.98 622.59 724.98 L 623.04 724.98 L 623.04 733.71 C 623.04 733.87 623.17 734 623.33 734 L 634.38 734 C 634.54 734 634.67 733.87 634.67 733.71 L 634.67 724.98 L 635.41 724.98 C 636.29 724.98 637 724.27 637 723.39 L 637 719.59 C 637 718.71 636.29 718 635.41 718 Z M 623.62 733.42 L 623.62 724.98 L 634.09 724.98 L 634.09 733.42 Z M 635.41 724.4 L 622.59 724.4 C 622.03 724.4 621.58 723.95 621.58 723.39 L 621.58 723.24 L 625.07 723.24 L 625.07 722.65 L 621.58 722.65 L 621.58 719.59 C 621.58 719.03 622.03 718.58 622.59 718.58 L 635.41 718.58 C 635.97 718.58 636.42 719.03 636.42 719.59 L 636.42 722.65 L 628.27 722.65 L 628.27 723.24 L 636.42 723.24 L 636.42 723.39 C 636.42 723.95 635.97 724.4 635.41 724.4 Z M 624.64 729.07 C 624.64 728.98 624.67 728.9 624.74 728.85 L 626.58 727.22 L 626.97 727.66 L 625.37 729.06 L 626.96 730.43 L 626.58 730.87 L 624.74 729.29 C 624.68 729.23 624.64 729.15 624.64 729.07 Z M 630.48 730.45 L 632.07 729.04 L 630.48 727.66 L 630.86 727.22 L 632.71 728.82 C 632.77 728.88 632.81 728.96 632.81 729.04 C 632.81 729.12 632.77 729.2 632.71 729.26 L 630.86 730.88 Z M 627.88 732.01 L 627.35 731.79 L 629.56 726.4 L 630.09 726.62 Z M 625.95 723.24 L 627.4 723.24 L 627.4 722.65 L 625.95 722.65 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 726px; margin-left: 641px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Ecspresso Pipeline</div></div></div></foreignObject><text x="641" y="730" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">Ecsp...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-61"><g><path d="M 919 756 L 959 756 L 959 796 L 919 796 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 925.83 773.61 L 933.18 773.61 L 933.18 772.48 L 925.83 772.48 Z M 951.12 781.83 L 951.92 782.62 L 947.72 786.82 C 947.61 786.93 947.46 786.98 947.32 786.98 C 947.18 786.98 947.03 786.93 946.92 786.82 L 944.81 784.71 L 945.61 783.91 L 947.32 785.62 Z M 953.72 784.94 C 953.59 786 953.16 786.99 952.47 787.79 C 951.98 788.37 951.36 788.84 950.68 789.16 C 949.77 789.6 948.75 789.76 947.74 789.64 C 946.76 789.52 945.83 789.13 945.06 788.52 C 943.58 787.34 942.83 785.52 943.06 783.64 C 943.27 781.86 944.35 780.31 945.94 779.49 C 946.7 779.1 947.53 778.9 948.38 778.9 C 948.6 778.9 948.82 778.91 949.04 778.94 C 950.81 779.15 952.36 780.24 953.17 781.83 C 953.66 782.79 953.85 783.87 953.72 784.94 Z M 954.18 781.32 C 953.19 779.39 951.32 778.08 949.17 777.81 C 947.88 777.66 946.58 777.89 945.42 778.49 C 943.5 779.48 942.2 781.35 941.94 783.5 C 941.66 785.77 942.57 787.98 944.36 789.4 C 945.29 790.14 946.42 790.61 947.6 790.76 C 947.87 790.79 948.13 790.81 948.39 790.81 C 949.35 790.81 950.3 790.6 951.16 790.18 C 951.99 789.79 952.74 789.22 953.33 788.53 C 954.16 787.55 954.69 786.36 954.84 785.07 C 955 783.78 954.77 782.48 954.18 781.32 Z M 931.48 777 L 933.74 777 L 933.74 775.87 L 931.48 775.87 Z M 925.83 777 L 930.35 777 L 930.35 775.87 L 925.83 775.87 Z M 925.34 762.32 L 950.62 762.32 C 951.29 762.32 951.83 762.98 951.83 763.79 L 951.83 767.97 L 950.14 767.97 L 950.14 765.15 C 950.14 764.83 949.88 764.58 949.57 764.58 L 936.57 764.58 C 936.26 764.58 936 764.83 936 765.15 L 936 767.97 L 924.13 767.97 L 924.13 763.79 C 924.13 762.99 924.68 762.32 925.34 762.32 Z M 943.08 767.8 C 944.39 767.8 945.46 768.86 945.46 770.16 C 945.46 771.01 944.99 771.8 944.23 772.21 C 943.51 772.6 942.64 772.6 941.92 772.21 C 941.17 771.8 940.7 771.01 940.7 770.16 C 940.7 768.86 941.76 767.8 943.08 767.8 Z M 924.13 780.05 L 924.13 769.1 L 936 769.1 L 936 778.69 C 936 779.01 936.26 779.26 936.57 779.26 L 942.48 779.26 L 942.48 778.13 L 937.76 778.13 C 937.81 775.87 939.31 773.92 941.47 773.29 C 942.47 773.76 943.66 773.77 944.68 773.29 C 945.91 773.65 946.99 774.47 947.65 775.58 L 948.62 774.99 C 947.94 773.86 946.9 772.98 945.69 772.47 C 946.26 771.84 946.59 771.02 946.59 770.16 C 946.59 768.24 945.01 766.67 943.08 766.67 C 941.14 766.67 939.57 768.24 939.57 770.16 C 939.57 771.02 939.9 771.83 940.46 772.46 C 938.95 773.1 937.77 774.29 937.13 775.76 L 937.13 765.71 L 949.01 765.71 L 949.01 774.74 L 950.14 774.74 L 950.14 769.1 L 951.83 769.1 L 951.83 777.56 L 952.96 777.56 L 952.96 763.79 C 952.96 762.36 951.91 761.19 950.62 761.19 L 925.34 761.19 C 924.05 761.19 923 762.36 923 763.79 L 923 780.05 C 923 781.48 924.05 782.64 925.34 782.64 L 940.53 782.64 L 940.53 781.52 L 925.34 781.52 C 924.68 781.52 924.13 780.84 924.13 780.05 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 776px; margin-left: 961px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Cognito</div></div></div></foreignObject><text x="961" y="780" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">Cognito</text></switch></g></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-62"><g><path d="M 619 36 L 859 36 L 859 156 L 619 156 Z" fill="none" stroke="#147eba" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 627.06 39.47 C 626.47 39.47 626 39.95 626 40.53 C 626 40.99 626.3 41.39 626.71 41.53 L 626.71 56.82 L 624.95 56.82 L 624.95 57.57 L 629.16 57.57 L 629.16 56.82 L 627.46 56.82 L 627.46 48.75 L 638.84 48.75 L 636.19 45.59 L 638.83 42.36 L 627.46 42.36 L 627.46 41.51 C 627.85 41.35 628.12 40.97 628.12 40.53 C 628.12 39.95 627.64 39.47 627.06 39.47 Z M 627.06 40.22 C 627.23 40.22 627.37 40.35 627.37 40.53 C 627.37 40.71 627.23 40.84 627.06 40.84 C 626.88 40.84 626.75 40.71 626.75 40.53 C 626.75 40.35 626.88 40.22 627.06 40.22 Z M 627.46 43.11 L 637.25 43.11 L 635.22 45.6 L 637.23 48 L 627.46 48 Z M 619 61 L 619 36 L 644 36 L 644 61 Z" fill="#147eba" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 208px; height: 1px; padding-top: 43px; margin-left: 651px;"><div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Disaster Recovery Region</div></div></div></foreignObject><text x="651" y="55" fill="#147EBA" font-family="&quot;Helvetica&quot;" font-size="12px">Disaster Recovery Region</text></switch></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-63"><g><rect x="779" y="76" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 786.54 95.05 L 784.73 95.06 C 784.76 99.62 786.87 103.79 790.52 106.51 C 793.09 108.43 796.11 109.36 799.1 109.36 C 803.49 109.36 807.83 107.36 810.65 103.57 C 815.4 97.21 814.08 88.18 807.72 83.44 C 801.56 78.85 792.91 79.95 788.06 85.8 L 788.09 83.29 L 786.27 83.26 L 786.21 87.81 C 786.21 88.05 786.3 88.28 786.47 88.45 C 786.64 88.63 786.87 88.72 787.11 88.73 L 791.71 88.79 L 791.74 86.97 L 789.47 86.94 C 793.71 81.85 801.25 80.89 806.63 84.89 C 812.19 89.04 813.34 96.93 809.2 102.49 C 805.05 108.05 797.16 109.2 791.6 105.05 C 788.42 102.68 786.57 99.03 786.54 95.05 Z M 799 91.45 C 796.99 91.45 795.36 93.09 795.36 95.09 C 795.36 97.1 796.99 98.73 799 98.73 C 801.01 98.73 802.64 97.1 802.64 95.09 C 802.64 93.09 801.01 91.45 799 91.45 Z M 792.59 100.35 L 794.61 98.32 C 793.95 97.41 793.55 96.3 793.55 95.09 C 793.55 93.84 793.98 92.68 794.69 91.76 L 792.59 89.81 L 793.83 88.48 L 796.03 90.52 C 796.88 89.97 797.9 89.64 799 89.64 C 800.22 89.64 801.34 90.04 802.25 90.72 L 804.44 88.48 L 805.74 89.75 L 803.51 92.03 C 804.11 92.91 804.45 93.96 804.45 95.09 C 804.45 96.23 804.1 97.29 803.5 98.16 L 805.74 100.32 L 804.48 101.63 L 802.24 99.47 C 801.33 100.14 800.21 100.55 799 100.55 C 797.86 100.55 796.79 100.19 795.92 99.59 L 793.87 101.63 Z M 809 114.18 L 812.64 114.18 L 812.64 113.27 L 809 113.27 Z M 785.36 114.18 L 789 114.18 L 789 113.27 L 785.36 113.27 Z M 819 76.91 L 819 112.36 C 819 112.87 818.59 113.27 818.09 113.27 L 814.45 113.27 L 814.45 115.09 C 814.45 115.59 814.05 116 813.55 116 L 808.09 116 C 807.59 116 807.18 115.59 807.18 115.09 L 807.18 113.27 L 790.82 113.27 L 790.82 115.09 C 790.82 115.59 790.41 116 789.91 116 L 784.45 116 C 783.95 116 783.55 115.59 783.55 115.09 L 783.55 113.27 L 779.91 113.27 C 779.41 113.27 779 112.87 779 112.36 L 779 104.18 L 780.82 104.18 L 780.82 111.45 L 817.18 111.45 L 817.18 77.82 L 780.82 77.82 L 780.82 84.18 L 779 84.18 L 779 76.91 C 779 76.41 779.41 76 779.91 76 L 818.09 76 C 818.59 76 819 76.41 819 76.91 Z M 779 98.73 L 780.82 98.73 L 780.82 89.64 L 779 89.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 123px; margin-left: 799px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Backup Vault</div></div></div></foreignObject><text x="799" y="135" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Backup...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-64"><g><path d="M 669 76 L 709 76 L 709 116 L 669 116 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 696.38 103.56 L 700.69 103.56 L 700.69 102.33 L 696.38 102.33 Z M 690.85 103.56 L 695.15 103.56 L 695.15 102.33 L 690.85 102.33 Z M 685.31 103.56 L 689.61 103.56 L 689.61 102.33 L 685.31 102.33 Z M 692.08 98.63 C 692.08 97.96 692.63 97.4 693.31 97.4 C 693.99 97.4 694.54 97.96 694.54 98.63 C 694.54 99.31 693.99 99.86 693.31 99.86 C 692.63 99.86 692.08 99.31 692.08 98.63 Z M 695.77 98.63 C 695.77 97.28 694.67 96.17 693.31 96.17 C 691.95 96.17 690.85 97.28 690.85 98.63 C 690.85 99.99 691.95 101.1 693.31 101.1 C 694.67 101.1 695.77 99.99 695.77 98.63 Z M 687.15 97.4 C 687.83 97.4 688.38 97.96 688.38 98.63 C 688.38 99.31 687.83 99.86 687.15 99.86 C 686.47 99.86 685.92 99.31 685.92 98.63 C 685.92 97.96 686.47 97.4 687.15 97.4 Z M 687.15 101.1 C 688.51 101.1 689.61 99.99 689.61 98.63 C 689.61 97.28 688.51 96.17 687.15 96.17 C 685.79 96.17 684.69 97.28 684.69 98.63 C 684.69 99.99 685.79 101.1 687.15 101.1 Z M 705 91.86 L 705 106.02 C 705 106.36 704.72 106.63 704.38 106.63 L 685.31 106.63 L 685.31 105.4 L 703.77 105.4 L 703.77 92.48 L 690.23 92.48 L 690.23 91.25 L 704.38 91.25 C 704.72 91.25 705 91.52 705 91.86 Z M 683.27 94.97 C 683.02 95.05 682.84 95.29 682.84 95.56 L 682.84 108.51 L 681.37 109.77 L 679.77 107.94 L 679.77 106.33 C 679.77 106.16 679.7 106.01 679.59 105.89 L 678.18 104.48 L 679.59 103.07 C 679.7 102.95 679.77 102.8 679.77 102.63 L 679.77 101.4 C 679.77 101.24 679.7 101.08 679.59 100.97 L 678.18 99.56 L 679.59 98.15 C 679.7 98.03 679.77 97.87 679.77 97.71 L 679.77 95.56 C 679.77 95.29 679.59 95.06 679.34 94.98 C 676.17 93.97 674.3 90.72 674.99 87.4 C 675.48 85.02 677.32 83.1 679.67 82.53 C 681.66 82.04 683.72 82.46 685.29 83.7 C 686.87 84.94 687.77 86.79 687.77 88.79 C 687.77 91.59 685.92 94.13 683.27 94.97 Z M 689 88.79 C 689 86.41 687.92 84.2 686.05 82.73 C 684.18 81.26 681.74 80.75 679.37 81.33 C 676.57 82.02 674.37 84.3 673.78 87.16 L 673.78 87.16 C 673 90.94 675.03 94.66 678.54 96 L 678.54 97.46 L 676.87 99.12 C 676.63 99.36 676.63 99.75 676.87 99.99 L 678.54 101.66 L 678.54 102.38 L 676.87 104.05 C 676.63 104.29 676.63 104.68 676.87 104.92 L 678.54 106.58 L 678.54 108.17 C 678.54 108.32 678.59 108.47 678.69 108.58 L 680.84 111.04 C 680.96 111.18 681.13 111.25 681.31 111.25 C 681.45 111.25 681.59 111.2 681.71 111.1 L 683.86 109.26 C 684 109.14 684.08 108.97 684.08 108.79 L 684.08 95.99 C 687 94.87 689 91.97 689 88.79 Z M 681.31 90.25 C 680.46 90.25 679.77 89.56 679.77 88.71 C 679.77 87.86 680.46 87.17 681.31 87.17 C 682.15 87.17 682.84 87.86 682.84 88.71 C 682.84 89.56 682.15 90.25 681.31 90.25 Z M 681.31 85.94 C 679.78 85.94 678.54 87.18 678.54 88.71 C 678.54 90.23 679.78 91.48 681.31 91.48 C 682.83 91.48 684.08 90.23 684.08 88.71 C 684.08 87.18 682.83 85.94 681.31 85.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 123px; margin-left: 689px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">KMS Key</div></div></div></foreignObject><text x="689" y="135" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">KMS Key</text></switch></g></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-65"><g><rect x="539" y="516" width="40" height="40" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/><image x="538.5" y="515.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC" preserveAspectRatio="none"/></g><g><g fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px"><text x="558.5" y="573.5">VPC Endpoint</text></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-66"><g><path d="M 279 76 L 319 76 L 319 116 L 279 116 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/><path d="M 307.64 102.93 C 307.64 101.98 306.87 101.21 305.93 101.21 C 304.98 101.21 304.21 101.98 304.21 102.93 C 304.21 103.87 304.98 104.64 305.93 104.64 C 306.87 104.64 307.64 103.87 307.64 102.93 Z M 308.78 102.93 C 308.78 104.5 307.5 105.78 305.93 105.78 C 304.35 105.78 303.07 104.5 303.07 102.93 C 303.07 101.35 304.35 100.07 305.93 100.07 C 307.5 100.07 308.78 101.35 308.78 102.93 Z M 293.51 94.68 C 293.51 93.74 292.74 92.97 291.8 92.97 C 290.85 92.97 290.08 93.74 290.08 94.68 C 290.08 95.63 290.85 96.4 291.8 96.4 C 292.74 96.4 293.51 95.63 293.51 94.68 Z M 294.65 94.68 C 294.65 96.26 293.37 97.54 291.8 97.54 C 290.22 97.54 288.94 96.26 288.94 94.68 C 288.94 93.11 290.22 91.82 291.8 91.82 C 293.37 91.82 294.65 93.11 294.65 94.68 Z M 298.85 85.72 C 298.85 86.66 299.61 87.43 300.56 87.43 C 301.51 87.43 302.27 86.66 302.27 85.72 C 302.27 84.77 301.51 84 300.56 84 C 299.61 84 298.85 84.77 298.85 85.72 Z M 297.7 85.72 C 297.7 84.14 298.99 82.86 300.56 82.86 C 302.14 82.86 303.42 84.14 303.42 85.72 C 303.42 87.29 302.14 88.58 300.56 88.58 C 298.99 88.58 297.7 87.29 297.7 85.72 Z M 313.86 96 C 313.86 90.7 311.02 85.8 306.44 83.15 C 305.61 83.31 304.82 83.54 303.83 83.9 L 303.44 82.82 C 303.96 82.64 304.42 82.49 304.87 82.36 C 303.03 81.56 301.03 81.14 299 81.14 C 298.03 81.14 297.09 81.24 296.16 81.42 C 296.83 81.82 297.43 82.21 298 82.65 L 297.31 83.56 C 296.5 82.94 295.65 82.42 294.54 81.83 C 288.93 83.6 284.89 88.55 284.25 94.36 C 285.42 94.12 286.55 93.99 287.81 93.96 L 287.84 95.1 C 286.52 95.13 285.39 95.27 284.16 95.55 C 284.15 95.7 284.14 95.85 284.14 96 C 284.14 100.95 286.59 105.51 290.62 108.26 C 289.9 106.12 289.54 104.11 289.54 102.14 C 289.54 101.02 289.74 100.1 289.94 99.12 C 289.99 98.9 290.04 98.67 290.08 98.43 L 291.2 98.65 C 291.16 98.89 291.11 99.13 291.06 99.36 C 290.86 100.31 290.69 101.14 290.69 102.14 C 290.69 104.37 291.18 106.68 292.17 109.19 C 294.3 110.29 296.59 110.86 299 110.86 C 300.57 110.86 302.11 110.61 303.58 110.12 C 304.15 108.99 304.58 107.92 304.94 106.69 L 306.03 107 C 305.77 107.9 305.48 108.72 305.12 109.53 C 306.04 109.11 306.91 108.6 307.73 108 C 307.54 107.52 307.32 107.04 307.09 106.57 L 308.11 106.06 C 308.31 106.46 308.49 106.86 308.67 107.27 C 311.97 104.44 313.86 100.38 313.86 96 Z M 315 96 C 315 100.99 312.73 105.6 308.78 108.66 C 307.81 109.42 306.74 110.05 305.62 110.56 C 305.15 110.77 304.66 110.97 304.16 111.14 C 302.52 111.71 300.78 112 299 112 C 296.37 112 293.76 111.35 291.45 110.11 C 286.24 107.32 283 101.91 283 96 C 283 95.61 283.01 95.31 283.03 95.03 C 283.42 88.36 288 82.58 294.43 80.67 C 295.89 80.22 297.43 80 299 80 C 301.75 80 304.45 80.71 306.82 82.05 C 311.86 84.87 315 90.22 315 96 Z M 297.47 87.69 L 296.71 86.83 C 295.43 87.95 294.44 89.14 293.27 90.93 L 294.23 91.55 C 295.33 89.85 296.27 88.74 297.47 87.69 Z M 295.73 95.1 L 295.36 96.18 C 297.98 97.08 300.27 98.52 302.56 100.71 L 303.35 99.88 C 300.94 97.58 298.51 96.06 295.73 95.1 Z M 303.37 88.38 C 305.51 91.65 306.72 95.24 306.96 99.05 L 305.82 99.12 C 305.59 95.51 304.45 92.11 302.41 89.01 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 123px; margin-left: 299px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">CloudFront</div></div></div></foreignObject><text x="299" y="135" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">CloudF...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-67"><g><path d="M 439 96 L 325.37 96" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 320.12 96 L 327.12 92.5 L 325.37 96 L 327.12 99.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-68"><g><path d="M 439 76 L 479 76 L 479 116 L 439 116 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 444.37 96.57 L 443 96.57 L 443 95.43 L 444.37 95.43 C 444.49 92.54 445.42 89.79 447.12 87.44 L 448.05 88.11 C 446.49 90.26 445.63 92.78 445.51 95.43 L 447 95.43 L 447 96.57 L 445.51 96.57 C 445.62 99.24 446.48 101.77 448.05 103.93 L 447.12 104.6 C 445.41 102.24 444.48 99.48 444.37 96.57 Z M 467.58 107.9 C 465.23 109.6 462.47 110.54 459.57 110.65 L 459.57 112 L 458.43 112 L 458.43 110.65 C 455.53 110.54 452.77 109.6 450.42 107.9 L 451.09 106.98 C 453.25 108.54 455.77 109.4 458.43 109.51 L 458.43 108 L 459.57 108 L 459.57 109.51 C 462.23 109.4 464.75 108.53 466.91 106.98 Z M 450.42 84.14 C 452.77 82.44 455.53 81.5 458.43 81.39 L 458.43 80 L 459.57 80 L 459.57 81.39 C 462.47 81.5 465.23 82.44 467.58 84.14 L 466.91 85.07 C 464.75 83.51 462.23 82.64 459.57 82.53 L 459.57 84 L 458.43 84 L 458.43 82.53 C 455.77 82.64 453.25 83.51 451.09 85.07 Z M 475 95.43 L 475 96.57 L 473.63 96.57 C 473.52 99.48 472.59 102.24 470.88 104.6 L 469.95 103.93 C 471.52 101.77 472.38 99.24 472.49 96.57 L 471 96.57 L 471 95.43 L 472.49 95.43 C 472.37 92.78 471.51 90.26 469.95 88.11 L 470.88 87.44 C 472.58 89.79 473.51 92.54 473.63 95.43 Z M 467.56 86.66 L 472.04 82.18 L 472.84 82.99 L 468.36 87.47 Z M 450.44 105.39 L 445.96 109.87 L 445.16 109.06 L 449.64 104.58 Z M 451.72 89.52 L 443.24 81.04 L 444.04 80.24 L 452.52 88.72 Z M 466.26 102.45 L 474.76 110.96 L 473.96 111.76 L 465.45 103.26 Z M 454.24 96.42 C 454.28 96.34 454.33 96.27 454.37 96.19 C 455.33 94.68 455.06 92.62 454.75 91.38 C 455.58 91.92 456.32 93.04 456.57 93.51 C 456.68 93.71 456.89 93.83 457.12 93.81 C 457.35 93.79 457.54 93.64 457.62 93.43 C 458.47 91.01 458.03 89.19 457.43 88.01 C 458.16 88.44 458.73 89.05 459.1 89.82 C 459.91 91.51 459.73 93.81 458.63 95.83 C 457.13 98.59 457.43 101.49 457.77 103.05 C 456.91 102.68 456.15 102.26 455.49 101.8 C 453.76 100.6 453.21 98.23 454.24 96.42 Z M 461.91 96.23 C 461.87 96.46 461.98 96.69 462.19 96.81 C 462.39 96.93 462.65 96.91 462.83 96.76 C 462.88 96.73 463.82 95.95 464.27 94.08 C 464.82 94.88 465.39 96.48 464.63 99.43 C 463.88 102.35 460.3 103.17 459.03 103.37 C 458.72 102.27 458.1 99.2 459.63 96.37 C 460.72 94.37 461.01 92.1 460.47 90.23 C 461.5 91.44 462.37 93.36 461.91 96.23 Z M 453.24 95.86 C 451.93 98.18 452.63 101.2 454.83 102.74 C 455.83 103.44 457.05 104.04 458.43 104.54 C 458.5 104.56 458.56 104.57 458.63 104.57 C 458.64 104.57 458.65 104.57 458.66 104.56 L 458.66 104.57 C 458.9 104.55 464.61 104.05 465.73 99.72 C 467.18 94.14 464.29 92.48 464.17 92.41 C 464 92.32 463.8 92.32 463.63 92.41 C 463.46 92.5 463.34 92.66 463.32 92.85 C 463.28 93.24 463.22 93.58 463.14 93.89 C 462.75 89.51 459.27 87.72 458.57 87.41 C 457.85 86.85 456.98 86.47 455.96 86.29 C 455.71 86.25 455.45 86.38 455.35 86.62 C 455.24 86.85 455.3 87.13 455.5 87.3 C 455.58 87.37 457.36 88.91 456.85 91.85 C 456.19 90.98 455.13 89.93 453.89 89.93 C 453.7 89.93 453.53 90.02 453.42 90.18 C 453.31 90.34 453.29 90.53 453.36 90.71 C 453.37 90.74 454.54 93.81 453.41 95.58 C 453.35 95.67 453.29 95.77 453.24 95.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 123px; margin-left: 459px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">WAF<br />(Web ACL)</div></div></div></foreignObject><text x="459" y="135" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">WAF...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-69"><g><path d="M 0 0 L 1239 0 L 1239 916 L 0 916 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/><path d="M 6.09 7.18 C 6.01 7.18 5.93 7.19 5.85 7.19 C 5.5 7.19 5.15 7.23 4.81 7.32 C 4.53 7.39 4.25 7.49 3.98 7.62 C 3.9 7.65 3.84 7.7 3.79 7.76 C 3.75 7.83 3.74 7.91 3.74 7.99 L 3.74 8.32 C 3.74 8.46 3.79 8.53 3.89 8.53 L 3.99 8.53 L 4.22 8.44 C 4.45 8.35 4.69 8.27 4.94 8.21 C 5.17 8.16 5.41 8.13 5.65 8.13 C 6.04 8.09 6.43 8.2 6.73 8.44 C 6.97 8.74 7.09 9.12 7.05 9.5 L 7.05 9.99 C 6.79 9.93 6.54 9.88 6.29 9.84 C 6.05 9.81 5.81 9.79 5.57 9.79 C 4.98 9.76 4.4 9.94 3.94 10.31 C 3.54 10.65 3.32 11.15 3.34 11.68 C 3.31 12.15 3.49 12.62 3.82 12.96 C 4.18 13.29 4.66 13.46 5.15 13.44 C 5.91 13.45 6.63 13.11 7.11 12.51 C 7.18 12.66 7.24 12.79 7.31 12.91 C 7.38 13.02 7.46 13.12 7.55 13.21 C 7.6 13.27 7.67 13.31 7.75 13.31 C 7.81 13.31 7.87 13.29 7.92 13.25 L 8.34 12.97 C 8.41 12.93 8.46 12.86 8.47 12.77 C 8.47 12.72 8.45 12.67 8.42 12.62 C 8.34 12.47 8.26 12.31 8.21 12.14 C 8.15 11.95 8.12 11.75 8.13 11.55 L 8.14 9.37 C 8.2 8.77 8 8.18 7.59 7.74 C 7.17 7.39 6.64 7.19 6.09 7.18 Z M 19.89 7.19 C 19.78 7.19 19.68 7.19 19.57 7.2 C 19.29 7.2 19 7.24 18.73 7.31 C 18.47 7.38 18.23 7.5 18.02 7.66 C 17.82 7.81 17.66 7.99 17.54 8.21 C 17.42 8.43 17.35 8.67 17.36 8.92 C 17.36 9.27 17.48 9.61 17.69 9.89 C 17.97 10.22 18.34 10.46 18.76 10.56 L 19.72 10.87 C 19.97 10.93 20.2 11.05 20.39 11.22 C 20.51 11.35 20.58 11.51 20.57 11.69 C 20.58 11.94 20.45 12.18 20.23 12.31 C 19.93 12.48 19.6 12.56 19.26 12.54 C 18.99 12.54 18.72 12.51 18.46 12.45 C 18.22 12.4 17.98 12.32 17.75 12.22 L 17.59 12.15 C 17.54 12.14 17.5 12.14 17.46 12.15 C 17.36 12.15 17.31 12.22 17.31 12.36 L 17.31 12.69 C 17.31 12.76 17.32 12.82 17.35 12.89 C 17.4 12.97 17.47 13.03 17.56 13.07 C 17.8 13.19 18.06 13.28 18.32 13.34 C 18.66 13.41 19 13.45 19.35 13.45 L 19.33 13.46 C 19.66 13.45 19.98 13.4 20.29 13.3 C 20.55 13.22 20.8 13.09 21.01 12.92 C 21.21 12.77 21.38 12.57 21.49 12.34 C 21.61 12.1 21.67 11.83 21.66 11.56 C 21.67 11.23 21.56 10.9 21.36 10.63 C 21.09 10.32 20.73 10.09 20.33 9.99 L 19.39 9.69 C 19.13 9.61 18.88 9.49 18.67 9.32 C 18.54 9.2 18.47 9.03 18.47 8.85 C 18.46 8.61 18.58 8.38 18.79 8.25 C 19.06 8.11 19.36 8.05 19.67 8.06 C 20.11 8.06 20.55 8.14 20.96 8.32 C 21.04 8.37 21.12 8.4 21.21 8.41 C 21.31 8.41 21.36 8.34 21.36 8.19 L 21.36 7.88 C 21.37 7.8 21.35 7.72 21.31 7.66 C 21.25 7.59 21.18 7.54 21.11 7.49 L 20.83 7.38 L 20.45 7.27 L 20.01 7.2 C 19.97 7.2 19.93 7.19 19.89 7.19 Z M 16.02 7.36 C 15.94 7.35 15.86 7.38 15.79 7.42 C 15.72 7.5 15.68 7.59 15.66 7.69 L 14.51 12.14 L 13.47 7.71 C 13.45 7.61 13.41 7.52 13.34 7.44 C 13.26 7.39 13.17 7.37 13.07 7.38 L 12.54 7.38 C 12.44 7.37 12.35 7.39 12.27 7.44 C 12.2 7.51 12.15 7.61 12.14 7.71 L 11.09 12.14 L 9.97 7.7 C 9.95 7.6 9.91 7.51 9.84 7.44 C 9.76 7.39 9.67 7.36 9.58 7.37 L 8.92 7.37 C 8.81 7.37 8.76 7.43 8.76 7.54 C 8.77 7.63 8.79 7.72 8.82 7.81 L 10.38 12.95 C 10.4 13.05 10.45 13.14 10.52 13.21 C 10.6 13.26 10.69 13.29 10.78 13.28 L 11.36 13.26 C 11.46 13.27 11.55 13.25 11.63 13.19 C 11.7 13.12 11.74 13.03 11.76 12.93 L 12.79 8.64 L 13.82 12.93 C 13.83 13.03 13.88 13.12 13.95 13.19 C 14.03 13.25 14.12 13.27 14.21 13.26 L 14.79 13.26 C 14.88 13.27 14.97 13.25 15.04 13.2 C 15.11 13.13 15.16 13.03 15.18 12.94 L 16.79 7.79 C 16.84 7.72 16.84 7.63 16.84 7.63 C 16.84 7.59 16.84 7.56 16.84 7.52 C 16.84 7.48 16.82 7.43 16.79 7.4 C 16.76 7.37 16.72 7.35 16.67 7.36 L 16.05 7.36 C 16.04 7.36 16.03 7.36 16.02 7.36 Z M 5.65 10.62 C 5.7 10.62 5.75 10.62 5.8 10.62 L 6.43 10.62 C 6.64 10.64 6.85 10.67 7.06 10.71 L 7.07 11.01 C 7.07 11.21 7.05 11.4 7 11.59 C 6.96 11.75 6.88 11.9 6.77 12.01 C 6.61 12.21 6.39 12.36 6.14 12.44 C 5.91 12.52 5.67 12.56 5.43 12.56 C 5.18 12.6 4.93 12.53 4.73 12.37 C 4.55 12.18 4.46 11.92 4.49 11.66 C 4.47 11.36 4.59 11.08 4.81 10.89 C 5.06 10.72 5.35 10.62 5.65 10.62 Z M 21.04 14.72 C 20.34 14.73 19.51 14.89 18.89 15.33 C 18.69 15.46 18.72 15.63 18.94 15.63 C 19.64 15.54 21.21 15.35 21.5 15.71 C 21.78 16.06 21.19 17.54 20.94 18.21 C 20.86 18.41 21.04 18.49 21.21 18.34 C 22.39 17.36 22.72 15.3 22.46 15 C 22.32 14.85 21.74 14.71 21.04 14.72 Z M 2.65 15.1 C 2.5 15.12 2.42 15.3 2.58 15.44 C 5.29 17.89 8.82 19.23 12.48 19.21 C 15.37 19.22 18.2 18.36 20.59 16.74 C 20.95 16.47 20.64 16.07 20.26 16.23 C 17.87 17.24 15.3 17.76 12.71 17.77 C 9.23 17.78 5.82 16.87 2.81 15.14 C 2.75 15.11 2.7 15.1 2.65 15.1 Z M 0 0 L 25 0 L 25 25 L 0 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1207px; height: 1px; padding-top: 7px; margin-left: 32px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Cloud</div></div></div></foreignObject><text x="32" y="19" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Cloud</text></switch></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-70"><g><rect x="169" y="356" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 189 356 C 177.97 356 169 364.97 169 376 C 169 387.03 177.97 396 189 396 C 200.03 396 209 387.03 209 376 C 209 364.97 200.03 356 189 356 Z M 189 394.18 C 178.97 394.18 170.82 386.03 170.82 376 C 170.82 365.97 178.97 357.82 189 357.82 C 199.03 357.82 207.18 365.97 207.18 376 C 207.18 386.03 199.03 394.18 189 394.18 Z M 200.85 381.45 L 199.45 381.45 L 199.45 378.39 C 199.45 377.88 199.05 377.48 198.55 377.48 L 196.27 377.48 L 196.27 374.41 C 196.27 373.91 195.87 373.5 195.36 373.5 L 189.91 373.5 L 189.91 371.34 L 195.36 371.34 C 195.87 371.34 196.27 370.93 196.27 370.43 L 196.27 363.27 C 196.27 362.77 195.87 362.36 195.36 362.36 L 182.64 362.36 C 182.13 362.36 181.73 362.77 181.73 363.27 L 181.73 370.43 C 181.73 370.93 182.13 371.34 182.64 371.34 L 188.09 371.34 L 188.09 373.5 L 182.64 373.5 C 182.13 373.5 181.73 373.91 181.73 374.41 L 181.73 377.48 L 179.46 377.48 C 178.95 377.48 178.55 377.88 178.55 378.39 L 178.55 381.45 L 177.15 381.45 C 176.65 381.45 176.24 381.86 176.24 382.36 L 176.24 386.34 C 176.24 386.84 176.65 387.25 177.15 387.25 L 181.05 387.25 C 181.55 387.25 181.96 386.84 181.96 386.34 L 181.96 382.36 C 181.96 381.86 181.55 381.45 181.05 381.45 L 180.36 381.45 L 180.36 379.3 L 184.11 379.3 L 184.11 381.45 L 183.43 381.45 C 182.93 381.45 182.52 381.86 182.52 382.36 L 182.52 386.34 C 182.52 386.84 182.93 387.25 183.43 387.25 L 187.41 387.25 C 187.91 387.25 188.32 386.84 188.32 386.34 L 188.32 382.36 C 188.32 381.86 187.91 381.45 187.41 381.45 L 185.93 381.45 L 185.93 378.39 C 185.93 377.88 185.53 377.48 185.02 377.48 L 183.55 377.48 L 183.55 375.32 L 194.45 375.32 L 194.45 377.48 L 192.98 377.48 C 192.47 377.48 192.07 377.88 192.07 378.39 L 192.07 381.45 L 190.59 381.45 C 190.09 381.45 189.68 381.86 189.68 382.36 L 189.68 386.34 C 189.68 386.84 190.09 387.25 190.59 387.25 L 194.57 387.25 C 195.07 387.25 195.48 386.84 195.48 386.34 L 195.48 382.36 C 195.48 381.86 195.07 381.45 194.57 381.45 L 193.89 381.45 L 193.89 379.3 L 197.64 379.3 L 197.64 381.45 L 196.9 381.45 C 196.4 381.45 195.99 381.86 195.99 382.36 L 195.99 386.34 C 195.99 386.84 196.4 387.25 196.9 387.25 L 200.85 387.25 C 201.35 387.25 201.76 386.84 201.76 386.34 L 201.76 382.36 C 201.76 381.86 201.35 381.45 200.85 381.45 Z M 183.55 369.52 L 183.55 364.18 L 194.45 364.18 L 194.45 369.52 Z M 178.06 385.43 L 178.06 383.27 L 180.14 383.27 L 180.14 385.43 Z M 184.34 385.43 L 184.34 383.27 L 186.5 383.27 L 186.5 385.43 Z M 191.5 385.43 L 191.5 383.27 L 193.66 383.27 L 193.66 385.43 Z M 197.81 385.43 L 197.81 383.27 L 199.94 383.27 L 199.94 385.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 403px; margin-left: 189px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ALB<br />(Rolling)</div></div></div></foreignObject><text x="189" y="415" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ALB...</text></switch></g></g></g><g data-cell-id="sF_ue8Q8BgG2MTyI-QfG-71"><g><rect x="429" y="356" width="40" height="40" fill="none" stroke="none" pointer-events="all"/><path d="M 439.57 386.84 L 439.57 384.26 L 441.18 385.55 Z M 439.23 381.65 C 438.95 381.43 438.58 381.39 438.26 381.54 C 437.95 381.7 437.75 382.01 437.75 382.36 L 437.75 388.73 C 437.75 389.08 437.95 389.39 438.26 389.55 C 438.39 389.61 438.53 389.64 438.66 389.64 C 438.86 389.64 439.06 389.57 439.23 389.44 L 443.2 386.26 C 443.42 386.08 443.55 385.82 443.55 385.55 C 443.55 385.27 443.42 385.01 443.2 384.84 Z M 461.05 378.58 L 461.05 375.01 L 462.83 376.79 Z M 464.76 376.15 L 460.78 372.18 C 460.52 371.92 460.13 371.84 459.79 371.98 C 459.45 372.12 459.23 372.45 459.23 372.82 L 459.23 375.89 L 452.29 375.89 L 452.29 367.25 C 452.29 366.75 451.89 366.34 451.39 366.34 L 444.39 366.34 L 444.39 368.16 L 450.48 368.16 L 450.48 375.89 L 444.23 375.89 L 444.23 377.7 L 450.48 377.7 L 450.48 385.43 L 444.39 385.43 L 444.39 387.25 L 451.39 387.25 C 451.89 387.25 452.29 386.84 452.29 386.34 L 452.29 377.7 L 459.23 377.7 L 459.23 380.77 C 459.23 381.14 459.45 381.47 459.79 381.61 C 459.9 381.66 460.02 381.68 460.14 381.68 C 460.37 381.68 460.61 381.59 460.78 381.42 L 464.76 377.44 C 465.11 377.08 465.11 376.51 464.76 376.15 Z M 439.57 378.09 L 439.57 375.51 L 441.18 376.79 Z M 439.23 372.9 C 438.95 372.68 438.58 372.64 438.26 372.79 C 437.95 372.95 437.75 373.26 437.75 373.61 L 437.75 379.98 C 437.75 380.33 437.95 380.64 438.26 380.8 C 438.39 380.86 438.53 380.89 438.66 380.89 C 438.86 380.89 439.06 380.82 439.23 380.69 L 443.2 377.5 C 443.42 377.33 443.55 377.07 443.55 376.79 C 443.55 376.52 443.42 376.26 443.2 376.08 Z M 439.57 368.54 L 439.57 365.96 L 441.18 367.25 Z M 439.23 363.36 C 438.95 363.14 438.58 363.1 438.26 363.25 C 437.95 363.4 437.75 363.72 437.75 364.07 L 437.75 370.43 C 437.75 370.78 437.95 371.1 438.26 371.25 C 438.39 371.31 438.53 371.34 438.66 371.34 C 438.86 371.34 439.06 371.27 439.23 371.14 L 443.2 367.96 C 443.42 367.79 443.55 367.53 443.55 367.25 C 443.55 366.97 443.42 366.71 443.2 366.54 Z M 449 394.18 C 438.97 394.18 430.82 386.03 430.82 376 C 430.82 365.97 438.97 357.82 449 357.82 C 459.03 357.82 467.18 365.97 467.18 376 C 467.18 386.03 459.03 394.18 449 394.18 Z M 449 356 C 437.97 356 429 364.97 429 376 C 429 387.03 437.97 396 449 396 C 460.03 396 469 387.03 469 376 C 469 364.97 460.03 356 449 356 Z" fill="#4d27aa" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 403px; margin-left: 449px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">NAT Gateway</div></div></div></foreignObject><text x="449" y="415" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">NAT Ga...</text></switch></g></g></g><g data-cell-id="0PMTHTs6A5PybTUWxF9K-0"><g><path d="M 427.5 726 L 470.5 726 L 470.5 769 L 427.5 769 Z" fill="#c925d1" stroke="none" pointer-events="all"/><path d="M 455.76 760.43 L 455.76 756.84 C 454.2 757.84 451.4 758.32 448.73 758.32 C 445.82 758.32 443.55 757.81 442.24 756.94 L 442.24 760.43 C 442.24 761.47 444.67 762.55 448.73 762.55 C 452.87 762.55 455.76 761.44 455.76 760.43 Z M 448.73 752.86 C 445.82 752.86 443.55 752.35 442.24 751.48 L 442.24 754.99 C 442.26 756.02 444.68 757.09 448.73 757.09 C 452.86 757.09 455.74 755.98 455.76 754.98 L 455.76 751.38 C 454.2 752.38 451.4 752.86 448.73 752.86 Z M 455.76 749.52 L 455.76 745.37 C 454.2 746.37 451.4 746.85 448.73 746.85 C 445.82 746.85 443.55 746.34 442.24 745.47 L 442.24 749.52 C 442.26 750.56 444.68 751.63 448.73 751.63 C 452.86 751.63 455.74 750.52 455.76 749.52 Z M 442.24 743.5 C 442.24 743.5 442.24 743.51 442.24 743.51 L 442.24 743.51 L 442.24 743.52 C 442.26 744.55 444.68 745.62 448.73 745.62 C 453.24 745.62 455.74 744.38 455.76 743.51 L 455.76 743.51 L 455.76 743.51 C 455.76 743.51 455.76 743.5 455.76 743.5 C 455.76 742.64 453.26 741.39 448.73 741.39 C 444.67 741.39 442.24 742.46 442.24 743.5 Z M 456.99 743.52 L 456.99 749.51 L 456.99 749.51 C 456.99 749.52 456.99 749.52 456.99 749.53 L 456.99 754.97 L 456.99 754.97 C 456.99 754.98 456.99 754.98 456.99 754.99 L 456.99 760.43 C 456.99 762.73 452.7 763.78 448.73 763.78 C 444.04 763.78 441.01 762.47 441.01 760.43 L 441.01 754.99 C 441.01 754.99 441.01 754.98 441.01 754.97 L 441.01 754.97 L 441.01 749.53 C 441.01 749.52 441.01 749.52 441.01 749.51 L 441.01 749.51 L 441.01 743.52 C 441.01 743.52 441.01 743.51 441.01 743.5 C 441.01 741.47 444.04 740.16 448.73 740.16 C 452.71 740.16 456.99 741.2 456.99 743.5 C 456.99 743.51 456.99 743.51 456.99 743.52 Z M 465.59 735.6 C 465.93 735.6 466.2 735.32 466.2 734.98 L 466.2 731.84 C 466.2 731.5 465.93 731.22 465.59 731.22 L 432.41 731.22 C 432.07 731.22 431.8 731.5 431.8 731.84 L 431.8 734.98 C 431.8 735.32 432.07 735.6 432.41 735.6 C 433.16 735.6 433.77 736.2 433.77 736.95 C 433.77 737.69 433.16 738.3 432.41 738.3 C 432.07 738.3 431.8 738.57 431.8 738.91 L 431.8 751.49 C 431.8 751.83 432.07 752.11 432.41 752.11 L 438.56 752.11 L 438.56 750.88 L 435.49 750.88 L 435.49 749.04 L 438.56 749.04 L 438.56 747.81 L 434.87 747.81 C 434.53 747.81 434.26 748.08 434.26 748.42 L 434.26 750.88 L 433.03 750.88 L 433.03 739.45 C 434.16 739.18 435 738.16 435 736.95 C 435 735.74 434.16 734.72 433.03 734.44 L 433.03 732.45 L 464.97 732.45 L 464.97 734.44 C 463.84 734.72 463 735.74 463 736.95 C 463 738.16 463.84 739.18 464.97 739.45 L 464.97 750.88 L 463.74 750.88 L 463.74 748.42 C 463.74 748.08 463.47 747.81 463.13 747.81 L 459.44 747.81 L 459.44 749.04 L 462.51 749.04 L 462.51 750.88 L 459.44 750.88 L 459.44 752.11 L 465.59 752.11 C 465.93 752.11 466.2 751.83 466.2 751.49 L 466.2 738.91 C 466.2 738.57 465.93 738.3 465.59 738.3 C 464.84 738.3 464.23 737.69 464.23 736.95 C 464.23 736.2 464.84 735.6 465.59 735.6 Z M 441.63 739.82 L 441.63 734.91 C 441.63 734.57 441.35 734.29 441.01 734.29 L 437.33 734.29 C 436.99 734.29 436.71 734.57 436.71 734.91 L 436.71 745.35 C 436.71 745.69 436.99 745.97 437.33 745.97 L 439.17 745.97 L 439.17 744.74 L 437.94 744.74 L 437.94 735.52 L 440.4 735.52 L 440.4 739.82 Z M 460.06 744.74 L 459.44 744.74 L 459.44 745.97 L 460.67 745.97 C 461.01 745.97 461.29 745.69 461.29 745.35 L 461.29 734.91 C 461.29 734.57 461.01 734.29 460.67 734.29 L 456.99 734.29 C 456.65 734.29 456.37 734.57 456.37 734.91 L 456.37 739.82 L 457.6 739.82 L 457.6 735.52 L 460.06 735.52 Z M 455.14 739.21 L 455.14 734.91 C 455.14 734.57 454.87 734.29 454.53 734.29 L 450.23 734.29 C 449.89 734.29 449.61 734.57 449.61 734.91 L 449.61 738.59 L 450.84 738.59 L 450.84 735.52 L 453.91 735.52 L 453.91 739.21 Z M 447.16 738.59 L 447.16 735.52 L 444.09 735.52 L 444.09 739.21 L 442.86 739.21 L 442.86 734.91 C 442.86 734.57 443.13 734.29 443.47 734.29 L 447.77 734.29 C 448.11 734.29 448.39 734.57 448.39 734.91 L 448.39 738.59 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 776px; margin-left: 449px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ElastiCache</div></div></div></foreignObject><text x="449" y="788" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ElastiC...</text></switch></g></g></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>