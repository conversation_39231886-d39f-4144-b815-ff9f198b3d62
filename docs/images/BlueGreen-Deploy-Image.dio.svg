<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1370px" height="277px" viewBox="-0.5 -0.5 1370 277" content="&lt;mxfile&gt;&lt;diagram id=&quot;PqED9AQzTnBefdZl7VOb&quot; name=&quot;240823&quot;&gt;7Vtfb6M4EP80le5eIgzYwGPSNnsPPWml3ulunyonOAm7TozAbZL79GcHE2JMaVISSCpaqcWD8ZiZ8W/+2Nw598vNtwTHiz9ZSOidbYWbO+fhzrYBsjzxT1K2GQX5TkaYJ1GoOhWE5+g/ooiWor5GIUm1jpwxyqNYJ07ZakWmXKPhJGFrvduMUZ1rjOfEIDxPMTWp/0QhX2RU3/YK+h8kmi9yzgAF2Z0lzjurN0kXOGTrA5LzeOfcJ4zx7Gq5uSdUCi+XS/bc+J27+4klZMWPecCx1Tz4Nn85Eop3VU2W8AWbsxWmjwV1tOBLKm4CcSnYJNt/RcMawLz5QzbzxsNGa21VK+MqWb07cUVK2WsyVb0cZSYcJ3OiujlwLzZhb4QtieAj+iSEYh696eNjpfj5vl8hG3GhxPOOqBTzN0xf1ajDp5EhvvQX4dOFek32ymm0Ivd7O5TEGVvxe0ZZsnvAEb9jyXE0T3AYkeLeiq2ktGcRpQfd3QfbGw4FPeUJ+0VKnUOcLkioGL2RhEfCap/whNDvLI14xFbi3oRxzpYHHYY0mssbnMWCilVrKuZCEl3fcu5qPQI7b6uXlyxxGmcvOos2ch6jmEVylMc3MViqBhFGH8sHlpu5xIcBXqfuAMcxFXORU3yhDIcvE0zxarqbgBK8mC3Z1NuLaQfqgf2K2+5BKGuviwXsKtLiYO3mtEaW4zZbZJ9cLLBisfgNF8vu0WGS4O1Bh52O04ORv0tCIXtQkr1TBqVSf1TbXVxkEyhkv3+T49QBjYX81zdTQ5QKdyI1sV5EnDzHeCfYtbBYXTuGyesL01yyucuwz2PZwNLFtTfZNiwbGaIUPpdjgXnJZ5BRhzqERvZ4XCVRNPXJZHYFeHc0vO3F8gIMQ6i0onNYRnkhtYl5XjPMKznJwIUPY6kAson4QbwhWj/yUERcF9GGbDQMNqrwE50DPz8EPD8YBEEAXegi1/egp4+XTVINUQOjwBpYtg894O7+OtDVuIiRBwD6SPAA4qaNdC7ZaxtcLo39wL8w+Ps9Yl0vYrndAZYLvlRU4LjdRQVuw3i33aTSrcB512uI80eLyoxF+6TyZpJKz+ouqXRRs0X2ycXimYsFWtVCumxSiazTAgtY9i1nDixyyXwR/wH9Dv1HH6NdcYyGugzSgmag131WWQWg7lmqch8j3nnSSjco55Ual3bSylPRH124pgitHrKuF7JguQjRImRBYOi/hTgNVuyUZfWftuM0vz7uMvp7Fy4AQftq9NF057IVfQT1SNtcH+a26i3Hzb7dXdycF3irnRCigtVoIq/m8uq3v+MQc/L7OdzTeIxQ754+XUbocJ8GmgWo3mZuwGb8spts02bMXd9an6rKiIVrfWJSOTvZ/CScb5UW8Ctnuopycc8o2Qzlwa0THLApugPR5NJrmGK55So7DAZQH+TYtKoco1YMdXLupCn0NLfsNQxb263hw8AMr1BbNfyceV/Dv8UaPijvOLdZxM9ZtZuMoPzM7OFq6SQ5BFZ9tlfxQP15gsbpSC6bL5KOAAt1l48g25BlXxS7mggSGEdz2gQ+pxnwdV/IrwRR+xwgegTonaeSH3h6Jb+T82EnewBw4Y1cVFtF6VGrY9SyyvXINlELdhKuVWwZom4O8jv10VfF6c8L77uhTk7BVGokuA2NOBfeX0Ff6xwMcDr8vALVHoTpi7NX66S6PMiMzHJUbzS3YDROh9tAnpmq1zrWr1vSDyrq8HITPP8Bjj7i8V/jlB3JBwNfsNovmsUn5ln34kN95/F/&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 184 20 L 94 20 Q 84 20 84 30 L 84 73.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 84 78.88 L 80.5 71.88 L 84 73.63 L 87.5 71.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="184" y="0" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 204 0 C 192.97 0 184 8.97 184 20 C 184 31.03 192.97 40 204 40 C 215.03 40 224 31.03 224 20 C 224 8.97 215.03 0 204 0 Z M 204 38.18 C 193.97 38.18 185.82 30.03 185.82 20 C 185.82 9.97 193.97 1.82 204 1.82 C 214.03 1.82 222.18 9.97 222.18 20 C 222.18 30.03 214.03 38.18 204 38.18 Z M 215.85 25.45 L 214.45 25.45 L 214.45 22.39 C 214.45 21.88 214.05 21.48 213.55 21.48 L 211.27 21.48 L 211.27 18.41 C 211.27 17.91 210.87 17.5 210.36 17.5 L 204.91 17.5 L 204.91 15.34 L 210.36 15.34 C 210.87 15.34 211.27 14.93 211.27 14.43 L 211.27 7.27 C 211.27 6.77 210.87 6.36 210.36 6.36 L 197.64 6.36 C 197.13 6.36 196.73 6.77 196.73 7.27 L 196.73 14.43 C 196.73 14.93 197.13 15.34 197.64 15.34 L 203.09 15.34 L 203.09 17.5 L 197.64 17.5 C 197.13 17.5 196.73 17.91 196.73 18.41 L 196.73 21.48 L 194.46 21.48 C 193.95 21.48 193.55 21.88 193.55 22.39 L 193.55 25.45 L 192.15 25.45 C 191.65 25.45 191.24 25.86 191.24 26.36 L 191.24 30.34 C 191.24 30.84 191.65 31.25 192.15 31.25 L 196.05 31.25 C 196.55 31.25 196.96 30.84 196.96 30.34 L 196.96 26.36 C 196.96 25.86 196.55 25.45 196.05 25.45 L 195.36 25.45 L 195.36 23.3 L 199.11 23.3 L 199.11 25.45 L 198.43 25.45 C 197.93 25.45 197.52 25.86 197.52 26.36 L 197.52 30.34 C 197.52 30.84 197.93 31.25 198.43 31.25 L 202.41 31.25 C 202.91 31.25 203.32 30.84 203.32 30.34 L 203.32 26.36 C 203.32 25.86 202.91 25.45 202.41 25.45 L 200.93 25.45 L 200.93 22.39 C 200.93 21.88 200.53 21.48 200.02 21.48 L 198.55 21.48 L 198.55 19.32 L 209.45 19.32 L 209.45 21.48 L 207.98 21.48 C 207.47 21.48 207.07 21.88 207.07 22.39 L 207.07 25.45 L 205.59 25.45 C 205.09 25.45 204.68 25.86 204.68 26.36 L 204.68 30.34 C 204.68 30.84 205.09 31.25 205.59 31.25 L 209.57 31.25 C 210.07 31.25 210.48 30.84 210.48 30.34 L 210.48 26.36 C 210.48 25.86 210.07 25.45 209.57 25.45 L 208.89 25.45 L 208.89 23.3 L 212.64 23.3 L 212.64 25.45 L 211.9 25.45 C 211.4 25.45 210.99 25.86 210.99 26.36 L 210.99 30.34 C 210.99 30.84 211.4 31.25 211.9 31.25 L 215.85 31.25 C 216.35 31.25 216.76 30.84 216.76 30.34 L 216.76 26.36 C 216.76 25.86 216.35 25.45 215.85 25.45 Z M 198.55 13.52 L 198.55 8.18 L 209.45 8.18 L 209.45 13.52 Z M 193.06 29.43 L 193.06 27.27 L 195.14 27.27 L 195.14 29.43 Z M 199.34 29.43 L 199.34 27.27 L 201.5 27.27 L 201.5 29.43 Z M 206.5 29.43 L 206.5 27.27 L 208.66 27.27 L 208.66 29.43 Z M 212.81 29.43 L 212.81 27.27 L 214.94 27.27 L 214.94 29.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 47px; margin-left: 204px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="204" y="59" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 84 120 L 84 150 Q 84 160 74 160 L 34 160 Q 24 160 24 170 L 24 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 24 206.04 L 20.5 199.04 L 24 200.79 L 27.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <ellipse cx="84" cy="100" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="#4d27aa" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 65px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                TG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="84" y="104" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TG
                </text>
            </switch>
        </g>
        <rect x="124" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 163.09 207.16 L 124.91 207.16 C 124.41 207.16 124 207.57 124 208.07 L 124 231.93 C 124 232.43 124.41 232.84 124.91 232.84 L 163.09 232.84 C 163.59 232.84 164 232.43 164 231.93 L 164 208.07 C 164 207.57 163.59 207.16 163.09 207.16 Z M 125.82 231.02 L 125.82 208.98 L 162.18 208.98 L 162.18 231.02 Z M 128.77 228.75 L 130.59 228.75 L 130.59 211.25 L 128.77 211.25 Z M 133.55 228.75 L 135.36 228.75 L 135.36 211.25 L 133.55 211.25 Z M 138.32 228.75 L 140.14 228.75 L 140.14 211.25 L 138.32 211.25 Z M 143.09 228.75 L 144.91 228.75 L 144.91 211.25 L 143.09 211.25 Z M 147.86 228.75 L 149.68 228.75 L 149.68 211.25 L 147.86 211.25 Z M 152.64 228.75 L 154.45 228.75 L 154.45 211.25 L 152.64 211.25 Z M 157.41 228.75 L 159.23 228.75 L 159.23 211.25 L 157.41 211.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 144px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="144" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 84 120 L 84 150 Q 84 160 94 160 L 134 160 Q 144 160 144 170 L 144 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 144 206.04 L 140.5 199.04 L 144 200.79 L 147.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="4" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 43.09 207.16 L 4.91 207.16 C 4.41 207.16 4 207.57 4 208.07 L 4 231.93 C 4 232.43 4.41 232.84 4.91 232.84 L 43.09 232.84 C 43.59 232.84 44 232.43 44 231.93 L 44 208.07 C 44 207.57 43.59 207.16 43.09 207.16 Z M 5.82 231.02 L 5.82 208.98 L 42.18 208.98 L 42.18 231.02 Z M 8.77 228.75 L 10.59 228.75 L 10.59 211.25 L 8.77 211.25 Z M 13.55 228.75 L 15.36 228.75 L 15.36 211.25 L 13.55 211.25 Z M 18.32 228.75 L 20.14 228.75 L 20.14 211.25 L 18.32 211.25 Z M 23.09 228.75 L 24.91 228.75 L 24.91 211.25 L 23.09 211.25 Z M 27.86 228.75 L 29.68 228.75 L 29.68 211.25 L 27.86 211.25 Z M 32.64 228.75 L 34.45 228.75 L 34.45 211.25 L 32.64 211.25 Z M 37.41 228.75 L 39.23 228.75 L 39.23 211.25 L 37.41 211.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 24px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="24" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <ellipse cx="324" cy="100" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="#4d27aa" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 305px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                TG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="324" y="104" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TG
                </text>
            </switch>
        </g>
        <path d="M 664 20 L 574 20 Q 564 20 564 30 L 564 73.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 564 78.88 L 560.5 71.88 L 564 73.63 L 567.5 71.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="664" y="0" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 684 0 C 672.97 0 664 8.97 664 20 C 664 31.03 672.97 40 684 40 C 695.03 40 704 31.03 704 20 C 704 8.97 695.03 0 684 0 Z M 684 38.18 C 673.97 38.18 665.82 30.03 665.82 20 C 665.82 9.97 673.97 1.82 684 1.82 C 694.03 1.82 702.18 9.97 702.18 20 C 702.18 30.03 694.03 38.18 684 38.18 Z M 695.85 25.45 L 694.45 25.45 L 694.45 22.39 C 694.45 21.88 694.05 21.48 693.55 21.48 L 691.27 21.48 L 691.27 18.41 C 691.27 17.91 690.87 17.5 690.36 17.5 L 684.91 17.5 L 684.91 15.34 L 690.36 15.34 C 690.87 15.34 691.27 14.93 691.27 14.43 L 691.27 7.27 C 691.27 6.77 690.87 6.36 690.36 6.36 L 677.64 6.36 C 677.13 6.36 676.73 6.77 676.73 7.27 L 676.73 14.43 C 676.73 14.93 677.13 15.34 677.64 15.34 L 683.09 15.34 L 683.09 17.5 L 677.64 17.5 C 677.13 17.5 676.73 17.91 676.73 18.41 L 676.73 21.48 L 674.46 21.48 C 673.95 21.48 673.55 21.88 673.55 22.39 L 673.55 25.45 L 672.15 25.45 C 671.65 25.45 671.24 25.86 671.24 26.36 L 671.24 30.34 C 671.24 30.84 671.65 31.25 672.15 31.25 L 676.05 31.25 C 676.55 31.25 676.96 30.84 676.96 30.34 L 676.96 26.36 C 676.96 25.86 676.55 25.45 676.05 25.45 L 675.36 25.45 L 675.36 23.3 L 679.11 23.3 L 679.11 25.45 L 678.43 25.45 C 677.93 25.45 677.52 25.86 677.52 26.36 L 677.52 30.34 C 677.52 30.84 677.93 31.25 678.43 31.25 L 682.41 31.25 C 682.91 31.25 683.32 30.84 683.32 30.34 L 683.32 26.36 C 683.32 25.86 682.91 25.45 682.41 25.45 L 680.93 25.45 L 680.93 22.39 C 680.93 21.88 680.53 21.48 680.02 21.48 L 678.55 21.48 L 678.55 19.32 L 689.45 19.32 L 689.45 21.48 L 687.98 21.48 C 687.47 21.48 687.07 21.88 687.07 22.39 L 687.07 25.45 L 685.59 25.45 C 685.09 25.45 684.68 25.86 684.68 26.36 L 684.68 30.34 C 684.68 30.84 685.09 31.25 685.59 31.25 L 689.57 31.25 C 690.07 31.25 690.48 30.84 690.48 30.34 L 690.48 26.36 C 690.48 25.86 690.07 25.45 689.57 25.45 L 688.89 25.45 L 688.89 23.3 L 692.64 23.3 L 692.64 25.45 L 691.9 25.45 C 691.4 25.45 690.99 25.86 690.99 26.36 L 690.99 30.34 C 690.99 30.84 691.4 31.25 691.9 31.25 L 695.85 31.25 C 696.35 31.25 696.76 30.84 696.76 30.34 L 696.76 26.36 C 696.76 25.86 696.35 25.45 695.85 25.45 Z M 678.55 13.52 L 678.55 8.18 L 689.45 8.18 L 689.45 13.52 Z M 673.06 29.43 L 673.06 27.27 L 675.14 27.27 L 675.14 29.43 Z M 679.34 29.43 L 679.34 27.27 L 681.5 27.27 L 681.5 29.43 Z M 686.5 29.43 L 686.5 27.27 L 688.66 27.27 L 688.66 29.43 Z M 692.81 29.43 L 692.81 27.27 L 694.94 27.27 L 694.94 29.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 47px; margin-left: 684px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="684" y="59" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 564 120 L 564 150 Q 564 160 554 160 L 514 160 Q 504 160 504 170 L 504 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 504 206.04 L 500.5 199.04 L 504 200.79 L 507.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <ellipse cx="564" cy="100" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="#4d27aa" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 545px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                TG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="564" y="104" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TG
                </text>
            </switch>
        </g>
        <rect x="604" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 643.09 207.16 L 604.91 207.16 C 604.41 207.16 604 207.57 604 208.07 L 604 231.93 C 604 232.43 604.41 232.84 604.91 232.84 L 643.09 232.84 C 643.59 232.84 644 232.43 644 231.93 L 644 208.07 C 644 207.57 643.59 207.16 643.09 207.16 Z M 605.82 231.02 L 605.82 208.98 L 642.18 208.98 L 642.18 231.02 Z M 608.77 228.75 L 610.59 228.75 L 610.59 211.25 L 608.77 211.25 Z M 613.55 228.75 L 615.36 228.75 L 615.36 211.25 L 613.55 211.25 Z M 618.32 228.75 L 620.14 228.75 L 620.14 211.25 L 618.32 211.25 Z M 623.09 228.75 L 624.91 228.75 L 624.91 211.25 L 623.09 211.25 Z M 627.86 228.75 L 629.68 228.75 L 629.68 211.25 L 627.86 211.25 Z M 632.64 228.75 L 634.45 228.75 L 634.45 211.25 L 632.64 211.25 Z M 637.41 228.75 L 639.23 228.75 L 639.23 211.25 L 637.41 211.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 624px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="624" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 564 120 L 564 150 Q 564 160 574 160 L 614 160 Q 624 160 624 170 L 624 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 624 206.04 L 620.5 199.04 L 624 200.79 L 627.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="484" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 523.09 207.16 L 484.91 207.16 C 484.41 207.16 484 207.57 484 208.07 L 484 231.93 C 484 232.43 484.41 232.84 484.91 232.84 L 523.09 232.84 C 523.59 232.84 524 232.43 524 231.93 L 524 208.07 C 524 207.57 523.59 207.16 523.09 207.16 Z M 485.82 231.02 L 485.82 208.98 L 522.18 208.98 L 522.18 231.02 Z M 488.77 228.75 L 490.59 228.75 L 490.59 211.25 L 488.77 211.25 Z M 493.55 228.75 L 495.36 228.75 L 495.36 211.25 L 493.55 211.25 Z M 498.32 228.75 L 500.14 228.75 L 500.14 211.25 L 498.32 211.25 Z M 503.09 228.75 L 504.91 228.75 L 504.91 211.25 L 503.09 211.25 Z M 507.86 228.75 L 509.68 228.75 L 509.68 211.25 L 507.86 211.25 Z M 512.64 228.75 L 514.45 228.75 L 514.45 211.25 L 512.64 211.25 Z M 517.41 228.75 L 519.23 228.75 L 519.23 211.25 L 517.41 211.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 504px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="504" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 804 120 L 804 150 Q 804 160 794 160 L 754 160 Q 744 160 744 170 L 744 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 744 206.04 L 740.5 199.04 L 744 200.79 L 747.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 804 120 L 804 150 Q 804 160 814 160 L 854 160 Q 864 160 864 170 L 864 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 864 206.04 L 860.5 199.04 L 864 200.79 L 867.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <ellipse cx="804" cy="100" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="#4d27aa" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 785px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                TG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="804" y="104" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TG
                </text>
            </switch>
        </g>
        <rect x="724" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 763.09 207.16 L 724.91 207.16 C 724.41 207.16 724 207.57 724 208.07 L 724 231.93 C 724 232.43 724.41 232.84 724.91 232.84 L 763.09 232.84 C 763.59 232.84 764 232.43 764 231.93 L 764 208.07 C 764 207.57 763.59 207.16 763.09 207.16 Z M 725.82 231.02 L 725.82 208.98 L 762.18 208.98 L 762.18 231.02 Z M 728.77 228.75 L 730.59 228.75 L 730.59 211.25 L 728.77 211.25 Z M 733.55 228.75 L 735.36 228.75 L 735.36 211.25 L 733.55 211.25 Z M 738.32 228.75 L 740.14 228.75 L 740.14 211.25 L 738.32 211.25 Z M 743.09 228.75 L 744.91 228.75 L 744.91 211.25 L 743.09 211.25 Z M 747.86 228.75 L 749.68 228.75 L 749.68 211.25 L 747.86 211.25 Z M 752.64 228.75 L 754.45 228.75 L 754.45 211.25 L 752.64 211.25 Z M 757.41 228.75 L 759.23 228.75 L 759.23 211.25 L 757.41 211.25 Z" fill="#66ff66" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 744px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                                <br/>
                                (Update)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="744" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <rect x="844" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 883.09 207.16 L 844.91 207.16 C 844.41 207.16 844 207.57 844 208.07 L 844 231.93 C 844 232.43 844.41 232.84 844.91 232.84 L 883.09 232.84 C 883.59 232.84 884 232.43 884 231.93 L 884 208.07 C 884 207.57 883.59 207.16 883.09 207.16 Z M 845.82 231.02 L 845.82 208.98 L 882.18 208.98 L 882.18 231.02 Z M 848.77 228.75 L 850.59 228.75 L 850.59 211.25 L 848.77 211.25 Z M 853.55 228.75 L 855.36 228.75 L 855.36 211.25 L 853.55 211.25 Z M 858.32 228.75 L 860.14 228.75 L 860.14 211.25 L 858.32 211.25 Z M 863.09 228.75 L 864.91 228.75 L 864.91 211.25 L 863.09 211.25 Z M 867.86 228.75 L 869.68 228.75 L 869.68 211.25 L 867.86 211.25 Z M 872.64 228.75 L 874.45 228.75 L 874.45 211.25 L 872.64 211.25 Z M 877.41 228.75 L 879.23 228.75 L 879.23 211.25 L 877.41 211.25 Z" fill="#66ff66" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 864px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                                <br/>
                                (Update)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="864" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 404.5 104.5 L 404.5 94.5 L 464.5 94.5 L 464.5 84 L 483.5 99.5 L 464.5 115 L 464.5 104.5 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1184 20 L 1274 20 Q 1284 20 1284 30 L 1284 73.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1284 78.88 L 1280.5 71.88 L 1284 73.63 L 1287.5 71.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="1144" y="0" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1164 0 C 1152.97 0 1144 8.97 1144 20 C 1144 31.03 1152.97 40 1164 40 C 1175.03 40 1184 31.03 1184 20 C 1184 8.97 1175.03 0 1164 0 Z M 1164 38.18 C 1153.97 38.18 1145.82 30.03 1145.82 20 C 1145.82 9.97 1153.97 1.82 1164 1.82 C 1174.03 1.82 1182.18 9.97 1182.18 20 C 1182.18 30.03 1174.03 38.18 1164 38.18 Z M 1175.85 25.45 L 1174.45 25.45 L 1174.45 22.39 C 1174.45 21.88 1174.05 21.48 1173.55 21.48 L 1171.27 21.48 L 1171.27 18.41 C 1171.27 17.91 1170.87 17.5 1170.36 17.5 L 1164.91 17.5 L 1164.91 15.34 L 1170.36 15.34 C 1170.87 15.34 1171.27 14.93 1171.27 14.43 L 1171.27 7.27 C 1171.27 6.77 1170.87 6.36 1170.36 6.36 L 1157.64 6.36 C 1157.13 6.36 1156.73 6.77 1156.73 7.27 L 1156.73 14.43 C 1156.73 14.93 1157.13 15.34 1157.64 15.34 L 1163.09 15.34 L 1163.09 17.5 L 1157.64 17.5 C 1157.13 17.5 1156.73 17.91 1156.73 18.41 L 1156.73 21.48 L 1154.46 21.48 C 1153.95 21.48 1153.55 21.88 1153.55 22.39 L 1153.55 25.45 L 1152.15 25.45 C 1151.65 25.45 1151.24 25.86 1151.24 26.36 L 1151.24 30.34 C 1151.24 30.84 1151.65 31.25 1152.15 31.25 L 1156.05 31.25 C 1156.55 31.25 1156.96 30.84 1156.96 30.34 L 1156.96 26.36 C 1156.96 25.86 1156.55 25.45 1156.05 25.45 L 1155.36 25.45 L 1155.36 23.3 L 1159.11 23.3 L 1159.11 25.45 L 1158.43 25.45 C 1157.93 25.45 1157.52 25.86 1157.52 26.36 L 1157.52 30.34 C 1157.52 30.84 1157.93 31.25 1158.43 31.25 L 1162.41 31.25 C 1162.91 31.25 1163.32 30.84 1163.32 30.34 L 1163.32 26.36 C 1163.32 25.86 1162.91 25.45 1162.41 25.45 L 1160.93 25.45 L 1160.93 22.39 C 1160.93 21.88 1160.53 21.48 1160.02 21.48 L 1158.55 21.48 L 1158.55 19.32 L 1169.45 19.32 L 1169.45 21.48 L 1167.98 21.48 C 1167.47 21.48 1167.07 21.88 1167.07 22.39 L 1167.07 25.45 L 1165.59 25.45 C 1165.09 25.45 1164.68 25.86 1164.68 26.36 L 1164.68 30.34 C 1164.68 30.84 1165.09 31.25 1165.59 31.25 L 1169.57 31.25 C 1170.07 31.25 1170.48 30.84 1170.48 30.34 L 1170.48 26.36 C 1170.48 25.86 1170.07 25.45 1169.57 25.45 L 1168.89 25.45 L 1168.89 23.3 L 1172.64 23.3 L 1172.64 25.45 L 1171.9 25.45 C 1171.4 25.45 1170.99 25.86 1170.99 26.36 L 1170.99 30.34 C 1170.99 30.84 1171.4 31.25 1171.9 31.25 L 1175.85 31.25 C 1176.35 31.25 1176.76 30.84 1176.76 30.34 L 1176.76 26.36 C 1176.76 25.86 1176.35 25.45 1175.85 25.45 Z M 1158.55 13.52 L 1158.55 8.18 L 1169.45 8.18 L 1169.45 13.52 Z M 1153.06 29.43 L 1153.06 27.27 L 1155.14 27.27 L 1155.14 29.43 Z M 1159.34 29.43 L 1159.34 27.27 L 1161.5 27.27 L 1161.5 29.43 Z M 1166.5 29.43 L 1166.5 27.27 L 1168.66 27.27 L 1168.66 29.43 Z M 1172.81 29.43 L 1172.81 27.27 L 1174.94 27.27 L 1174.94 29.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 47px; margin-left: 1164px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1164" y="59" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 1044 120 L 1044 150 Q 1044 160 1034 160 L 994 160 Q 984 160 984 170 L 984 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 984 206.04 L 980.5 199.04 L 984 200.79 L 987.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <ellipse cx="1044" cy="100" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="#4d27aa" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 1025px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                TG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1044" y="104" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TG
                </text>
            </switch>
        </g>
        <rect x="1084" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1123.09 207.16 L 1084.91 207.16 C 1084.41 207.16 1084 207.57 1084 208.07 L 1084 231.93 C 1084 232.43 1084.41 232.84 1084.91 232.84 L 1123.09 232.84 C 1123.59 232.84 1124 232.43 1124 231.93 L 1124 208.07 C 1124 207.57 1123.59 207.16 1123.09 207.16 Z M 1085.82 231.02 L 1085.82 208.98 L 1122.18 208.98 L 1122.18 231.02 Z M 1088.77 228.75 L 1090.59 228.75 L 1090.59 211.25 L 1088.77 211.25 Z M 1093.55 228.75 L 1095.36 228.75 L 1095.36 211.25 L 1093.55 211.25 Z M 1098.32 228.75 L 1100.14 228.75 L 1100.14 211.25 L 1098.32 211.25 Z M 1103.09 228.75 L 1104.91 228.75 L 1104.91 211.25 L 1103.09 211.25 Z M 1107.86 228.75 L 1109.68 228.75 L 1109.68 211.25 L 1107.86 211.25 Z M 1112.64 228.75 L 1114.45 228.75 L 1114.45 211.25 L 1112.64 211.25 Z M 1117.41 228.75 L 1119.23 228.75 L 1119.23 211.25 L 1117.41 211.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 1104px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1104" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 1044 120 L 1044 150 Q 1044 160 1054 160 L 1094 160 Q 1104 160 1104 170 L 1104 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1104 206.04 L 1100.5 199.04 L 1104 200.79 L 1107.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="964" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1003.09 207.16 L 964.91 207.16 C 964.41 207.16 964 207.57 964 208.07 L 964 231.93 C 964 232.43 964.41 232.84 964.91 232.84 L 1003.09 232.84 C 1003.59 232.84 1004 232.43 1004 231.93 L 1004 208.07 C 1004 207.57 1003.59 207.16 1003.09 207.16 Z M 965.82 231.02 L 965.82 208.98 L 1002.18 208.98 L 1002.18 231.02 Z M 968.77 228.75 L 970.59 228.75 L 970.59 211.25 L 968.77 211.25 Z M 973.55 228.75 L 975.36 228.75 L 975.36 211.25 L 973.55 211.25 Z M 978.32 228.75 L 980.14 228.75 L 980.14 211.25 L 978.32 211.25 Z M 983.09 228.75 L 984.91 228.75 L 984.91 211.25 L 983.09 211.25 Z M 987.86 228.75 L 989.68 228.75 L 989.68 211.25 L 987.86 211.25 Z M 992.64 228.75 L 994.45 228.75 L 994.45 211.25 L 992.64 211.25 Z M 997.41 228.75 L 999.23 228.75 L 999.23 211.25 L 997.41 211.25 Z" fill="#66b2ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 984px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="984" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 1284 120 L 1284 150 Q 1284 160 1274 160 L 1234 160 Q 1224 160 1224 170 L 1224 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1224 206.04 L 1220.5 199.04 L 1224 200.79 L 1227.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1284 120 L 1284 150 Q 1284 160 1294 160 L 1334 160 Q 1344 160 1344 170 L 1344 200.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1344 206.04 L 1340.5 199.04 L 1344 200.79 L 1347.5 199.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <ellipse cx="1284" cy="100" rx="20" ry="20" fill="rgb(255, 255, 255)" stroke="#4d27aa" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 1265px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                TG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1284" y="104" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    TG
                </text>
            </switch>
        </g>
        <rect x="1204" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1243.09 207.16 L 1204.91 207.16 C 1204.41 207.16 1204 207.57 1204 208.07 L 1204 231.93 C 1204 232.43 1204.41 232.84 1204.91 232.84 L 1243.09 232.84 C 1243.59 232.84 1244 232.43 1244 231.93 L 1244 208.07 C 1244 207.57 1243.59 207.16 1243.09 207.16 Z M 1205.82 231.02 L 1205.82 208.98 L 1242.18 208.98 L 1242.18 231.02 Z M 1208.77 228.75 L 1210.59 228.75 L 1210.59 211.25 L 1208.77 211.25 Z M 1213.55 228.75 L 1215.36 228.75 L 1215.36 211.25 L 1213.55 211.25 Z M 1218.32 228.75 L 1220.14 228.75 L 1220.14 211.25 L 1218.32 211.25 Z M 1223.09 228.75 L 1224.91 228.75 L 1224.91 211.25 L 1223.09 211.25 Z M 1227.86 228.75 L 1229.68 228.75 L 1229.68 211.25 L 1227.86 211.25 Z M 1232.64 228.75 L 1234.45 228.75 L 1234.45 211.25 L 1232.64 211.25 Z M 1237.41 228.75 L 1239.23 228.75 L 1239.23 211.25 L 1237.41 211.25 Z" fill="#66ff66" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 1224px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                                <br/>
                                (Update)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1224" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <rect x="1324" y="200" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1363.09 207.16 L 1324.91 207.16 C 1324.41 207.16 1324 207.57 1324 208.07 L 1324 231.93 C 1324 232.43 1324.41 232.84 1324.91 232.84 L 1363.09 232.84 C 1363.59 232.84 1364 232.43 1364 231.93 L 1364 208.07 C 1364 207.57 1363.59 207.16 1363.09 207.16 Z M 1325.82 231.02 L 1325.82 208.98 L 1362.18 208.98 L 1362.18 231.02 Z M 1328.77 228.75 L 1330.59 228.75 L 1330.59 211.25 L 1328.77 211.25 Z M 1333.55 228.75 L 1335.36 228.75 L 1335.36 211.25 L 1333.55 211.25 Z M 1338.32 228.75 L 1340.14 228.75 L 1340.14 211.25 L 1338.32 211.25 Z M 1343.09 228.75 L 1344.91 228.75 L 1344.91 211.25 L 1343.09 211.25 Z M 1347.86 228.75 L 1349.68 228.75 L 1349.68 211.25 L 1347.86 211.25 Z M 1352.64 228.75 L 1354.45 228.75 L 1354.45 211.25 L 1352.64 211.25 Z M 1357.41 228.75 L 1359.23 228.75 L 1359.23 211.25 L 1357.41 211.25 Z" fill="#66ff66" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 1344px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                container
                                <br/>
                                (Update)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1344" y="259" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    contai...
                </text>
            </switch>
        </g>
        <path d="M 884.5 104.29 L 884.5 94.29 L 944.5 94.29 L 944.5 83.79 L 963.5 99.29 L 944.5 114.79 L 944.5 104.29 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>