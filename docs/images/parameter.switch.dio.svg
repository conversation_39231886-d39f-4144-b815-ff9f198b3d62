<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="941px" height="261px" viewBox="-0.5 -0.5 941 261" content="&lt;mxfile&gt;&lt;diagram id=&quot;yny-sU1lQc9J4URSI4Re&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="0" width="940" height="260" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="50" y="57" width="580" height="189" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="50" y="57" width="590" height="89" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 588px; height: 1px; padding-top: 64px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="">
                                    <font style="">
                                        <font style="font-size: 15px;">
                                            <b>
                                                bin/blea-guest-ecsapp-sample.ts
                                            </b>
                                        </font>
                                        <br/>
                                        <br/>
                                    </font>
                                </span>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <font style="font-size: 12px;">
                                        import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
                                    </font>
                                </div>
                                <span style="">
                                    <font style="font-size: 12px;">
                                        <br/>
                                    </font>
                                </span>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <font style="font-size: 12px;">
                                        const
                                        <span style="background-color: rgb(255, 204, 204);">
                                            config
                                        </span>
                                        :
                                        <span style="font-style: italic;">
                                            IConfig
                                        </span>
                                        =
                                        <u style="">
                                            require('../params/' + envKey)
                                        </u>
                                        ;
                                    </font>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="76" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    bin/blea-guest-ecsapp-sample.ts...
                </text>
            </switch>
        </g>
        <image x="-0.5" y="0.5" width="50" height="50" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="12" y="59" width="27" height="15" stroke-width="0"/>
            <text x="24.5" y="68.5">
                CDK
            </text>
        </g>
        <path d="M 650 156 L 650 166 L 345 166 L 345 152.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 345 147.12 L 348.5 154.12 L 345 152.37 L 341.5 154.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 163px; margin-left: 476px;">
                        <div data-drawio-colors="color: #000000; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                環境ファイル読込
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="476" y="166" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    環境ファイル読込
                </text>
            </switch>
        </g>
        <rect x="650" y="126" width="280" height="120" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 278px; height: 1px; padding-top: 133px; margin-left: 652px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="">
                                    <font style="font-size: 15px;">
                                        <b>
                                            params/stage.ts
                                        </b>
                                    </font>
                                    <br/>
                                    <br/>
                                </font>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        <div style="line-height: 19px;">
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    <span style="font-style: italic;">
                                                        export
                                                    </span>
                                                    const
                                                    <span style="background-color: rgb(204, 255, 230);">
                                                        VpcParam
                                                    </span>
                                                    : inf.
                                                    <span style="font-style: italic;">
                                                        IVpcParam
                                                    </span>
                                                    = {
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    <span style="background-color: rgb(204, 204, 255);">
                                                        cidr
                                                    </span>
                                                    :
                                                    <b>
                                                        '10.100.0.0/16'
                                                    </b>
                                                    ,
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    maxAzs: 2,
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    };
                                                </font>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="652" y="145" fill="#000000" font-family="Helvetica" font-size="12px">
                    params/stage.ts...
                </text>
            </switch>
        </g>
        <path d="M 290 30 L 530 30 L 530 80 L 415.2 80 L 393.2 130 L 405.2 80 L 290 80 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 55px; margin-left: 291px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <br/>
                                <span style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    -c environment={環境名}で指定した環境名
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="410" y="59" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    -c environment={環境名}で指定した環境名
                </text>
            </switch>
        </g>
        <rect x="50" y="164" width="590" height="70" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 588px; height: 1px; padding-top: 171px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
                                    </div>
                                    <div style="">
                                        myVpcCidr:
                                        <span style="background-color: rgb(255, 204, 204);">
                                            config
                                        </span>
                                        .
                                        <span style="background-color: rgb(204, 255, 230);">
                                            VpcParam
                                        </span>
                                        .
                                        <span style="background-color: rgb(204, 204, 255);">
                                            cidr
                                        </span>
                                        ,
                                    </div>
                                    <div style="line-height: 19px;">
                                        <div>
                                            myVpcMaxAzs: config.VpcParam.maxAzs,
                                        </div>
                                    </div>
                                    <div style="">
                                        });
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="183" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>