# Deployment Error Reference

ここでは、デプロイ時にエラーが生じるか、現状、対応策のないものについて記載する。  
コマンドの書き換え等で事前にエラー回避ができるものについては、[CDK コードの修正](03_HowToDeploy.md#cdk-コードの修正)にまとめている。

## 目次

- [Deployment Error Reference](#deployment-error-reference)
  - [目次](#目次)
  - [ECS クラスターの初回デプロイエラー](#ecs-クラスターの初回デプロイエラー)
  - [VPC スタックのデプロイエラー](#vpc-スタックのデプロイエラー)
  - [セッションポリシーのサイズ制限エラー](#セッションポリシーのサイズ制限エラー)
  - [CloudWatch Logs の文字数制限エラー](#cloudwatch-logs-の文字数制限エラー)

## ECS クラスターの初回デプロイエラー

- 発生条件

  - ECS クラスターを AWS アカウント上に初めてデプロイする時
  - ※既に`AWSServiceRoleForECS`ロールが存在する場合、または、初回以降のデプロイでは、当該エラーは発生しない。

- エラー内容

  ```sh
  7:12:56 AM | CREATE_FAILED        | AWS::ECS::ClusterCapacityProviderAssociations   | ProdLiving-ECSCommon/Cluster/Cluster
  Resource handler returned message: "Unable to assume the service linked role. Please verify that the ECS service linked role exists. (Service: AmazonECS; Status Code: 400; Error Code: InvalidParameterException; Reque
  st ID: 0a21090b-d270-476a-b42e-7e6d39a961c8; Proxy: null)" (RequestToken: e0b8e82f-5c7c-30d5-d655-f9720723136b, HandlerErrorCode: InvalidRequest)
  ```

- 原因

  - ECS クラスター作成時に、自動的に`AWSServiceRoleForECS`ロールが作成されるが、ECS サービスから参照できるようになるまでに、タイムラグがあるため。

- 対応策

  - デプロイを再実行する。
  - デプロイ前に手動で`AWSServiceRoleForECS`を作成する。

## VPC スタックのデプロイエラー

- 発生条件

  - VPC スタックを初回デプロイする時　※VPC スタックを初回デプロイしても、当該エラーが発生しない場合もある。
  - ※再デプロイでは、当該エラーは発生しない。

- エラー内容

  ```sh
  Access Denied for LogDestination: prodkoshi-vpc-flowlogbucket0863acca-kw32ab24mmdo.  Please check LogDestination permission" (RequestToken: 344250be-0106-a9da-72cb-8835c953f6ff, HandlerErrorCode: G eneralServiceException)
  ```

- 原因

  - VPC フローログを作成する際に、フローログを作成する`CreateFlowLogs API`の実行に併せて、VPC フローログ格納用 S3 バケットに必要なポリシーを追加する`PutBucketPolicy API`が同じタイミングで実行される。
  - `PutBucketPolicy API`を実行後に必要なポリシーの更新が完了してない状態で`CreateFlowLogs API`が実行された結果、必要なポリシーが設定されておらず当該エラーが発生する。

- 対応策

  - デプロイを再実行する。

## セッションポリシーのサイズ制限エラー

- 発生条件

  - CodePipeline をデプロイ後、パイプラインを実行する時
  - パイプライン周りのリソース名が長い場合
  - ※同じ`PjPrefix`や CDK コードを使用した場合でも、自動作成されるリソースの命名によってエラーが発生しない場合もある。

- エラー内容

  - ターミナル上
    ```sh
    PackedPolicyTooLargeException
    ```
  - CloudTrail での AssumeRole API のエラーメッセージ
    ```sh
    Packed policy consumes 101% of allotted space, please use smaller policy.
    ```

- 原因

  - CodePipeline はアクションを実行する際に、指定された IAM ロールを引き受ける。
  - その際に、セッションポリシーはバイナリ形式にてパッケージ化のうえ自動作成されるが、作成されたセッションポリシーのサイズがクウォータに抵触したため。
  - このセッションポリシーは各種 AWS リソース名を基に作成されるため、リソース名が長すぎると当事象が発生する。

- 対応策

  - 以下の AWS リソースの名前を短縮する。
    - CodeDeploy アプリケーション名
    - デプロイメントグループ名
    - アーティファクトストア S3 バケット名
    - CodePipeline パイプライン名
    - アーティファクト名

## CloudWatch Logs の文字数制限エラー

- 発生条件

  - Elasticache クラスターを dev 環境などの複数回デプロイを繰り返す環境に作成する際

- エラー内容

  ```sh
  Failed to enable log delivery for log type slow-log. Error: Failed to grant access to log group Prodkuri-ElastiCache-ElastiCacheLoggroupFB0C3E33-1HaGrZG9vJ3c. Check the length of the resource policy document.
  ```

- 原因

  - CloudWatch Logs ロググループが作成されるたびに、CloudWatch Logs リソースポリシーにロググループ名が追加されている。  
    CloudWatch Logs リソースポリシーには最大 5120 文字の制限があり、そのクォータに抵触したため。

- 対応策

  - リソースポリシーを削除する。

    ```sh
    aws logs delete-resource-policies --policy-name xxxxx
    ```

  - CloudWatch Logs リソースポリシーの文字数制限に達しないために、CloudWatch Logs ロググループ名の前に`/aws/vendedlogs/`を付ける。

- 参考サイト  
  https://repost.aws/questions/QUEiy_nnlLR4CBK1N561l4Ig/enabling-cloudwatch-slowlogs-and-engine-logs-for-elasticache-redis-cluster
