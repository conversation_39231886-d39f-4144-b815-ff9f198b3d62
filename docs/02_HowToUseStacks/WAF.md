# HowToUseWAF

ここでは、WafAlb スタックと WafCloudFront スタックでデプロイされる WAF の利用方法を記載する

## 概要

- WafAlb と WafCloudFront スタックは、それぞれ ALB または CloudFront にアタッチする WAF Web ACLs (以下 WAF) をデプロイするスタックである。
- 下記の機能を持った WAF をデプロイする。
  - AWS マネージドルール
  - IP セット（許可 IP リスト）
  - Basic 認証 (**※WafCloudFront のみ**)
    - Basic 認証を使用する理由として、プロジェクトに参加している社員に制限するためである。
    - IP 制限のみだと、社内の端末にアクセスできるものが誰でもアクセスできてしまうため。
  - ALB へのアクセス制限
    - CloudFront からのみ ALB へのアクセスを許可するため、WafAlb では CloudFront のカスタムヘッダを利用したアクセス制限を行っている。
    - CloudFront で設定した `x-pre-shared-key` ヘッダの値を Waf でフィルタリングすることで ALB へのアクセス制限を実装している。
- CloudFront はグローバルリージョンのため、WafCloudFront スタックは WAF を us-east-1 (バージニア北部) にデプロイしている。  
  参考：https://docs.aws.amazon.com/ja_jp/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-webacl.html#cfn-wafv2-webacl-scope

## WAF スタックが複数ある理由

- WafCloudFront スタックと WafAlb スタックで WAF をデプロイするスタックが２つ存在する。
- 基本的な機能は同じで、`waf-construct` で WAF の定義をしている。
- ベースコードのテンプレートでは、CloudFront にアクセスする際に WAF で Basic 認証を行う設計になっている。
- Basic 認証の有無で、WafAlb と WafCloudFront でスタックを分けている。

## WafAlb 構成図

WafAlb の構成図については以下の通り

![WafAlb構成図](images/WafAlb-Detail-Architecture.dio.svg)

- ECS App の ALB にアタッチされる。
- WAF のログ情報を S3 バケット に保存する。

## WafCloudFront 構成図

WafCloudFront の構成図については以下の通り

![WafCloudFront構成図](images/WafCloudFront-Detail-Architecture.dio.svg)

- Basic 認証で用いるユーザー名・パスワードを SSM パラメーターストア に保存する。
- WAF のログ情報を S3 バケット に保存する。

## IP セットによる IP アドレス制限

- WAF では設定した IP アドレス以外のアドレスからの通信を制限する機能を提供している。
- `params/` 配下の各環境ファイルに設定した IP アドレス の通信のみ許可している。

### 設定箇所

- `WafParam` または `WafAlbParam` の `allowIpList` に許可する IP アドレスのリストを配列に格納する。
- アクセスが想定されるグローバル IP アドレスを CIDR 形式で記述する必要がある。

**`WafParam`のサンプルコード**

```typescript
export const WafAlbParam: inf.IWafParam = {
  allowIPList: ['xxx.xxx.xxx.xxx/16'],
  preSharedKey: 'pre-string-for-preSharedKey',
};
```
