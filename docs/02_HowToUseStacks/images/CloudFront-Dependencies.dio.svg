<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="441px" height="691px" viewBox="-0.5 -0.5 441 691" content="&lt;mxfile&gt;&lt;diagram id=&quot;U8jFnNACYYQc-wtoAfAj&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="0" width="440" height="690" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/>
        <rect x="10" y="378" width="410" height="300" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="10" y="30" width="410" height="300" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="304" y="568" width="100" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 305.36 594.49 L 305.36 588.45 L 311.39 588.45 L 311.39 594.49 Z M 304.07 595.78 L 312.68 595.78 L 312.68 587.16 L 304.07 587.16 Z M 314.94 594.49 L 314.94 588.45 L 321.54 588.45 L 321.54 594.49 Z M 313.65 595.78 L 322.83 595.78 L 322.83 587.16 L 313.65 587.16 Z M 325.08 594.49 L 325.08 588.45 L 331.12 588.45 L 331.12 594.49 Z M 323.79 595.78 L 332.41 595.78 L 332.41 587.16 L 323.79 587.16 Z M 305.36 584.91 L 305.36 578.87 L 316.46 578.87 L 316.46 584.91 Z M 304.07 586.2 L 317.75 586.2 L 317.75 577.58 L 304.07 577.58 Z M 320.01 584.91 L 320.01 578.87 L 331.12 578.87 L 331.12 584.91 Z M 318.72 586.2 L 332.41 586.2 L 332.41 577.58 L 318.72 577.58 Z M 305.36 575.33 L 305.36 569.29 L 331.12 569.29 L 331.12 575.33 Z M 304.07 576.62 L 332.41 576.62 L 332.41 568 L 304.07 568 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 582px; margin-left: 333px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;" color="#010409" face="-apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji">
                                    <span style="font-size: 10px;">
                                        waf-alb
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="368" y="585" fill="#000000" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    waf-alb
                </text>
            </switch>
        </g>
        <path d="M 332.33 610.22 L 375.67 610.22 L 375.67 653.56 L 332.33 653.56 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 338.15 632.51 L 336.67 632.51 L 336.67 631.27 L 338.15 631.27 C 338.28 628.14 339.29 625.16 341.13 622.62 L 342.13 623.34 C 340.45 625.67 339.51 628.4 339.39 631.27 L 341 631.27 L 341 632.51 L 339.39 632.51 C 339.5 635.39 340.44 638.14 342.13 640.48 L 341.13 641.2 C 339.28 638.65 338.27 635.66 338.15 632.51 Z M 363.29 644.78 C 360.75 646.63 357.76 647.64 354.62 647.76 L 354.62 649.22 L 353.38 649.22 L 353.38 647.76 C 350.24 647.64 347.25 646.63 344.71 644.78 L 345.43 643.78 C 347.77 645.47 350.5 646.4 353.38 646.52 L 353.38 644.89 L 354.62 644.89 L 354.62 646.52 C 357.5 646.4 360.23 645.47 362.57 643.78 Z M 344.71 619.04 C 347.25 617.2 350.24 616.18 353.38 616.06 L 353.38 614.56 L 354.62 614.56 L 354.62 616.06 C 357.76 616.18 360.75 617.2 363.29 619.04 L 362.57 620.04 C 360.23 618.36 357.5 617.42 354.62 617.3 L 354.62 618.89 L 353.38 618.89 L 353.38 617.3 C 350.5 617.42 347.77 618.36 345.43 620.04 Z M 371.33 631.27 L 371.33 632.51 L 369.85 632.51 C 369.73 635.66 368.72 638.65 366.87 641.2 L 365.87 640.48 C 367.56 638.14 368.5 635.39 368.61 632.51 L 367 632.51 L 367 631.27 L 368.61 631.27 C 368.49 628.4 367.55 625.67 365.87 623.34 L 366.87 622.62 C 368.71 625.16 369.72 628.14 369.85 631.27 Z M 363.27 621.77 L 368.12 616.91 L 369 617.79 L 364.14 622.64 Z M 344.73 642.06 L 339.88 646.91 L 339 646.03 L 343.86 641.18 Z M 346.11 624.87 L 336.92 615.69 L 337.8 614.81 L 346.98 624 Z M 361.87 638.88 L 371.08 648.09 L 370.2 648.97 L 360.99 639.76 Z M 348.84 632.34 C 348.89 632.26 348.94 632.18 348.99 632.1 C 350.03 630.46 349.73 628.23 349.4 626.88 C 350.29 627.47 351.1 628.68 351.37 629.19 C 351.49 629.4 351.72 629.53 351.96 629.51 C 352.21 629.49 352.42 629.33 352.5 629.1 C 353.42 626.48 352.95 624.51 352.3 623.24 C 353.09 623.7 353.7 624.36 354.11 625.2 C 354.98 627.02 354.79 629.51 353.6 631.7 C 351.97 634.7 352.3 637.84 352.67 639.52 C 351.74 639.12 350.91 638.67 350.2 638.17 C 348.32 636.87 347.73 634.31 348.84 632.34 Z M 357.15 632.14 C 357.11 632.39 357.23 632.64 357.45 632.77 C 357.67 632.9 357.95 632.88 358.15 632.72 C 358.2 632.68 359.22 631.84 359.71 629.81 C 360.3 630.67 360.92 632.41 360.1 635.61 C 359.28 638.77 355.41 639.65 354.03 639.87 C 353.7 638.68 353.02 635.35 354.68 632.29 C 355.87 630.12 356.18 627.66 355.59 625.64 C 356.71 626.95 357.65 629.02 357.15 632.14 Z M 347.76 631.73 C 346.34 634.25 347.09 637.52 349.49 639.19 C 350.57 639.94 351.88 640.6 353.39 641.14 C 353.46 641.16 353.53 641.17 353.6 641.17 C 353.61 641.17 353.62 641.17 353.63 641.17 L 353.63 641.17 C 353.9 641.15 360.08 640.61 361.3 635.91 C 362.86 629.87 359.73 628.08 359.6 628 C 359.42 627.9 359.2 627.9 359.01 628 C 358.83 628.09 358.7 628.27 358.68 628.48 C 358.64 628.9 358.57 629.27 358.49 629.6 C 358.06 624.85 354.3 622.92 353.54 622.58 C 352.76 621.98 351.81 621.57 350.71 621.37 C 350.43 621.32 350.16 621.47 350.04 621.72 C 349.92 621.98 349.99 622.28 350.2 622.46 C 350.3 622.54 352.23 624.21 351.67 627.39 C 350.96 626.45 349.81 625.31 348.47 625.31 C 348.26 625.31 348.07 625.42 347.96 625.58 C 347.84 625.75 347.82 625.97 347.89 626.16 C 347.9 626.19 349.16 629.52 347.94 631.43 C 347.88 631.53 347.82 631.63 347.76 631.73 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <rect x="304" y="391" width="100" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 305.36 417.49 L 305.36 411.45 L 311.39 411.45 L 311.39 417.49 Z M 304.07 418.78 L 312.68 418.78 L 312.68 410.16 L 304.07 410.16 Z M 314.94 417.49 L 314.94 411.45 L 321.54 411.45 L 321.54 417.49 Z M 313.65 418.78 L 322.83 418.78 L 322.83 410.16 L 313.65 410.16 Z M 325.08 417.49 L 325.08 411.45 L 331.12 411.45 L 331.12 417.49 Z M 323.79 418.78 L 332.41 418.78 L 332.41 410.16 L 323.79 410.16 Z M 305.36 407.91 L 305.36 401.87 L 316.46 401.87 L 316.46 407.91 Z M 304.07 409.2 L 317.75 409.2 L 317.75 400.58 L 304.07 400.58 Z M 320.01 407.91 L 320.01 401.87 L 331.12 401.87 L 331.12 407.91 Z M 318.72 409.2 L 332.41 409.2 L 332.41 400.58 L 318.72 400.58 Z M 305.36 398.33 L 305.36 392.29 L 331.12 392.29 L 331.12 398.33 Z M 304.07 399.62 L 332.41 399.62 L 332.41 391 L 304.07 391 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 405px; margin-left: 333px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;" color="#010409" face="-apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji">
                                    <span style="font-size: 10px;">
                                        cloudfront
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="368" y="408" fill="#000000" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    cloudfront
                </text>
            </switch>
        </g>
        <path d="M 332.33 433.22 L 375.67 433.22 L 375.67 476.56 L 332.33 476.56 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 363.36 462.39 C 363.36 461.37 362.53 460.54 361.5 460.54 C 360.48 460.54 359.65 461.37 359.65 462.39 C 359.65 463.42 360.48 464.25 361.5 464.25 C 362.53 464.25 363.36 463.42 363.36 462.39 Z M 364.6 462.39 C 364.6 464.1 363.21 465.49 361.5 465.49 C 359.8 465.49 358.41 464.1 358.41 462.39 C 358.41 460.69 359.8 459.3 361.5 459.3 C 363.21 459.3 364.6 460.69 364.6 462.39 Z M 348.05 453.46 C 348.05 452.44 347.22 451.6 346.2 451.6 C 345.17 451.6 344.34 452.44 344.34 453.46 C 344.34 454.48 345.17 455.32 346.2 455.32 C 347.22 455.32 348.05 454.48 348.05 453.46 Z M 349.29 453.46 C 349.29 455.17 347.9 456.56 346.2 456.56 C 344.49 456.56 343.1 455.17 343.1 453.46 C 343.1 451.75 344.49 450.36 346.2 450.36 C 347.9 450.36 349.29 451.75 349.29 453.46 Z M 353.83 443.75 C 353.83 444.77 354.67 445.61 355.69 445.61 C 356.71 445.61 357.55 444.77 357.55 443.75 C 357.55 442.73 356.71 441.89 355.69 441.89 C 354.67 441.89 353.83 442.73 353.83 443.75 Z M 352.59 443.75 C 352.59 442.04 353.98 440.66 355.69 440.66 C 357.4 440.66 358.79 442.04 358.79 443.75 C 358.79 445.46 357.4 446.85 355.69 446.85 C 353.98 446.85 352.59 445.46 352.59 443.75 Z M 370.1 454.89 C 370.1 449.15 367.02 443.84 362.06 440.97 C 361.17 441.15 360.31 441.39 359.23 441.78 L 358.81 440.61 C 359.37 440.41 359.87 440.25 360.36 440.11 C 358.36 439.25 356.2 438.79 354 438.79 C 352.95 438.79 351.93 438.9 350.92 439.1 C 351.65 439.52 352.29 439.95 352.92 440.42 L 352.17 441.41 C 351.29 440.74 350.37 440.17 349.17 439.54 C 343.09 441.45 338.71 446.82 338.02 453.11 C 339.29 452.85 340.52 452.71 341.88 452.68 L 341.91 453.92 C 340.48 453.95 339.26 454.1 337.92 454.4 C 337.92 454.56 337.9 454.73 337.9 454.89 C 337.9 460.25 340.56 465.19 344.92 468.17 C 344.14 465.86 343.75 463.67 343.75 461.54 C 343.75 460.33 343.96 459.33 344.19 458.27 C 344.24 458.03 344.29 457.78 344.34 457.52 L 345.55 457.76 C 345.5 458.02 345.45 458.28 345.4 458.52 C 345.18 459.56 344.99 460.45 344.99 461.54 C 344.99 463.96 345.52 466.46 346.6 469.18 C 348.91 470.37 351.39 470.98 354 470.98 C 355.71 470.98 357.37 470.71 358.96 470.19 C 359.58 468.96 360.05 467.8 360.43 466.46 L 361.62 466.81 C 361.34 467.78 361.02 468.66 360.63 469.55 C 361.63 469.1 362.57 468.54 363.46 467.89 C 363.25 467.37 363.02 466.85 362.77 466.34 L 363.87 465.79 C 364.09 466.22 364.29 466.66 364.47 467.1 C 368.06 464.04 370.1 459.63 370.1 454.89 Z M 371.33 454.89 C 371.33 460.29 368.88 465.29 364.6 468.6 C 363.54 469.42 362.39 470.11 361.17 470.66 C 360.66 470.89 360.13 471.11 359.59 471.29 C 357.81 471.91 355.93 472.22 354 472.22 C 351.15 472.22 348.32 471.51 345.82 470.17 C 340.17 467.15 336.67 461.29 336.67 454.89 C 336.67 454.46 336.68 454.14 336.7 453.84 C 337.12 446.61 342.09 440.35 349.05 438.28 C 350.63 437.8 352.3 437.56 354 437.56 C 356.98 437.56 359.91 438.32 362.47 439.77 C 367.94 442.83 371.33 448.63 371.33 454.89 Z M 352.34 445.89 L 351.52 444.96 C 350.14 446.17 349.06 447.45 347.8 449.4 L 348.83 450.07 C 350.03 448.23 351.04 447.02 352.34 445.89 Z M 350.46 453.92 L 350.05 455.09 C 352.9 456.06 355.38 457.62 357.86 459.99 L 358.71 459.09 C 356.1 456.6 353.47 454.95 350.46 453.92 Z M 358.73 446.64 C 361.06 450.18 362.36 454.07 362.62 458.19 L 361.38 458.27 C 361.14 454.36 359.9 450.67 357.7 447.31 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <rect x="27" y="391" width="100" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 28.36 417.49 L 28.36 411.45 L 34.39 411.45 L 34.39 417.49 Z M 27.07 418.78 L 35.68 418.78 L 35.68 410.16 L 27.07 410.16 Z M 37.94 417.49 L 37.94 411.45 L 44.54 411.45 L 44.54 417.49 Z M 36.65 418.78 L 45.83 418.78 L 45.83 410.16 L 36.65 410.16 Z M 48.08 417.49 L 48.08 411.45 L 54.12 411.45 L 54.12 417.49 Z M 46.79 418.78 L 55.41 418.78 L 55.41 410.16 L 46.79 410.16 Z M 28.36 407.91 L 28.36 401.87 L 39.46 401.87 L 39.46 407.91 Z M 27.07 409.2 L 40.75 409.2 L 40.75 400.58 L 27.07 400.58 Z M 43.01 407.91 L 43.01 401.87 L 54.12 401.87 L 54.12 407.91 Z M 41.72 409.2 L 55.41 409.2 L 55.41 400.58 L 41.72 400.58 Z M 28.36 398.33 L 28.36 392.29 L 54.12 392.29 L 54.12 398.33 Z M 27.07 399.62 L 55.41 399.62 L 55.41 391 L 27.07 391 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 405px; margin-left: 56px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;" color="#010409" face="-apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji">
                                    <span style="font-size: 10px;">
                                        waf-cloudfront
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="91" y="408" fill="#000000" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    waf-cloudfront
                </text>
            </switch>
        </g>
        <path d="M 55.47 433.22 L 98.81 433.22 L 98.81 476.56 L 55.47 476.56 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 61.29 455.51 L 59.81 455.51 L 59.81 454.27 L 61.29 454.27 C 61.41 451.14 62.43 448.16 64.27 445.62 L 65.27 446.34 C 63.59 448.67 62.65 451.4 62.53 454.27 L 64.14 454.27 L 64.14 455.51 L 62.53 455.51 C 62.64 458.39 63.58 461.14 65.27 463.48 L 64.27 464.2 C 62.42 461.65 61.4 458.66 61.29 455.51 Z M 86.43 467.78 C 83.89 469.63 80.9 470.64 77.76 470.76 L 77.76 472.22 L 76.52 472.22 L 76.52 470.76 C 73.38 470.64 70.39 469.63 67.85 467.78 L 68.57 466.78 C 70.91 468.47 73.64 469.4 76.52 469.52 L 76.52 467.89 L 77.76 467.89 L 77.76 469.52 C 80.64 469.4 83.37 468.47 85.71 466.78 Z M 67.85 442.04 C 70.39 440.2 73.38 439.18 76.52 439.06 L 76.52 437.56 L 77.76 437.56 L 77.76 439.06 C 80.9 439.18 83.89 440.2 86.43 442.04 L 85.71 443.04 C 83.37 441.36 80.64 440.42 77.76 440.3 L 77.76 441.89 L 76.52 441.89 L 76.52 440.3 C 73.64 440.42 70.91 441.36 68.57 443.04 Z M 94.47 454.27 L 94.47 455.51 L 92.99 455.51 C 92.87 458.66 91.86 461.65 90.01 464.2 L 89.01 463.48 C 90.7 461.14 91.64 458.39 91.75 455.51 L 90.14 455.51 L 90.14 454.27 L 91.75 454.27 C 91.62 451.4 90.69 448.67 89.01 446.34 L 90.01 445.62 C 91.85 448.16 92.86 451.14 92.99 454.27 Z M 86.41 444.77 L 91.26 439.91 L 92.14 440.79 L 87.28 445.64 Z M 67.87 465.06 L 63.02 469.91 L 62.14 469.03 L 66.99 464.18 Z M 69.25 447.87 L 60.06 438.69 L 60.94 437.81 L 70.12 447 Z M 85.01 461.88 L 94.22 471.09 L 93.34 471.97 L 84.13 462.76 Z M 71.98 455.34 C 72.02 455.26 72.08 455.18 72.13 455.1 C 73.17 453.46 72.87 451.23 72.54 449.88 C 73.43 450.47 74.24 451.68 74.51 452.19 C 74.63 452.4 74.85 452.53 75.1 452.51 C 75.35 452.49 75.56 452.33 75.64 452.1 C 76.56 449.48 76.09 447.51 75.44 446.24 C 76.23 446.7 76.84 447.36 77.25 448.2 C 78.12 450.02 77.93 452.51 76.74 454.7 C 75.11 457.7 75.43 460.84 75.8 462.52 C 74.88 462.12 74.05 461.67 73.33 461.17 C 71.46 459.87 70.86 457.31 71.98 455.34 Z M 80.29 455.14 C 80.25 455.39 80.37 455.64 80.59 455.77 C 80.81 455.9 81.09 455.88 81.29 455.72 C 81.34 455.68 82.36 454.84 82.85 452.81 C 83.44 453.67 84.06 455.41 83.24 458.61 C 82.42 461.77 78.55 462.65 77.17 462.87 C 76.84 461.68 76.16 458.35 77.82 455.29 C 79 453.12 79.32 450.66 78.73 448.64 C 79.85 449.95 80.79 452.02 80.29 455.14 Z M 70.9 454.73 C 69.48 457.25 70.23 460.52 72.63 462.19 C 73.71 462.94 75.02 463.6 76.53 464.14 C 76.59 464.16 76.66 464.17 76.73 464.17 C 76.75 464.17 76.76 464.17 76.77 464.17 L 76.77 464.17 C 77.03 464.15 83.22 463.61 84.43 458.91 C 86 452.87 82.87 451.08 82.74 451 C 82.55 450.9 82.33 450.9 82.15 451 C 81.97 451.09 81.84 451.27 81.82 451.48 C 81.78 451.9 81.71 452.27 81.63 452.6 C 81.2 447.85 77.44 445.92 76.67 445.58 C 75.89 444.98 74.95 444.57 73.85 444.37 C 73.57 444.32 73.3 444.47 73.18 444.72 C 73.06 444.98 73.13 445.28 73.34 445.46 C 73.44 445.54 75.36 447.21 74.81 450.39 C 74.1 449.45 72.95 448.31 71.61 448.31 C 71.4 448.31 71.21 448.42 71.1 448.58 C 70.98 448.75 70.95 448.97 71.03 449.16 C 71.04 449.19 72.3 452.52 71.08 454.43 C 71.02 454.53 70.96 454.63 70.9 454.73 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <rect x="27" y="568" width="100" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 28.36 594.49 L 28.36 588.45 L 34.39 588.45 L 34.39 594.49 Z M 27.07 595.78 L 35.68 595.78 L 35.68 587.16 L 27.07 587.16 Z M 37.94 594.49 L 37.94 588.45 L 44.54 588.45 L 44.54 594.49 Z M 36.65 595.78 L 45.83 595.78 L 45.83 587.16 L 36.65 587.16 Z M 48.08 594.49 L 48.08 588.45 L 54.12 588.45 L 54.12 594.49 Z M 46.79 595.78 L 55.41 595.78 L 55.41 587.16 L 46.79 587.16 Z M 28.36 584.91 L 28.36 578.87 L 39.46 578.87 L 39.46 584.91 Z M 27.07 586.2 L 40.75 586.2 L 40.75 577.58 L 27.07 577.58 Z M 43.01 584.91 L 43.01 578.87 L 54.12 578.87 L 54.12 584.91 Z M 41.72 586.2 L 55.41 586.2 L 55.41 577.58 L 41.72 577.58 Z M 28.36 575.33 L 28.36 569.29 L 54.12 569.29 L 54.12 575.33 Z M 27.07 576.62 L 55.41 576.62 L 55.41 568 L 27.07 568 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 582px; margin-left: 56px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;" color="#010409" face="-apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji">
                                    <span style="font-size: 10px;">
                                        ecs-app
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="91" y="585" fill="#000000" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    ecs-app
                </text>
            </switch>
        </g>
        <path d="M 55.33 610.22 L 98.67 610.22 L 98.67 653.56 L 55.33 653.56 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 91.28 636.6 L 86.42 633.69 L 86.42 626.74 C 86.42 626.52 86.31 626.32 86.12 626.21 L 79.13 622.13 L 79.13 616.26 L 91.28 623.44 Z M 92.2 622.57 L 78.83 614.67 C 78.64 614.56 78.41 614.56 78.22 614.66 C 78.03 614.77 77.91 614.97 77.91 615.19 L 77.91 622.48 C 77.91 622.7 78.03 622.9 78.21 623.01 L 85.2 627.09 L 85.2 634.03 C 85.2 634.24 85.32 634.44 85.5 634.55 L 91.58 638.2 C 91.67 638.25 91.78 638.28 91.89 638.28 C 91.99 638.28 92.1 638.26 92.19 638.2 C 92.38 638.1 92.5 637.89 92.5 637.68 L 92.5 623.09 C 92.5 622.88 92.38 622.68 92.2 622.57 Z M 76.97 647.92 L 62.72 640.35 L 62.72 623.44 L 74.87 616.26 L 74.87 622.15 L 68.47 626.22 C 68.29 626.34 68.19 626.53 68.19 626.74 L 68.19 637.07 C 68.19 637.29 68.31 637.5 68.52 637.61 L 76.69 641.86 C 76.87 641.95 77.08 641.95 77.25 641.86 L 85.18 637.76 L 90.06 640.69 Z M 91.59 640.19 L 85.52 636.55 C 85.34 636.44 85.11 636.43 84.92 636.53 L 76.97 640.64 L 69.4 636.7 L 69.4 627.07 L 75.81 623 C 75.98 622.88 76.09 622.69 76.09 622.48 L 76.09 615.19 C 76.09 614.97 75.97 614.77 75.78 614.66 C 75.59 614.56 75.36 614.56 75.17 614.67 L 61.8 622.57 C 61.62 622.68 61.5 622.88 61.5 623.09 L 61.5 640.71 C 61.5 640.94 61.63 641.15 61.83 641.25 L 76.69 649.15 C 76.78 649.2 76.87 649.22 76.97 649.22 C 77.07 649.22 77.17 649.2 77.27 649.15 L 91.57 641.25 C 91.77 641.14 91.88 640.94 91.89 640.73 C 91.89 640.51 91.78 640.3 91.59 640.19 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <path d="M 304 441 L 130 441" fill="none" stroke="#df2e35" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <ellipse cx="127" cy="441" rx="3" ry="3" fill="#df2e35" stroke="#df2e35" stroke-width="2" pointer-events="none"/>
        <path d="M 304 618 L 130 618" fill="none" stroke="#e97b0c" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <ellipse cx="127" cy="618" rx="3" ry="3" fill="#e97b0c" stroke="#e97b0c" stroke-width="2" pointer-events="none"/>
        <rect x="304" y="219" width="100" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 305.36 245.49 L 305.36 239.45 L 311.39 239.45 L 311.39 245.49 Z M 304.07 246.78 L 312.68 246.78 L 312.68 238.16 L 304.07 238.16 Z M 314.94 245.49 L 314.94 239.45 L 321.54 239.45 L 321.54 245.49 Z M 313.65 246.78 L 322.83 246.78 L 322.83 238.16 L 313.65 238.16 Z M 325.08 245.49 L 325.08 239.45 L 331.12 239.45 L 331.12 245.49 Z M 323.79 246.78 L 332.41 246.78 L 332.41 238.16 L 323.79 238.16 Z M 305.36 235.91 L 305.36 229.87 L 316.46 229.87 L 316.46 235.91 Z M 304.07 237.2 L 317.75 237.2 L 317.75 228.58 L 304.07 228.58 Z M 320.01 235.91 L 320.01 229.87 L 331.12 229.87 L 331.12 235.91 Z M 318.72 237.2 L 332.41 237.2 L 332.41 228.58 L 318.72 228.58 Z M 305.36 226.33 L 305.36 220.29 L 331.12 220.29 L 331.12 226.33 Z M 304.07 227.62 L 332.41 227.62 L 332.41 219 L 304.07 219 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 233px; margin-left: 333px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;" color="#010409" face="-apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji">
                                    <span style="font-size: 10px;">
                                        waf-alb
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="368" y="236" fill="#000000" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    waf-alb
                </text>
            </switch>
        </g>
        <path d="M 332.33 261.22 L 375.67 261.22 L 375.67 304.56 L 332.33 304.56 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 338.15 283.51 L 336.67 283.51 L 336.67 282.27 L 338.15 282.27 C 338.28 279.14 339.29 276.16 341.13 273.62 L 342.13 274.34 C 340.45 276.67 339.51 279.4 339.39 282.27 L 341 282.27 L 341 283.51 L 339.39 283.51 C 339.5 286.39 340.44 289.14 342.13 291.48 L 341.13 292.2 C 339.28 289.65 338.27 286.66 338.15 283.51 Z M 363.29 295.78 C 360.75 297.63 357.76 298.64 354.62 298.76 L 354.62 300.22 L 353.38 300.22 L 353.38 298.76 C 350.24 298.64 347.25 297.63 344.71 295.78 L 345.43 294.78 C 347.77 296.47 350.5 297.4 353.38 297.52 L 353.38 295.89 L 354.62 295.89 L 354.62 297.52 C 357.5 297.4 360.23 296.47 362.57 294.78 Z M 344.71 270.04 C 347.25 268.2 350.24 267.18 353.38 267.06 L 353.38 265.56 L 354.62 265.56 L 354.62 267.06 C 357.76 267.18 360.75 268.2 363.29 270.04 L 362.57 271.04 C 360.23 269.36 357.5 268.42 354.62 268.3 L 354.62 269.89 L 353.38 269.89 L 353.38 268.3 C 350.5 268.42 347.77 269.36 345.43 271.04 Z M 371.33 282.27 L 371.33 283.51 L 369.85 283.51 C 369.73 286.66 368.72 289.65 366.87 292.2 L 365.87 291.48 C 367.56 289.14 368.5 286.39 368.61 283.51 L 367 283.51 L 367 282.27 L 368.61 282.27 C 368.49 279.4 367.55 276.67 365.87 274.34 L 366.87 273.62 C 368.71 276.16 369.72 279.14 369.85 282.27 Z M 363.27 272.77 L 368.12 267.91 L 369 268.79 L 364.14 273.64 Z M 344.73 293.06 L 339.88 297.91 L 339 297.03 L 343.86 292.18 Z M 346.11 275.87 L 336.92 266.69 L 337.8 265.81 L 346.98 275 Z M 361.87 289.88 L 371.08 299.09 L 370.2 299.97 L 360.99 290.76 Z M 348.84 283.34 C 348.89 283.26 348.94 283.18 348.99 283.1 C 350.03 281.46 349.73 279.23 349.4 277.88 C 350.29 278.47 351.1 279.68 351.37 280.19 C 351.49 280.4 351.72 280.53 351.96 280.51 C 352.21 280.49 352.42 280.33 352.5 280.1 C 353.42 277.48 352.95 275.51 352.3 274.24 C 353.09 274.7 353.7 275.36 354.11 276.2 C 354.98 278.02 354.79 280.51 353.6 282.7 C 351.97 285.7 352.3 288.84 352.67 290.52 C 351.74 290.12 350.91 289.67 350.2 289.17 C 348.32 287.87 347.73 285.31 348.84 283.34 Z M 357.15 283.14 C 357.11 283.39 357.23 283.64 357.45 283.77 C 357.67 283.9 357.95 283.88 358.15 283.72 C 358.2 283.68 359.22 282.84 359.71 280.81 C 360.3 281.67 360.92 283.41 360.1 286.61 C 359.28 289.77 355.41 290.65 354.03 290.87 C 353.7 289.68 353.02 286.35 354.68 283.29 C 355.87 281.12 356.18 278.66 355.59 276.64 C 356.71 277.95 357.65 280.02 357.15 283.14 Z M 347.76 282.73 C 346.34 285.25 347.09 288.52 349.49 290.19 C 350.57 290.94 351.88 291.6 353.39 292.14 C 353.46 292.16 353.53 292.17 353.6 292.17 C 353.61 292.17 353.62 292.17 353.63 292.17 L 353.63 292.17 C 353.9 292.15 360.08 291.61 361.3 286.91 C 362.86 280.87 359.73 279.08 359.6 279 C 359.42 278.9 359.2 278.9 359.01 279 C 358.83 279.09 358.7 279.27 358.68 279.48 C 358.64 279.9 358.57 280.27 358.49 280.6 C 358.06 275.85 354.3 273.92 353.54 273.58 C 352.76 272.98 351.81 272.57 350.71 272.37 C 350.43 272.32 350.16 272.47 350.04 272.72 C 349.92 272.98 349.99 273.28 350.2 273.46 C 350.3 273.54 352.23 275.21 351.67 278.39 C 350.96 277.45 349.81 276.31 348.47 276.31 C 348.26 276.31 348.07 276.42 347.96 276.58 C 347.84 276.75 347.82 276.97 347.89 277.16 C 347.9 277.19 349.16 280.52 347.94 282.43 C 347.88 282.53 347.82 282.63 347.76 282.73 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <rect x="304" y="42" width="100" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 305.36 68.49 L 305.36 62.45 L 311.39 62.45 L 311.39 68.49 Z M 304.07 69.78 L 312.68 69.78 L 312.68 61.16 L 304.07 61.16 Z M 314.94 68.49 L 314.94 62.45 L 321.54 62.45 L 321.54 68.49 Z M 313.65 69.78 L 322.83 69.78 L 322.83 61.16 L 313.65 61.16 Z M 325.08 68.49 L 325.08 62.45 L 331.12 62.45 L 331.12 68.49 Z M 323.79 69.78 L 332.41 69.78 L 332.41 61.16 L 323.79 61.16 Z M 305.36 58.91 L 305.36 52.87 L 316.46 52.87 L 316.46 58.91 Z M 304.07 60.2 L 317.75 60.2 L 317.75 51.58 L 304.07 51.58 Z M 320.01 58.91 L 320.01 52.87 L 331.12 52.87 L 331.12 58.91 Z M 318.72 60.2 L 332.41 60.2 L 332.41 51.58 L 318.72 51.58 Z M 305.36 49.33 L 305.36 43.29 L 331.12 43.29 L 331.12 49.33 Z M 304.07 50.62 L 332.41 50.62 L 332.41 42 L 304.07 42 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 56px; margin-left: 333px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;" color="#010409" face="-apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji">
                                    <span style="font-size: 10px;">
                                        cloudfront
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="368" y="59" fill="#000000" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    cloudfront
                </text>
            </switch>
        </g>
        <path d="M 332.33 84.22 L 375.67 84.22 L 375.67 127.56 L 332.33 127.56 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 363.36 113.39 C 363.36 112.37 362.53 111.54 361.5 111.54 C 360.48 111.54 359.65 112.37 359.65 113.39 C 359.65 114.42 360.48 115.25 361.5 115.25 C 362.53 115.25 363.36 114.42 363.36 113.39 Z M 364.6 113.39 C 364.6 115.1 363.21 116.49 361.5 116.49 C 359.8 116.49 358.41 115.1 358.41 113.39 C 358.41 111.69 359.8 110.3 361.5 110.3 C 363.21 110.3 364.6 111.69 364.6 113.39 Z M 348.05 104.46 C 348.05 103.44 347.22 102.6 346.2 102.6 C 345.17 102.6 344.34 103.44 344.34 104.46 C 344.34 105.48 345.17 106.32 346.2 106.32 C 347.22 106.32 348.05 105.48 348.05 104.46 Z M 349.29 104.46 C 349.29 106.17 347.9 107.56 346.2 107.56 C 344.49 107.56 343.1 106.17 343.1 104.46 C 343.1 102.75 344.49 101.36 346.2 101.36 C 347.9 101.36 349.29 102.75 349.29 104.46 Z M 353.83 94.75 C 353.83 95.77 354.67 96.61 355.69 96.61 C 356.71 96.61 357.55 95.77 357.55 94.75 C 357.55 93.73 356.71 92.89 355.69 92.89 C 354.67 92.89 353.83 93.73 353.83 94.75 Z M 352.59 94.75 C 352.59 93.04 353.98 91.66 355.69 91.66 C 357.4 91.66 358.79 93.04 358.79 94.75 C 358.79 96.46 357.4 97.85 355.69 97.85 C 353.98 97.85 352.59 96.46 352.59 94.75 Z M 370.1 105.89 C 370.1 100.15 367.02 94.84 362.06 91.97 C 361.17 92.15 360.31 92.39 359.23 92.78 L 358.81 91.61 C 359.37 91.41 359.87 91.25 360.36 91.11 C 358.36 90.25 356.2 89.79 354 89.79 C 352.95 89.79 351.93 89.9 350.92 90.1 C 351.65 90.52 352.29 90.95 352.92 91.42 L 352.17 92.41 C 351.29 91.74 350.37 91.17 349.17 90.54 C 343.09 92.45 338.71 97.82 338.02 104.11 C 339.29 103.85 340.52 103.71 341.88 103.68 L 341.91 104.92 C 340.48 104.95 339.26 105.1 337.92 105.4 C 337.92 105.56 337.9 105.73 337.9 105.89 C 337.9 111.25 340.56 116.19 344.92 119.17 C 344.14 116.86 343.75 114.67 343.75 112.54 C 343.75 111.33 343.96 110.33 344.19 109.27 C 344.24 109.03 344.29 108.78 344.34 108.52 L 345.55 108.76 C 345.5 109.02 345.45 109.28 345.4 109.52 C 345.18 110.56 344.99 111.45 344.99 112.54 C 344.99 114.96 345.52 117.46 346.6 120.18 C 348.91 121.37 351.39 121.98 354 121.98 C 355.71 121.98 357.37 121.71 358.96 121.19 C 359.58 119.96 360.05 118.8 360.43 117.46 L 361.62 117.81 C 361.34 118.78 361.02 119.66 360.63 120.55 C 361.63 120.1 362.57 119.54 363.46 118.89 C 363.25 118.37 363.02 117.85 362.77 117.34 L 363.87 116.79 C 364.09 117.22 364.29 117.66 364.47 118.1 C 368.06 115.04 370.1 110.63 370.1 105.89 Z M 371.33 105.89 C 371.33 111.29 368.88 116.29 364.6 119.6 C 363.54 120.42 362.39 121.11 361.17 121.66 C 360.66 121.89 360.13 122.11 359.59 122.29 C 357.81 122.91 355.93 123.22 354 123.22 C 351.15 123.22 348.32 122.51 345.82 121.17 C 340.17 118.15 336.67 112.29 336.67 105.89 C 336.67 105.46 336.68 105.14 336.7 104.84 C 337.12 97.61 342.09 91.35 349.05 89.28 C 350.63 88.8 352.3 88.56 354 88.56 C 356.98 88.56 359.91 89.32 362.47 90.77 C 367.94 93.83 371.33 99.63 371.33 105.89 Z M 352.34 96.89 L 351.52 95.96 C 350.14 97.17 349.06 98.45 347.8 100.4 L 348.83 101.07 C 350.03 99.23 351.04 98.02 352.34 96.89 Z M 350.46 104.92 L 350.05 106.09 C 352.9 107.06 355.38 108.62 357.86 110.99 L 358.71 110.09 C 356.1 107.6 353.47 105.95 350.46 104.92 Z M 358.73 97.64 C 361.06 101.18 362.36 105.07 362.62 109.19 L 361.38 109.27 C 361.14 105.36 359.9 101.67 357.7 98.31 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <rect x="27" y="42" width="100" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 28.36 68.49 L 28.36 62.45 L 34.39 62.45 L 34.39 68.49 Z M 27.07 69.78 L 35.68 69.78 L 35.68 61.16 L 27.07 61.16 Z M 37.94 68.49 L 37.94 62.45 L 44.54 62.45 L 44.54 68.49 Z M 36.65 69.78 L 45.83 69.78 L 45.83 61.16 L 36.65 61.16 Z M 48.08 68.49 L 48.08 62.45 L 54.12 62.45 L 54.12 68.49 Z M 46.79 69.78 L 55.41 69.78 L 55.41 61.16 L 46.79 61.16 Z M 28.36 58.91 L 28.36 52.87 L 39.46 52.87 L 39.46 58.91 Z M 27.07 60.2 L 40.75 60.2 L 40.75 51.58 L 27.07 51.58 Z M 43.01 58.91 L 43.01 52.87 L 54.12 52.87 L 54.12 58.91 Z M 41.72 60.2 L 55.41 60.2 L 55.41 51.58 L 41.72 51.58 Z M 28.36 49.33 L 28.36 43.29 L 54.12 43.29 L 54.12 49.33 Z M 27.07 50.62 L 55.41 50.62 L 55.41 42 L 27.07 42 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 56px; margin-left: 56px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;" color="#010409" face="-apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji">
                                    <span style="font-size: 10px;">
                                        waf-cloudfront
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="91" y="59" fill="#000000" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    waf-cloudfront
                </text>
            </switch>
        </g>
        <path d="M 55.47 84.22 L 98.81 84.22 L 98.81 127.56 L 55.47 127.56 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 61.29 106.51 L 59.81 106.51 L 59.81 105.27 L 61.29 105.27 C 61.41 102.14 62.43 99.16 64.27 96.62 L 65.27 97.34 C 63.59 99.67 62.65 102.4 62.53 105.27 L 64.14 105.27 L 64.14 106.51 L 62.53 106.51 C 62.64 109.39 63.58 112.14 65.27 114.48 L 64.27 115.2 C 62.42 112.65 61.4 109.66 61.29 106.51 Z M 86.43 118.78 C 83.89 120.63 80.9 121.64 77.76 121.76 L 77.76 123.22 L 76.52 123.22 L 76.52 121.76 C 73.38 121.64 70.39 120.63 67.85 118.78 L 68.57 117.78 C 70.91 119.47 73.64 120.4 76.52 120.52 L 76.52 118.89 L 77.76 118.89 L 77.76 120.52 C 80.64 120.4 83.37 119.47 85.71 117.78 Z M 67.85 93.04 C 70.39 91.2 73.38 90.18 76.52 90.06 L 76.52 88.56 L 77.76 88.56 L 77.76 90.06 C 80.9 90.18 83.89 91.2 86.43 93.04 L 85.71 94.04 C 83.37 92.36 80.64 91.42 77.76 91.3 L 77.76 92.89 L 76.52 92.89 L 76.52 91.3 C 73.64 91.42 70.91 92.36 68.57 94.04 Z M 94.47 105.27 L 94.47 106.51 L 92.99 106.51 C 92.87 109.66 91.86 112.65 90.01 115.2 L 89.01 114.48 C 90.7 112.14 91.64 109.39 91.75 106.51 L 90.14 106.51 L 90.14 105.27 L 91.75 105.27 C 91.62 102.4 90.69 99.67 89.01 97.34 L 90.01 96.62 C 91.85 99.16 92.86 102.14 92.99 105.27 Z M 86.41 95.77 L 91.26 90.91 L 92.14 91.79 L 87.28 96.64 Z M 67.87 116.06 L 63.02 120.91 L 62.14 120.03 L 66.99 115.18 Z M 69.25 98.87 L 60.06 89.69 L 60.94 88.81 L 70.12 98 Z M 85.01 112.88 L 94.22 122.09 L 93.34 122.97 L 84.13 113.76 Z M 71.98 106.34 C 72.02 106.26 72.08 106.18 72.13 106.1 C 73.17 104.46 72.87 102.23 72.54 100.88 C 73.43 101.47 74.24 102.68 74.51 103.19 C 74.63 103.4 74.85 103.53 75.1 103.51 C 75.35 103.49 75.56 103.33 75.64 103.1 C 76.56 100.48 76.09 98.51 75.44 97.24 C 76.23 97.7 76.84 98.36 77.25 99.2 C 78.12 101.02 77.93 103.51 76.74 105.7 C 75.11 108.7 75.43 111.84 75.8 113.52 C 74.88 113.12 74.05 112.67 73.33 112.17 C 71.46 110.87 70.86 108.31 71.98 106.34 Z M 80.29 106.14 C 80.25 106.39 80.37 106.64 80.59 106.77 C 80.81 106.9 81.09 106.88 81.29 106.72 C 81.34 106.68 82.36 105.84 82.85 103.81 C 83.44 104.67 84.06 106.41 83.24 109.61 C 82.42 112.77 78.55 113.65 77.17 113.87 C 76.84 112.68 76.16 109.35 77.82 106.29 C 79 104.12 79.32 101.66 78.73 99.64 C 79.85 100.95 80.79 103.02 80.29 106.14 Z M 70.9 105.73 C 69.48 108.25 70.23 111.52 72.63 113.19 C 73.71 113.94 75.02 114.6 76.53 115.14 C 76.59 115.16 76.66 115.17 76.73 115.17 C 76.75 115.17 76.76 115.17 76.77 115.17 L 76.77 115.17 C 77.03 115.15 83.22 114.61 84.43 109.91 C 86 103.87 82.87 102.08 82.74 102 C 82.55 101.9 82.33 101.9 82.15 102 C 81.97 102.09 81.84 102.27 81.82 102.48 C 81.78 102.9 81.71 103.27 81.63 103.6 C 81.2 98.85 77.44 96.92 76.67 96.58 C 75.89 95.98 74.95 95.57 73.85 95.37 C 73.57 95.32 73.3 95.47 73.18 95.72 C 73.06 95.98 73.13 96.28 73.34 96.46 C 73.44 96.54 75.36 98.21 74.81 101.39 C 74.1 100.45 72.95 99.31 71.61 99.31 C 71.4 99.31 71.21 99.42 71.1 99.58 C 70.98 99.75 70.95 99.97 71.03 100.16 C 71.04 100.19 72.3 103.52 71.08 105.43 C 71.02 105.53 70.96 105.63 70.9 105.73 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <rect x="27" y="219" width="100" height="100" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 28.36 245.49 L 28.36 239.45 L 34.39 239.45 L 34.39 245.49 Z M 27.07 246.78 L 35.68 246.78 L 35.68 238.16 L 27.07 238.16 Z M 37.94 245.49 L 37.94 239.45 L 44.54 239.45 L 44.54 245.49 Z M 36.65 246.78 L 45.83 246.78 L 45.83 238.16 L 36.65 238.16 Z M 48.08 245.49 L 48.08 239.45 L 54.12 239.45 L 54.12 245.49 Z M 46.79 246.78 L 55.41 246.78 L 55.41 238.16 L 46.79 238.16 Z M 28.36 235.91 L 28.36 229.87 L 39.46 229.87 L 39.46 235.91 Z M 27.07 237.2 L 40.75 237.2 L 40.75 228.58 L 27.07 228.58 Z M 43.01 235.91 L 43.01 229.87 L 54.12 229.87 L 54.12 235.91 Z M 41.72 237.2 L 55.41 237.2 L 55.41 228.58 L 41.72 228.58 Z M 28.36 226.33 L 28.36 220.29 L 54.12 220.29 L 54.12 226.33 Z M 27.07 227.62 L 55.41 227.62 L 55.41 219 L 27.07 219 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 70px; height: 1px; padding-top: 233px; margin-left: 56px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;" color="#010409" face="-apple-system, BlinkMacSystemFont, Segoe UI, Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji">
                                    <span style="font-size: 10px;">
                                        ecs-app
                                    </span>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="91" y="236" fill="#000000" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    ecs-app
                </text>
            </switch>
        </g>
        <path d="M 55.33 261.22 L 98.67 261.22 L 98.67 304.56 L 55.33 304.56 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 91.28 287.6 L 86.42 284.69 L 86.42 277.74 C 86.42 277.52 86.31 277.32 86.12 277.21 L 79.13 273.13 L 79.13 267.26 L 91.28 274.44 Z M 92.2 273.57 L 78.83 265.67 C 78.64 265.56 78.41 265.56 78.22 265.66 C 78.03 265.77 77.91 265.97 77.91 266.19 L 77.91 273.48 C 77.91 273.7 78.03 273.9 78.21 274.01 L 85.2 278.09 L 85.2 285.03 C 85.2 285.24 85.32 285.44 85.5 285.55 L 91.58 289.2 C 91.67 289.25 91.78 289.28 91.89 289.28 C 91.99 289.28 92.1 289.26 92.19 289.2 C 92.38 289.1 92.5 288.89 92.5 288.68 L 92.5 274.09 C 92.5 273.88 92.38 273.68 92.2 273.57 Z M 76.97 298.92 L 62.72 291.35 L 62.72 274.44 L 74.87 267.26 L 74.87 273.15 L 68.47 277.22 C 68.29 277.34 68.19 277.53 68.19 277.74 L 68.19 288.07 C 68.19 288.29 68.31 288.5 68.52 288.61 L 76.69 292.86 C 76.87 292.95 77.08 292.95 77.25 292.86 L 85.18 288.76 L 90.06 291.69 Z M 91.59 291.19 L 85.52 287.55 C 85.34 287.44 85.11 287.43 84.92 287.53 L 76.97 291.64 L 69.4 287.7 L 69.4 278.07 L 75.81 274 C 75.98 273.88 76.09 273.69 76.09 273.48 L 76.09 266.19 C 76.09 265.97 75.97 265.77 75.78 265.66 C 75.59 265.56 75.36 265.56 75.17 265.67 L 61.8 273.57 C 61.62 273.68 61.5 273.88 61.5 274.09 L 61.5 291.71 C 61.5 291.94 61.63 292.15 61.83 292.25 L 76.69 300.15 C 76.78 300.2 76.87 300.22 76.97 300.22 C 77.07 300.22 77.17 300.2 77.27 300.15 L 91.57 292.25 C 91.77 292.14 91.88 291.94 91.89 291.73 C 91.89 291.51 91.78 291.3 91.59 291.19 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 407px; height: 1px; padding-top: 15px; margin-left: 13px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 14px;">
                                    サービス運用時
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="13" y="19" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    サービス運用時
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 407px; height: 1px; padding-top: 363px; margin-left: 12px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 14px;">
                                    サービス終了時
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="12" y="367" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    サービス終了時
                </text>
            </switch>
        </g>
        <rect x="10" y="558" width="410" height="120" fill-opacity="0.6" fill="#647687" stroke="#314354" stroke-opacity="0.6" pointer-events="none"/>
        <path d="M 304 92 L 130 92" fill="none" stroke="#df2e35" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <ellipse cx="127" cy="92" rx="3" ry="3" fill="#df2e35" stroke="#df2e35" stroke-width="2" pointer-events="none"/>
        <path d="M 304 269 L 130 269" fill="none" stroke="#e97b0c" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <ellipse cx="127" cy="269" rx="3" ry="3" fill="#e97b0c" stroke="#e97b0c" stroke-width="2" pointer-events="none"/>
        <path d="M 304 92 L 129.12 266.88" fill="none" stroke="#e97b0c" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <ellipse cx="127" cy="269" rx="3" ry="3" fill="#e97b0c" stroke="#e97b0c" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 180px; margin-left: 215px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                参照
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="215" y="185" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    参照
                </text>
            </switch>
        </g>
        <path d="M 354 142 L 354 216" fill="none" stroke="#db2a32" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <ellipse cx="354" cy="219" rx="3" ry="3" fill="#db2a32" stroke="#db2a32" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 181px; margin-left: 354px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                参照
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="354" y="185" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    参照
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 408px; height: 1px; padding-top: 543px; margin-left: 11px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                スタック削除可能
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="215" y="547" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    スタック削除可能
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>