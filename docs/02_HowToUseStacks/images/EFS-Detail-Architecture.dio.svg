<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="561px" height="168px" viewBox="-0.5 -0.5 561 168" content="&lt;mxfile&gt;&lt;diagram name=&quot;240902&quot; id=&quot;f13kGtmtnwtn7VZKcMEF&quot;&gt;7Vhbb5swFP41PLYKEEL6GNKk27RK3TK13VPlgANWDUa2c9uv3zGYBDBNG61pO6kVbX0+H4x9vnMDyx2nmyuO8uSaRZhaTi/aWO6l5Tiu04O/CtiWgO9flEDMSVRC9h6YkT9Yg/q+eEkiLBqKkjEqSd4EQ5ZlOJQNDHHO1k21BaPNp+YoxgYwCxE10TsSyaREh46/x79gEifVk+2BPl+KKmV9EpGgiK1rkDux3DFnTJajdDPGVNmussvPWXj3bTD6dXv7uB1OJvc/5tfyrFxseswtuyNwnMnXXdopl14hutT2wgtxJiQKH/Wh5bay5DohEs9yFCp5Dc5iuUEiUwqSDcMFoXTMKOMgZywDpYCzZRZh9fweSBTNMb1hgkjCMsBCOA0G7WCFuSTA2PeWgmTqEYiSuFN9pCfmTEqWwsQLzaTNqZbBm5qTaLNdYZZiybegol3evtAesN47kDfQWFJ3Hk+DSDttvFtrTwwMNDdH8OQaPBGw7OZVObL/S470bBWkmrKdXKPM7aSsdyrK+gZl1sS1ho41qgbBtBgUlx7YBqM4gnSmRU1ZjVCwIt/e6wArhN9KOPcq8XJTn7zc1qUbzAmcVRFWgMdxI9iSh/iAnqezPeIxPrTeoNRTBz3INMcUSbJq5vVXp80zaJtdmaRQCvVLcfFMwDWjqyP8kMjLqrcgG6V3sgDptwKkbwZIvyM++qcKj4Fh58l0ZhhaPGIZJto/c0YyWezDC+CCnY3LXw9Uxwo5d7wOsAvzTdA21eCf3fWENtiF+SZom2pKqnbdBLsw3zN33L7b7rjbbt0NlxuwpaQkw+Nd46VsvGCZrFzUgu7PdaaK1CDmKCK4MTfojVzXr81dEg4LlXUhY1y5VcPl1Xq+Dx0W4EJy9ohrM4viB2YiJJJdz/BU2dnVk3aheaIe1WsgnFC3qbZTydrjel0RCX1fribTTaw65HO0Fv1zjsv89zVU+wlALEdNLchYArb3AFbAD2IrJE6NpHCqgLcHHyzifSPiA2gzlzlgNxRlh2P/n5xV59qWL7rT4cDpG76old/bDYtkh/lkhcucZz/lifPCiA+5MmHbt96suny48jJ82tlu0ZLKT287nPSOdb9VYdS3ym1uu91/b3e7MNxNvUejKDqrCsVZzigJt59vbPuG9PlXNruLxNO9slXfrGo0fh1dq/rUzd2pc0Yw7Q07uqUPkjNenCJyzFMiBGxIvF+F6ntvljJA3H8VLOZqn1bdyV8=&lt;/diagram&gt;&lt;diagram id=&quot;ZXlRkXRwuZhEmwdYOqbC&quot; name=&quot;240821&quot;&gt;7VjbbqMwEP0aHlthLqF9LLl0K+1KlSLt5alywQlWDUa2c9uv3zGYBDBJu2rartRtieI5HoN95szYxPHH+fZW4DL7xlPCHM9Nt44/cTwPIS+EL43saiSKrmtgKWhqnA7AnP4mBnQNuqIpkR1HxTlTtOyCCS8KkqgOhoXgm67bgrPuU0u8JBYwTzCz0R80VVmNXnnRAf9C6DJrnoxGZn05bpzNSmSGU75pQf7U8ceCc1W38u2YME1ew0s9bnakdz8xQQr1kgFePWCN2cqsjSzkhVQ4eTITVLtm1ZuMKjIvcaLtDUTW8eNM5QwsBM0FZWzMGRdgF7wAp1jwVZGS1Dgw/EjYPZdUUV4AlsAcCXjHayIUBXa/9hwU14/AjC4H3W9MxyNXiufQYS/e8KFHkG0LMmTcEp4TJXbgYpSIrk1gNoe4hiODZe2YhgbERkvL/b0OdEPDMD7Mvm+xT4Gv7edi3vQ2GWECsbdbgfAHA+GeIRAIWZFwpr5z5Tk3TSOeVY3qMg1kBYqkUCeMaSLRihOQI3Y/wXAb45c2LsPGnGzbnZNd27ongsK6dBwq8Cjlkq9EYuYTmOKIxZIYL1N69UxPhkUQhhVddyveazgOLIrntzaBjEER17w9o/muwAcyAMuyLv0LutV+59Bo0NNoYGs0GJBocAaFhhZ709ncok8+EZVkRiElp4WqHhnGcMEkxvUnBNexRi71bS1wCItsENlu8IWGntAHh7DIBpHtpq1m1l1wCItCe8b90WhgNOqNhsuP+UoxWpDx/kyhOV7wQjXCczwf/mc6fvFS4JSSTt/IvfH9qNU3oQJuVBfcggutoI6Q9f2iCA4PgEsl+BNp9SyqP+hJscyqPHBP1PN9oe5X8COFvr25wArNCQx5jW0U5w7lGRxpSt2Zb5f69HeJNzK4FKQuS3eJnk8MZt3qekHJkTC9B2CBPMidVCS3Uv0MaYxGH5fHIyuPYzhrrUrA7hkuTmf0qyRo6mJPYf7sagSlua8w4/zR4qpKGBHTNakrGTqmr8eKxIdSU9hXzFvsBB+5FUTHJfQdr5j6r6HTBepvRbWuSH2DOuT3z7zvKKJrS0T6xQ+n6UVTqi9Kzmiy+5QvI4H7/NsIGgrNed5GXCs4dzff9A4xHJG3zu945l4NnEL+kfx+cTqXRORUSpiQfJc9IgjfLb3BPPxmVPW1fnnzp38A&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="17" width="560" height="150" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 558px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                efs-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    efs-stack
                </text>
            </switch>
        </g>
        <rect x="20" y="47" width="360" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 44px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                index
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    index
                </text>
            </switch>
        </g>
        <path d="M 80 87 L 153.63 87" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 158.88 87 L 151.88 90.5 L 153.63 87 L 151.88 83.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 87px; margin-left: 120px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                アタッチ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="120" y="90" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    アタッチ
                </text>
            </switch>
        </g>
        <ellipse cx="60" cy="87" rx="20" ry="20" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 87px; margin-left: 41px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                SG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="91" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SG
                </text>
            </switch>
        </g>
        <path d="M 160 67 L 200 67 L 200 107 L 160 107 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 175.08 77.87 C 178.66 76.37 181.2 78.34 182.12 79.26 C 182.87 80.01 183.46 80.96 183.87 82.1 C 183.94 82.29 184.11 82.44 184.32 82.47 C 184.53 82.5 184.74 82.42 184.86 82.25 C 185.31 81.65 185.97 81.31 186.67 81.31 C 187.71 81.31 188.98 82.1 189.11 84.33 C 189.12 84.59 189.31 84.8 189.56 84.85 C 192.15 85.4 193.46 86.83 193.46 89.12 C 193.46 91.25 192.49 92.56 190.57 93.03 L 190.84 94.14 C 193.27 93.55 194.61 91.77 194.61 89.12 C 194.61 86.43 193.05 84.56 190.21 83.83 C 189.9 81.3 188.24 80.16 186.67 80.16 C 185.92 80.16 185.2 80.42 184.61 80.89 C 184.17 79.94 183.61 79.13 182.93 78.45 C 180.66 76.2 177.57 75.59 174.64 76.82 C 172.14 77.87 170.26 80.76 170.26 83.55 C 170.26 83.68 170.26 83.82 170.27 83.97 C 167.97 84.73 166.62 86.61 166.62 89.09 C 166.62 89.22 166.62 89.34 166.63 89.46 C 166.75 91.61 168.18 93.47 170.29 94.19 L 170.66 93.11 C 169 92.54 167.86 91.08 167.77 89.4 C 167.77 89.3 167.76 89.2 167.76 89.09 C 167.76 86.19 169.8 85.24 171.02 84.94 C 171.3 84.87 171.48 84.61 171.45 84.32 C 171.41 84.04 171.4 83.78 171.4 83.55 C 171.4 81.23 173.02 78.74 175.08 77.87 Z M 186.86 89.86 C 186.86 89.63 186.81 89.46 186.72 89.38 C 186.62 89.29 186.45 89.28 186.32 89.29 L 178.86 89.29 C 178.54 89.29 178.29 89.03 178.29 88.71 C 178.29 88.56 178.23 88.13 178.16 87.86 L 176.18 87.86 C 176.1 88.12 176 88.57 176 88.72 C 175.99 89.03 175.74 89.29 175.43 89.29 L 173.71 89.29 C 173.55 89.28 173.38 89.29 173.28 89.38 C 173.19 89.46 173.14 89.63 173.14 89.86 L 173.14 97.86 L 186.86 97.86 Z M 188 91 L 188 95.13 L 189.48 91 Z M 187.97 98.62 L 187.96 98.62 C 187.88 98.84 187.68 99 187.43 99 L 172.57 99 C 172.26 99 172 98.75 172 98.43 L 172 89.86 C 172 89.16 172.27 88.76 172.5 88.54 C 172.72 88.34 173.11 88.1 173.75 88.15 L 174.92 88.14 C 175.03 87.55 175.28 86.71 175.89 86.71 L 178.49 86.71 C 178.53 86.71 178.56 86.72 178.6 86.72 C 179.13 86.82 179.32 87.59 179.39 88.14 L 186.29 88.14 C 186.89 88.1 187.28 88.34 187.5 88.54 C 187.73 88.76 188 89.16 188 89.86 L 190.29 89.86 C 190.47 89.86 190.65 89.95 190.75 90.1 C 190.86 90.25 190.89 90.45 190.82 90.62 Z M 194.86 101.05 L 190.51 96.7 L 189.7 97.51 L 194.05 101.86 L 190.9 101.86 L 190.9 103 L 195.43 103 C 195.74 103 196 102.74 196 102.43 L 196 97.9 L 194.86 97.9 Z M 170.08 96.68 L 165.14 101.14 L 165.14 97.9 L 164 97.9 L 164 102.43 C 164 102.74 164.26 103 164.57 103 L 169.67 103 L 169.67 101.86 L 166.06 101.86 L 170.85 97.53 Z M 195.43 71 L 190.33 71 L 190.33 72.14 L 194.14 72.14 L 189.68 77.08 L 190.53 77.85 L 194.86 73.06 L 194.86 76.67 L 196 76.67 L 196 71.57 C 196 71.26 195.74 71 195.43 71 Z M 165.14 76.67 L 164 76.67 L 164 71.57 C 164 71.26 164.26 71 164.57 71 L 169.67 71 L 169.67 72.14 L 165.95 72.14 L 170.87 77.06 L 170.06 77.87 L 165.14 72.95 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 114px; margin-left: 180px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                EFS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="126" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EFS
                </text>
            </switch>
        </g>
        <rect x="240" y="67" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 273.64 105.18 L 273.64 76.09 L 267.27 76.09 C 266.77 76.09 266.36 75.68 266.36 75.18 L 266.36 68.82 L 246.36 68.82 L 246.36 105.18 Z M 268.18 74.27 L 272.35 74.27 L 268.18 70.1 Z M 275.45 75.18 L 275.45 106.09 C 275.45 106.59 275.05 107 274.55 107 L 245.45 107 C 244.95 107 244.55 106.59 244.55 106.09 L 244.55 67.91 C 244.55 67.41 244.95 67 245.45 67 L 267.27 67 L 267.27 67.01 C 267.51 67.01 267.74 67.09 267.92 67.27 L 275.19 74.54 C 275.36 74.71 275.45 74.95 275.45 75.18 Z M 269.62 98.29 L 268.1 99.81 L 266.59 98.29 L 265.3 99.58 L 266.82 101.09 L 265.3 102.61 L 266.59 103.89 L 268.1 102.38 L 269.62 103.89 L 270.9 102.61 L 269.39 101.09 L 270.9 99.58 Z M 252.27 76.09 C 253.02 76.09 253.64 75.48 253.64 74.73 C 253.64 73.98 253.02 73.36 252.27 73.36 C 251.52 73.36 250.91 73.98 250.91 74.73 C 250.91 75.48 251.52 76.09 252.27 76.09 Z M 252.27 77.91 C 250.52 77.91 249.09 76.48 249.09 74.73 C 249.09 72.97 250.52 71.55 252.27 71.55 C 254.03 71.55 255.45 72.97 255.45 74.73 C 255.45 76.48 254.03 77.91 252.27 77.91 Z M 261.39 98.71 C 260.9 98.78 260.42 98.81 259.94 98.81 C 257.81 98.81 255.76 98.13 254.03 96.84 C 251.51 94.97 250.06 92.09 250.03 88.95 L 251.85 88.94 C 251.87 91.5 253.06 93.85 255.11 95.38 C 256.85 96.68 258.98 97.22 261.13 96.91 C 263.27 96.59 265.16 95.47 266.45 93.73 C 269.12 90.15 268.38 85.06 264.8 82.39 C 261.46 79.9 256.81 80.38 254.03 83.36 L 255.45 83.36 L 255.45 85.18 L 251.82 85.18 C 251.32 85.18 250.91 84.77 250.91 84.27 L 250.91 80.64 L 252.73 80.64 L 252.73 82.1 C 256.13 78.47 261.81 77.89 265.89 80.93 C 270.27 84.2 271.18 90.43 267.91 94.82 C 266.33 96.94 264.01 98.32 261.39 98.71 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 114px; margin-left: 260px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Plan
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="126" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <rect x="320" y="67" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 327.54 86.05 L 325.73 86.06 C 325.76 90.62 327.87 94.79 331.52 97.51 C 334.09 99.43 337.11 100.36 340.1 100.36 C 344.49 100.36 348.83 98.36 351.65 94.57 C 356.4 88.21 355.08 79.18 348.72 74.44 C 342.56 69.85 333.91 70.95 329.06 76.8 L 329.09 74.29 L 327.27 74.26 L 327.21 78.81 C 327.21 79.05 327.3 79.28 327.47 79.45 C 327.64 79.63 327.87 79.72 328.11 79.73 L 332.71 79.79 L 332.74 77.97 L 330.47 77.94 C 334.71 72.85 342.25 71.89 347.63 75.89 C 353.19 80.04 354.34 87.93 350.2 93.49 C 346.05 99.05 338.16 100.2 332.6 96.05 C 329.42 93.68 327.57 90.03 327.54 86.05 Z M 340 82.45 C 337.99 82.45 336.36 84.09 336.36 86.09 C 336.36 88.1 337.99 89.73 340 89.73 C 342.01 89.73 343.64 88.1 343.64 86.09 C 343.64 84.09 342.01 82.45 340 82.45 Z M 333.59 91.35 L 335.61 89.32 C 334.95 88.41 334.55 87.3 334.55 86.09 C 334.55 84.84 334.98 83.68 335.69 82.76 L 333.59 80.81 L 334.83 79.48 L 337.03 81.52 C 337.88 80.97 338.9 80.64 340 80.64 C 341.22 80.64 342.34 81.04 343.25 81.72 L 345.44 79.48 L 346.74 80.75 L 344.51 83.03 C 345.11 83.91 345.45 84.96 345.45 86.09 C 345.45 87.23 345.1 88.29 344.5 89.16 L 346.74 91.32 L 345.48 92.63 L 343.24 90.47 C 342.33 91.14 341.21 91.55 340 91.55 C 338.86 91.55 337.79 91.19 336.92 90.59 L 334.87 92.63 Z M 350 105.18 L 353.64 105.18 L 353.64 104.27 L 350 104.27 Z M 326.36 105.18 L 330 105.18 L 330 104.27 L 326.36 104.27 Z M 360 67.91 L 360 103.36 C 360 103.87 359.59 104.27 359.09 104.27 L 355.45 104.27 L 355.45 106.09 C 355.45 106.59 355.05 107 354.55 107 L 349.09 107 C 348.59 107 348.18 106.59 348.18 106.09 L 348.18 104.27 L 331.82 104.27 L 331.82 106.09 C 331.82 106.59 331.41 107 330.91 107 L 325.45 107 C 324.95 107 324.55 106.59 324.55 106.09 L 324.55 104.27 L 320.91 104.27 C 320.41 104.27 320 103.87 320 103.36 L 320 95.18 L 321.82 95.18 L 321.82 102.45 L 358.18 102.45 L 358.18 68.82 L 321.82 68.82 L 321.82 75.18 L 320 75.18 L 320 67.91 C 320 67.41 320.41 67 320.91 67 L 359.09 67 C 359.59 67 360 67.41 360 67.91 Z M 320 89.73 L 321.82 89.73 L 321.82 80.64 L 320 80.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 114px; margin-left: 340px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Vault
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="126" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <rect x="400" y="47" width="140" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 44px; margin-left: 401px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                efs-add-resource-policy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="470" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    efs-add-resource-policy
                </text>
            </switch>
        </g>
        <rect x="450" y="67" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 466.97 81.58 L 468.26 82.87 L 461.89 89.23 C 461.72 89.41 461.48 89.5 461.25 89.5 C 461.02 89.5 460.78 89.41 460.61 89.23 L 457.43 86.05 L 458.71 84.77 L 461.25 87.31 Z M 466.97 71.24 L 468.26 72.53 L 461.89 78.89 C 461.72 79.07 461.48 79.16 461.25 79.16 C 461.02 79.16 460.78 79.07 460.61 78.89 L 457.43 75.71 L 458.71 74.43 L 461.25 76.96 Z M 466.67 94.8 L 463.73 97.74 L 466.67 100.68 L 465.38 101.96 L 462.44 99.02 L 459.51 101.96 L 458.22 100.68 L 461.16 97.74 L 458.22 94.8 L 459.51 93.52 L 462.44 96.45 L 465.38 93.52 Z M 484.2 104.45 C 484.2 104.85 483.88 105.18 483.47 105.18 L 456.53 105.18 C 456.12 105.18 455.8 104.85 455.8 104.45 L 455.8 69.55 C 455.8 69.15 456.12 68.82 456.53 68.82 L 483.47 68.82 C 483.88 68.82 484.2 69.15 484.2 69.55 Z M 483.47 67 L 456.53 67 C 455.12 67 453.98 68.14 453.98 69.55 L 453.98 104.45 C 453.98 105.86 455.12 107 456.53 107 L 483.47 107 C 484.88 107 486.02 105.86 486.02 104.45 L 486.02 69.55 C 486.02 68.14 484.88 67 483.47 67 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 114px; margin-left: 470px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAM Policy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="470" y="126" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAM Po...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>