<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="751px" height="518px" viewBox="-0.5 -0.5 751 518" content="&lt;mxfile&gt;&lt;diagram id=&quot;QpF0_U5Uija1xOwxJGoi&quot; name=&quot;240816&quot;&gt;7Vpbb6s4EP41SLsPG3GHPIZcuit1V0fNSuexcsEhqA5G4DTp/vodgyGAnTQ5ufSiJq2Cx3Yw833zMR6iWePV9i5H2fJvGmGimXq01ayJZpqm4fvwwS2vlcVwHGGJ8yQStp1hnvyHhVEX1nUS4aIzkFFKWJJ1jSFNUxyyjg3lOd10hy0o6Z41QzGWDPMQEdn6M4nYsrL6prez/4mTeFmf2XCHVc8K1YPFlRRLFNFNy2RNNWucU8qqo9V2jAn3Xu2Xat5sT2+zsByn7JgJtl3NeEFkLS7ugRKSpDEYJzgj9FUslL3WV79ZJgzPMxTy9gYg1qxgyVYEWgYc5nSdRjgSrUVCyJgSmkM7pSlMCQh6wuQHLRKW0BTMIawVQ3/wgnOWgJfvewMY5adAJImVw0ei44kyRlfQITtB+IXPwNuWSTjlDtMVZjlcqi56PVcAJCjq2qK92eFtDoVt2cLa1IURCY7FzXfvYIADgYQalaEEygzlMWIYjHOcvyTg+j4qxTNmIV+ZDh7IaJKycgFOAH+wpHH178DQMbcMTEdhVNk82WjIw+DDUJ2hb1TZPNloyMN4q15116iyeY684v5sQzHb6M2GPyugawYhgceNnHAfL2jKamZrpgXvGUcziHMUJbjTN/P8qW63+iZJDl9UsTulOWdUJ1JgzkR3xoYH9oLl9Bm3ehblC3oiVCzLQNMPBE8TFf1w2RNVrUjmVyjE1zDrtmAcPyUqssodi2TL1xGAmmW8c7WNufIP0KawBzku6DoP8V8hX08AzeqoO2ohCF5+SSWJeq0VAQqf41JWVEoS0DzCea+nclotzsZlRMG3u6JgW7Io2ApNsC8gCa4kCcBGhoCVuSQF3FfZrwhtw5U3tbYiz2F0jvd4c6dGT/U16AeRGHo9JMzbIeFJSJx6hzyG4RFeoDVhh0nelnyZ8Ypbb1+bOtHyJnzuHvhkmG4Ag/82DErZvrZoKjTQGuDQfAzpKlszDJ913EqaP3N8x7IP4HQb4ki6fg4VRIT6/D57RISa1vnUqL/3EDcqjbyeQll6V6EsRQLZqNbFE0hLFikcFn8ABVc0hY8UQF8DxF8gtW/APkmVrOu53pRcP33hqzX1hzV5I28/K9EUGPQ0JdB1357sE/n3TiHLjQrOSw8V+xUUskjw3aN1LgPqXr8bnIZ+u/TBciR+zP+Zg+FfmiXhNz1+jR6sdN5Rt67T6WK9J13kxF+bDjVY0XCkTT1tONNGjsQaHMW49iPfXtKYpohMd9Y2CPtdVG3currGYKOGWZfL/HQHHZljgljy0i2fneUWRfrHvTHUgtItvqGNglu4xVZ4xXwvr8jFvOmYS8uYrAum2Ch+14y+a0afuGYEKe2hnPMS0u++n/IrdrjTbbjmV/lAFYnkvqpPXi1rL9irJIpK7athJXjBFNg3407w68nbpp6/b1jWURTdJRd/ibpOq/5QnkCGs2Hehyj01EXWQ8BcPUue6b7hftQsWdLU43dVlODzuHpRNjW1Ig+24s2rrri2t+/uwO28bJl7pj5w/QvkVPJ+fn4nZ5aEJFnBHXJuCeWIHczpDy58pyOrrn87XTUsyX+je7gotyTWEySlbsyPfhPPnX+/eXDbE9Mbjb5ecKMsI7AWvsRHQlH0+IQISsNyAR/4IVuvcHpLrjpybWZPmnW9wrHde8joKQrHhqp6aVyiemkc4YEPkgW14kY/KStqItoQjR+IQUClpcUs91wyvg01TsqJDGM4GHZe18JNLhOpVTa4+xbYTyywJ2x2T2Bs3asPhnr77XeUyLhh0iA/vwr4kcTn+6RgWPUbiM8uUpfA03UGttN+vwmnNXCt9ltG17oEunJl445gnH7De0F43XeD11E8srjefonC1ITxi3aUan6BlNTtp2TXqwJCc/fj37Kv9Rtqa/o/&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="460" y="317" width="290" height="200" rx="30" ry="30" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 288px; height: 1px; padding-top: 314px; margin-left: 461px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Rolling Deploy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="605" y="314" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Rolling Deploy
                </text>
            </switch>
        </g>
        <path d="M 540 107 L 580 107 L 580 147 L 540 147 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 567.53 137.06 L 567.53 134.02 L 569.41 133.26 L 569.41 136.3 Z M 564.39 133.26 L 566.27 134.02 L 566.27 137.06 L 564.39 136.3 Z M 560.63 141.45 L 560.63 138.41 L 562.51 137.65 L 562.51 140.69 Z M 557.49 137.65 L 559.37 138.41 L 559.37 141.45 L 557.49 140.69 Z M 553.73 137.06 L 553.73 134.02 L 555.61 133.26 L 555.61 136.3 Z M 550.59 133.26 L 552.47 134.02 L 552.47 137.06 L 550.59 136.3 Z M 553.1 131.76 L 554.55 132.34 L 553.1 132.92 L 551.65 132.34 Z M 560 136.15 L 561.45 136.73 L 560 137.31 L 558.55 136.73 Z M 566.9 131.76 L 568.35 132.34 L 566.9 132.92 L 565.45 132.34 Z M 570.27 131.76 L 567.13 130.5 C 566.98 130.44 566.82 130.44 566.67 130.5 L 563.53 131.76 C 563.29 131.85 563.14 132.08 563.14 132.34 L 563.14 136.05 L 560.23 134.89 C 560.08 134.83 559.92 134.83 559.77 134.89 L 556.86 136.05 L 556.86 132.34 C 556.86 132.08 556.71 131.85 556.47 131.76 L 553.33 130.5 C 553.18 130.44 553.02 130.44 552.87 130.5 L 549.73 131.76 C 549.49 131.85 549.34 132.08 549.34 132.34 L 549.34 136.73 C 549.34 136.98 549.49 137.21 549.73 137.31 L 552.87 138.57 C 552.94 138.59 553.02 138.61 553.1 138.61 C 553.18 138.61 553.26 138.59 553.33 138.57 L 556.24 137.4 L 556.24 141.12 C 556.24 141.37 556.39 141.61 556.63 141.7 L 559.77 142.96 C 559.84 142.98 559.92 143 560 143 C 560.08 143 560.16 142.98 560.23 142.96 L 563.37 141.7 C 563.61 141.61 563.76 141.37 563.76 141.12 L 563.76 137.4 L 566.67 138.57 C 566.74 138.59 566.82 138.61 566.9 138.61 C 566.98 138.61 567.06 138.59 567.13 138.57 L 570.27 137.31 C 570.51 137.21 570.66 136.98 570.66 136.73 L 570.66 132.34 C 570.66 132.08 570.51 131.85 570.27 131.76 Z M 575.68 124.15 C 575.68 127.81 567.6 129.79 560 129.79 C 552.4 129.79 544.32 127.81 544.32 124.15 C 544.32 122.4 546.26 120.85 549.79 119.81 L 550.14 121.01 C 547.33 121.85 545.57 123.05 545.57 124.15 C 545.57 126.22 551.5 128.54 560 128.54 C 568.5 128.54 574.43 126.22 574.43 124.15 C 574.43 123.05 572.67 121.85 569.86 121.01 L 570.21 119.81 C 573.74 120.85 575.68 122.4 575.68 124.15 Z M 560 112.31 L 566.41 114.78 L 560 117.24 L 553.59 114.78 Z M 566.64 124.71 C 565.45 125.24 563.47 125.86 560.63 125.94 L 560.63 118.34 L 567.53 115.69 L 567.53 123.34 C 567.53 123.93 567.18 124.47 566.64 124.71 Z M 552.47 123.34 L 552.47 115.69 L 559.37 118.34 L 559.37 125.94 C 556.53 125.86 554.55 125.24 553.36 124.71 C 552.82 124.47 552.47 123.93 552.47 123.34 Z M 552.85 125.86 C 554.23 126.48 556.59 127.21 560 127.21 C 563.41 127.21 565.77 126.48 567.15 125.86 C 568.14 125.42 568.78 124.43 568.78 123.34 L 568.78 114.78 C 568.78 114.52 568.62 114.28 568.38 114.19 L 560.23 111.06 C 560.08 111 559.92 111 559.77 111.06 L 551.62 114.19 C 551.38 114.28 551.22 114.52 551.22 114.78 L 551.22 123.34 C 551.22 124.43 551.86 125.42 552.85 125.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 154px; margin-left: 560px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Fargate Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="560" y="166" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Fargat...
                </text>
            </switch>
        </g>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <text x="689.5" y="154.5">
                Container
            </text>
        </g>
        <path d="M 670 128.5 L 670 105.5 L 710 105.52 L 709.92 128.5 Z" fill="#f58534" stroke="none" pointer-events="none"/>
        <path d="M 670 128.5 L 670 126.57 L 709.92 126.57 L 709.92 128.5 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="none"/>
        <path d="M 706.34 125.32 L 706.34 106.76 L 708.23 106.76 L 708.23 125.32 Z M 702.88 125.32 L 702.88 106.76 L 704.77 106.76 L 704.77 125.32 Z M 699.42 125.32 L 699.42 106.76 L 701.3 106.76 L 701.3 125.32 Z M 695.95 125.32 L 695.95 106.76 L 697.84 106.76 L 697.84 125.32 Z M 692.49 125.32 L 692.49 106.76 L 694.37 106.76 L 694.37 125.32 Z M 689.02 125.32 L 689.02 106.76 L 690.91 106.76 L 690.91 125.32 Z M 685.56 125.32 L 685.56 106.76 L 687.44 106.76 L 687.44 125.32 Z M 682.09 125.32 L 682.09 106.76 L 683.98 106.76 L 683.98 125.32 Z M 678.63 125.32 L 678.63 106.76 L 680.5 106.76 L 680.5 125.32 Z M 675.16 125.32 L 675.16 106.76 L 677.04 106.76 L 677.04 125.32 Z M 671.7 125.32 L 671.7 106.76 L 673.58 106.76 L 673.58 125.32 Z" fill-opacity="0.3" fill="#ffffff" stroke="none" pointer-events="none"/>
        <rect x="670" y="105.5" width="0" height="0" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 708.23 125.32 L 706.84 125.32 L 706.84 107.26 L 708.23 107.26 Z M 704.77 125.32 L 703.37 125.32 L 703.37 107.26 L 704.77 107.26 Z M 701.3 125.32 L 699.91 125.32 L 699.91 107.26 L 701.3 107.26 Z M 697.84 125.32 L 696.44 125.32 L 696.44 107.26 L 697.84 107.26 Z M 694.37 125.32 L 692.98 125.32 L 692.98 107.26 L 694.37 107.26 Z M 690.91 125.32 L 689.51 125.32 L 689.51 107.26 L 690.91 107.26 Z M 687.44 125.32 L 686.05 125.32 L 686.05 107.26 L 687.44 107.26 Z M 683.98 125.32 L 682.58 125.32 L 682.58 107.26 L 683.98 107.26 Z M 680.5 125.32 L 679.12 125.32 L 679.12 107.26 L 680.5 107.26 Z M 677.04 125.32 L 675.65 125.32 L 675.65 107.26 L 677.04 107.26 Z M 673.58 125.32 L 672.19 125.32 L 672.19 107.26 L 673.58 107.26 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <rect x="0" y="17" width="320" height="200" rx="30" ry="30" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                ecs-common-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="160" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ecs-common-construct
                </text>
            </switch>
        </g>
        <path d="M 197.04 137.82 L 197.04 136 L 216.8 136 L 212.95 132.15 L 214.24 130.86 L 219.64 136.27 C 220 136.62 220 137.2 219.64 137.55 L 219.64 137.55 L 214.24 142.96 L 212.95 141.67 L 216.8 137.82 Z M 205.19 121.36 L 203.52 120.65 L 201.28 125.89 L 202.96 126.6 Z M 212.07 127.22 L 210.98 125.76 L 206.39 129.21 L 207.49 130.66 Z M 203.93 146.83 L 207.4 151.35 L 208.85 150.24 L 205.38 145.72 Z M 198.09 148.68 L 198.8 154.31 L 200.61 154.08 L 199.9 148.45 Z M 189.87 152.64 L 191.54 153.35 L 193.78 148.11 L 192.11 147.4 Z M 182.99 146.78 L 184.08 148.24 L 188.67 144.79 L 187.57 143.34 Z M 185.71 137.58 L 180 138.3 L 180.23 140.11 L 185.93 139.39 Z M 187.01 131.66 L 181.71 129.47 L 181.01 131.15 L 186.31 133.34 Z M 191.13 127.17 L 187.66 122.65 L 186.22 123.76 L 189.69 128.28 Z M 196.97 125.32 L 196.26 119.69 L 194.45 119.92 L 195.16 125.55 Z M 204.47 141.19 L 202.39 144.8 C 202.22 145.08 201.92 145.26 201.6 145.26 L 193.55 145.26 C 193.22 145.26 192.92 145.08 192.76 144.8 L 188.73 137.83 C 188.57 137.55 188.57 137.2 188.73 136.92 L 192.76 129.95 C 192.92 129.66 193.22 129.49 193.55 129.49 L 201.6 129.49 C 201.92 129.49 202.22 129.66 202.39 129.95 L 203.76 132.33 L 202.18 133.24 L 201.07 131.31 L 194.07 131.31 L 190.57 137.37 L 194.07 143.44 L 201.07 143.44 L 202.89 140.28 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 164px; margin-left: 200px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Event Rule
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="176" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Event...
                </text>
            </switch>
        </g>
        <path d="M 419.09 119.73 L 380.91 119.73 C 380.41 119.73 380 120.13 380 120.64 L 380 129.73 C 380 130.23 380.41 130.64 380.91 130.64 L 419.09 130.64 C 419.59 130.64 420 130.23 420 129.73 L 420 120.64 C 420 120.13 419.59 119.73 419.09 119.73 Z M 381.82 128.82 L 381.82 121.55 L 418.18 121.55 L 418.18 128.82 Z M 418.18 140.18 L 420 140.18 L 420 141.55 C 420 142.05 419.59 142.45 419.09 142.45 L 417.73 142.45 L 417.73 140.64 L 418.18 140.64 Z M 401.36 142.45 L 404.09 142.45 L 404.09 140.64 L 401.36 140.64 Z M 412.27 142.45 L 415 142.45 L 415 140.64 L 412.27 140.64 Z M 390.45 142.45 L 393.18 142.45 L 393.18 140.64 L 390.45 140.64 Z M 385 142.45 L 387.73 142.45 L 387.73 140.64 L 385 140.64 Z M 406.82 142.45 L 409.55 142.45 L 409.55 140.64 L 406.82 140.64 Z M 395.91 142.45 L 398.64 142.45 L 398.64 140.64 L 395.91 140.64 Z M 381.82 140.64 L 382.27 140.64 L 382.27 142.45 L 380.91 142.45 C 380.41 142.45 380 142.05 380 141.55 L 380 140.18 L 381.82 140.18 Z M 380 138.36 L 381.82 138.36 L 381.82 136.55 L 380 136.55 Z M 380.91 132.45 L 382.27 132.45 L 382.27 134.27 L 381.82 134.27 L 381.82 134.73 L 380 134.73 L 380 133.36 C 380 132.86 380.41 132.45 380.91 132.45 Z M 401.36 134.27 L 404.09 134.27 L 404.09 132.45 L 401.36 132.45 Z M 395.91 134.27 L 398.64 134.27 L 398.64 132.45 L 395.91 132.45 Z M 390.45 134.27 L 393.18 134.27 L 393.18 132.45 L 390.45 132.45 Z M 412.27 134.27 L 415 134.27 L 415 132.45 L 412.27 132.45 Z M 385 134.27 L 387.73 134.27 L 387.73 132.45 L 385 132.45 Z M 406.82 134.27 L 409.55 134.27 L 409.55 132.45 L 406.82 132.45 Z M 420 133.36 L 420 134.73 L 418.18 134.73 L 418.18 134.27 L 417.73 134.27 L 417.73 132.45 L 419.09 132.45 C 419.59 132.45 420 132.86 420 133.36 Z M 418.18 138.36 L 420 138.36 L 420 136.55 L 418.18 136.55 Z M 418.18 152 L 420 152 L 420 153.36 C 420 153.87 419.59 154.27 419.09 154.27 L 417.73 154.27 L 417.73 152.45 L 418.18 152.45 Z M 390.45 154.27 L 393.18 154.27 L 393.18 152.45 L 390.45 152.45 Z M 401.36 154.27 L 404.09 154.27 L 404.09 152.45 L 401.36 152.45 Z M 385 154.27 L 387.73 154.27 L 387.73 152.45 L 385 152.45 Z M 412.27 154.27 L 415 154.27 L 415 152.45 L 412.27 152.45 Z M 406.82 154.27 L 409.55 154.27 L 409.55 152.45 L 406.82 152.45 Z M 395.91 154.27 L 398.64 154.27 L 398.64 152.45 L 395.91 152.45 Z M 381.82 152.45 L 382.27 152.45 L 382.27 154.27 L 380.91 154.27 C 380.41 154.27 380 153.87 380 153.36 L 380 152 L 381.82 152 Z M 380 150.18 L 381.82 150.18 L 381.82 148.36 L 380 148.36 Z M 380.91 144.27 L 382.27 144.27 L 382.27 146.09 L 381.82 146.09 L 381.82 146.55 L 380 146.55 L 380 145.18 C 380 144.68 380.41 144.27 380.91 144.27 Z M 401.36 146.09 L 404.09 146.09 L 404.09 144.27 L 401.36 144.27 Z M 406.82 146.09 L 409.55 146.09 L 409.55 144.27 L 406.82 144.27 Z M 385 146.09 L 387.73 146.09 L 387.73 144.27 L 385 144.27 Z M 395.91 146.09 L 398.64 146.09 L 398.64 144.27 L 395.91 144.27 Z M 412.27 146.09 L 415 146.09 L 415 144.27 L 412.27 144.27 Z M 390.45 146.09 L 393.18 146.09 L 393.18 144.27 L 390.45 144.27 Z M 420 145.18 L 420 146.55 L 418.18 146.55 L 418.18 146.09 L 417.73 146.09 L 417.73 144.27 L 419.09 144.27 C 419.59 144.27 420 144.68 420 145.18 Z M 418.18 150.18 L 420 150.18 L 420 148.36 L 418.18 148.36 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 164px; margin-left: 400px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                SNS Topic
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="176" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SNS To...
                </text>
            </switch>
        </g>
        <path d="M 220 137 L 290 137 Q 300 137 310 137 L 373.63 137" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 378.88 137 L 371.88 140.5 L 373.63 137 L 371.88 133.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 137px; margin-left: 300px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                通知
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="300" y="140" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    通知
                </text>
            </switch>
        </g>
        <path d="M 100 137 L 173.63 137" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 178.88 137 L 171.88 140.5 L 173.63 137 L 171.88 133.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 137px; margin-left: 140px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                発火
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="140" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    発火
                </text>
            </switch>
        </g>
        <path d="M 60 117 L 100 117 L 100 157 L 60 157 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 93.18 141.35 L 88.69 138.66 L 88.69 132.24 C 88.69 132.05 88.59 131.86 88.42 131.76 L 81.96 128 L 81.96 122.57 L 93.18 129.2 Z M 94.03 128.4 L 81.69 121.1 C 81.51 121 81.3 121 81.12 121.1 C 80.95 121.2 80.84 121.39 80.84 121.59 L 80.84 128.32 C 80.84 128.52 80.95 128.7 81.12 128.8 L 87.57 132.57 L 87.57 138.98 C 87.57 139.17 87.68 139.36 87.85 139.46 L 93.45 142.82 C 93.54 142.88 93.64 142.9 93.74 142.9 C 93.84 142.9 93.93 142.88 94.02 142.83 C 94.2 142.73 94.3 142.54 94.3 142.34 L 94.3 128.88 C 94.3 128.68 94.2 128.5 94.03 128.4 Z M 79.97 151.8 L 66.82 144.81 L 66.82 129.2 L 78.04 122.57 L 78.04 128.01 L 72.13 131.77 C 71.96 131.87 71.87 132.05 71.87 132.24 L 71.87 141.78 C 71.87 141.99 71.98 142.18 72.17 142.28 L 79.71 146.21 C 79.88 146.29 80.07 146.29 80.23 146.21 L 87.55 142.42 L 92.06 145.13 Z M 93.47 144.67 L 87.86 141.3 C 87.69 141.2 87.49 141.19 87.32 141.28 L 79.97 145.08 L 72.99 141.44 L 72.99 132.55 L 78.9 128.79 C 79.06 128.69 79.16 128.51 79.16 128.32 L 79.16 121.59 C 79.16 121.39 79.05 121.2 78.88 121.1 C 78.7 121 78.49 121 78.31 121.1 L 65.97 128.4 C 65.8 128.5 65.7 128.68 65.7 128.88 L 65.7 145.15 C 65.7 145.35 65.81 145.54 65.99 145.64 L 79.71 152.93 C 79.79 152.98 79.88 153 79.97 153 C 80.07 153 80.16 152.98 80.25 152.93 L 93.45 145.64 C 93.63 145.54 93.74 145.36 93.74 145.16 C 93.75 144.96 93.64 144.77 93.47 144.67 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 164px; margin-left: 80px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ECS Cluster
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="176" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS Cl...
                </text>
            </switch>
        </g>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" font-size="12px">
            <text x="101.5" y="61.5">
                ExcutionRole
            </text>
        </g>
        <path d="M 95.84 59.39 C 95.72 59.28 95.58 59.21 95.44 59.15 C 94.58 51.57 88.13 45.72 80.42 45.72 C 76.95 45.72 73.55 46.93 70.87 49.12 C 67.33 52 65.3 56.28 65.3 60.85 C 65.3 60.88 65.3 60.91 65.3 60.95 C 64.75 61.22 64.18 61.48 63.61 61.73 C 61.87 62.49 60.62 63.04 60.43 64.03 C 60.38 64.28 60.37 64.77 60.85 65.25 C 61.7 66.1 63.32 66.4 64.96 66.4 C 65.65 66.4 66.34 66.35 66.98 66.26 C 68.07 66.11 70.1 65.71 71.48 64.7 C 73.66 63.12 78.59 62.53 81.16 62.73 C 82.69 62.85 84.54 63.65 86.03 64.3 C 87.17 64.79 88.07 65.18 88.77 65.28 C 89.74 65.41 91.32 65.25 93 65.08 L 93.14 65.06 C 93.58 65.02 94.01 64.98 94.44 64.94 C 94.61 64.92 94.79 64.91 95.02 64.9 C 95.73 64.85 96.29 64.24 96.29 63.53 L 96.29 60.41 C 96.29 60.02 96.13 59.65 95.84 59.39 Z M 80.42 47.35 C 87.23 47.35 92.96 52.47 93.8 59.15 C 93.56 59.17 93.32 59.19 93.05 59.22 L 92.9 59.23 C 92.88 58.48 92.27 57.88 91.52 57.88 L 90.94 57.88 C 89.15 52.6 83.96 48.97 78.08 48.97 C 75.91 48.97 73.78 49.47 71.86 50.41 C 71.88 50.4 71.89 50.39 71.9 50.38 C 74.29 48.43 77.32 47.35 80.42 47.35 Z M 88.79 60.62 L 88.79 59.51 L 91.28 59.51 L 91.28 60.62 Z M 66.76 64.65 C 64.6 64.94 62.9 64.68 62.19 64.24 C 62.62 63.94 63.58 63.52 64.26 63.22 C 64.85 62.97 65.44 62.69 66.02 62.4 C 66.96 63.12 68.12 63.66 69.41 63.98 C 68.67 64.28 67.74 64.51 66.76 64.65 Z M 94.67 63.29 C 94.54 63.3 94.41 63.31 94.29 63.32 C 93.86 63.35 93.42 63.4 92.97 63.44 L 92.83 63.46 C 91.4 63.61 89.77 63.77 89 63.67 C 88.51 63.6 87.62 63.21 86.68 62.8 C 85.08 62.11 83.08 61.24 81.28 61.11 C 78.8 60.92 74.52 61.38 71.76 62.68 C 70.16 62.61 68.68 62.18 67.53 61.47 C 67.52 61.25 67.52 61.02 67.52 60.94 C 67.52 57.57 69.09 54.41 71.85 52.27 C 73.72 51.18 75.87 50.6 78.08 50.6 C 83.07 50.6 87.49 53.54 89.21 57.88 L 88.55 57.88 C 87.89 57.88 87.33 58.35 87.2 58.97 C 87.09 58.93 86.98 58.89 86.86 58.84 C 85.21 58.2 82.94 57.33 80.32 57.49 C 76.49 57.71 74.61 57.94 73.13 58.37 L 73.57 59.93 C 74.93 59.54 76.72 59.33 80.42 59.11 C 82.69 58.98 84.76 59.78 86.28 60.36 C 86.6 60.48 86.89 60.59 87.16 60.69 L 87.16 60.86 C 87.16 61.62 87.79 62.25 88.55 62.25 L 91.52 62.25 C 92.28 62.25 92.9 61.63 92.91 60.86 L 93.2 60.84 C 93.68 60.79 94.12 60.75 94.49 60.71 L 94.67 60.7 Z" fill="#bf0816" stroke="none" pointer-events="none"/>
        <ellipse cx="605" cy="377" rx="20" ry="20" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 377px; margin-left: 586px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                SG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="605" y="381" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SG
                </text>
            </switch>
        </g>
        <path d="M 520 357 C 508.97 357 500 365.97 500 377 C 500 388.03 508.97 397 520 397 C 531.03 397 540 388.03 540 377 C 540 365.97 531.03 357 520 357 Z M 520 395.18 C 509.97 395.18 501.82 387.03 501.82 377 C 501.82 366.97 509.97 358.82 520 358.82 C 530.03 358.82 538.18 366.97 538.18 377 C 538.18 387.03 530.03 395.18 520 395.18 Z M 531.85 382.45 L 530.45 382.45 L 530.45 379.39 C 530.45 378.88 530.05 378.48 529.55 378.48 L 527.27 378.48 L 527.27 375.41 C 527.27 374.91 526.87 374.5 526.36 374.5 L 520.91 374.5 L 520.91 372.34 L 526.36 372.34 C 526.87 372.34 527.27 371.93 527.27 371.43 L 527.27 364.27 C 527.27 363.77 526.87 363.36 526.36 363.36 L 513.64 363.36 C 513.13 363.36 512.73 363.77 512.73 364.27 L 512.73 371.43 C 512.73 371.93 513.13 372.34 513.64 372.34 L 519.09 372.34 L 519.09 374.5 L 513.64 374.5 C 513.13 374.5 512.73 374.91 512.73 375.41 L 512.73 378.48 L 510.46 378.48 C 509.95 378.48 509.55 378.88 509.55 379.39 L 509.55 382.45 L 508.15 382.45 C 507.65 382.45 507.24 382.86 507.24 383.36 L 507.24 387.34 C 507.24 387.84 507.65 388.25 508.15 388.25 L 512.05 388.25 C 512.55 388.25 512.96 387.84 512.96 387.34 L 512.96 383.36 C 512.96 382.86 512.55 382.45 512.05 382.45 L 511.36 382.45 L 511.36 380.3 L 515.11 380.3 L 515.11 382.45 L 514.43 382.45 C 513.93 382.45 513.52 382.86 513.52 383.36 L 513.52 387.34 C 513.52 387.84 513.93 388.25 514.43 388.25 L 518.41 388.25 C 518.91 388.25 519.32 387.84 519.32 387.34 L 519.32 383.36 C 519.32 382.86 518.91 382.45 518.41 382.45 L 516.93 382.45 L 516.93 379.39 C 516.93 378.88 516.53 378.48 516.02 378.48 L 514.55 378.48 L 514.55 376.32 L 525.45 376.32 L 525.45 378.48 L 523.98 378.48 C 523.47 378.48 523.07 378.88 523.07 379.39 L 523.07 382.45 L 521.59 382.45 C 521.09 382.45 520.68 382.86 520.68 383.36 L 520.68 387.34 C 520.68 387.84 521.09 388.25 521.59 388.25 L 525.57 388.25 C 526.07 388.25 526.48 387.84 526.48 387.34 L 526.48 383.36 C 526.48 382.86 526.07 382.45 525.57 382.45 L 524.89 382.45 L 524.89 380.3 L 528.64 380.3 L 528.64 382.45 L 527.9 382.45 C 527.4 382.45 526.99 382.86 526.99 383.36 L 526.99 387.34 C 526.99 387.84 527.4 388.25 527.9 388.25 L 531.85 388.25 C 532.35 388.25 532.76 387.84 532.76 387.34 L 532.76 383.36 C 532.76 382.86 532.35 382.45 531.85 382.45 Z M 514.55 370.52 L 514.55 365.18 L 525.45 365.18 L 525.45 370.52 Z M 509.06 386.43 L 509.06 384.27 L 511.14 384.27 L 511.14 386.43 Z M 515.34 386.43 L 515.34 384.27 L 517.5 384.27 L 517.5 386.43 Z M 522.5 386.43 L 522.5 384.27 L 524.66 384.27 L 524.66 386.43 Z M 528.81 386.43 L 528.81 384.27 L 530.94 384.27 L 530.94 386.43 Z" fill="#4d27aa" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 404px; margin-left: 520px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ALB
                                <br/>
                                (Rolling)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="520" y="416" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB...
                </text>
            </switch>
        </g>
        <rect x="140" y="417" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="1 4" pointer-events="none"/>
        <path d="M 170.91 427 C 159.88 427 150.91 435.97 150.91 447 C 150.91 458.03 159.88 467 170.91 467 C 181.94 467 190.91 458.03 190.91 447 C 190.91 435.97 181.94 427 170.91 427 Z M 170.91 465.18 C 160.88 465.18 152.73 457.03 152.73 447 C 152.73 436.97 160.88 428.82 170.91 428.82 C 180.93 428.82 189.09 436.97 189.09 447 C 189.09 457.03 180.93 465.18 170.91 465.18 Z M 182.76 452.45 L 181.36 452.45 L 181.36 449.39 C 181.36 448.88 180.96 448.48 180.45 448.48 L 178.18 448.48 L 178.18 445.41 C 178.18 444.91 177.77 444.5 177.27 444.5 L 171.82 444.5 L 171.82 442.34 L 177.27 442.34 C 177.77 442.34 178.18 441.93 178.18 441.43 L 178.18 434.27 C 178.18 433.77 177.77 433.36 177.27 433.36 L 164.55 433.36 C 164.04 433.36 163.64 433.77 163.64 434.27 L 163.64 441.43 C 163.64 441.93 164.04 442.34 164.55 442.34 L 170 442.34 L 170 444.5 L 164.55 444.5 C 164.04 444.5 163.64 444.91 163.64 445.41 L 163.64 448.48 L 161.36 448.48 C 160.86 448.48 160.46 448.88 160.46 449.39 L 160.46 452.45 L 159.06 452.45 C 158.56 452.45 158.15 452.86 158.15 453.36 L 158.15 457.34 C 158.15 457.84 158.56 458.25 159.06 458.25 L 162.96 458.25 C 163.46 458.25 163.86 457.84 163.86 457.34 L 163.86 453.36 C 163.86 452.86 163.46 452.45 162.96 452.45 L 162.27 452.45 L 162.27 450.3 L 166.02 450.3 L 166.02 452.45 L 165.34 452.45 C 164.84 452.45 164.43 452.86 164.43 453.36 L 164.43 457.34 C 164.43 457.84 164.84 458.25 165.34 458.25 L 169.32 458.25 C 169.82 458.25 170.23 457.84 170.23 457.34 L 170.23 453.36 C 170.23 452.86 169.82 452.45 169.32 452.45 L 167.84 452.45 L 167.84 449.39 C 167.84 448.88 167.43 448.48 166.93 448.48 L 165.46 448.48 L 165.46 446.32 L 176.36 446.32 L 176.36 448.48 L 174.89 448.48 C 174.38 448.48 173.98 448.88 173.98 449.39 L 173.98 452.45 L 172.5 452.45 C 172 452.45 171.59 452.86 171.59 453.36 L 171.59 457.34 C 171.59 457.84 172 458.25 172.5 458.25 L 176.48 458.25 C 176.98 458.25 177.39 457.84 177.39 457.34 L 177.39 453.36 C 177.39 452.86 176.98 452.45 176.48 452.45 L 175.8 452.45 L 175.8 450.3 L 179.55 450.3 L 179.55 452.45 L 178.81 452.45 C 178.31 452.45 177.9 452.86 177.9 453.36 L 177.9 457.34 C 177.9 457.84 178.31 458.25 178.81 458.25 L 182.76 458.25 C 183.26 458.25 183.67 457.84 183.67 457.34 L 183.67 453.36 C 183.67 452.86 183.26 452.45 182.76 452.45 Z M 165.46 440.52 L 165.46 435.18 L 176.36 435.18 L 176.36 440.52 Z M 159.97 456.43 L 159.97 454.27 L 162.05 454.27 L 162.05 456.43 Z M 166.25 456.43 L 166.25 454.27 L 168.41 454.27 L 168.41 456.43 Z M 173.41 456.43 L 173.41 454.27 L 175.57 454.27 L 175.57 456.43 Z M 179.72 456.43 L 179.72 454.27 L 181.85 454.27 L 181.85 456.43 Z" fill="#4d27aa" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 474px; margin-left: 171px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ALB
                                <br/>
                                (BG)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="171" y="486" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB...
                </text>
            </switch>
        </g>
        <rect x="205.45" y="427" width="43.64" height="30" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 42px; height: 1px; padding-top: 442px; margin-left: 206px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                Blue
                                <br/>
                                Listener
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="227" y="445" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    Blue...
                </text>
            </switch>
        </g>
        <rect x="205.45" y="477" width="43.64" height="30" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 42px; height: 1px; padding-top: 492px; margin-left: 206px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                Gleen
                                <br/>
                                Listener
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="227" y="495" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    Gleen...
                </text>
            </switch>
        </g>
        <ellipse cx="360" cy="397" rx="20" ry="20" fill="none" stroke="rgb(0, 0, 0)" stroke-opacity="0.5" pointer-events="none"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>