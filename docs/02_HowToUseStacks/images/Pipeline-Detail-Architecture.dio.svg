<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="697px" height="278px" viewBox="-0.5 -0.5 697 278" content="&lt;mxfile&gt;&lt;diagram name=&quot;240826&quot; id=&quot;PJQ13wmb1EqW22s4T2RU&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;XiMurMHVUEbgKz0XYkYH&quot; name=&quot;240816&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0">
            <stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="256" y="117" width="240" height="140" fill="none" stroke="#4d72f3" stroke-dasharray="3 3" pointer-events="none"/>
        <path d="M 256 117 L 276 117 L 276 137 L 256 137 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 272.41 119 L 259.59 119 C 258.71 119 258 119.71 258 120.59 L 258 124.39 C 258 125.27 258.71 125.98 259.59 125.98 L 260.04 125.98 L 260.04 134.71 C 260.04 134.87 260.17 135 260.33 135 L 271.38 135 C 271.54 135 271.67 134.87 271.67 134.71 L 271.67 125.98 L 272.41 125.98 C 273.29 125.98 274 125.27 274 124.39 L 274 120.59 C 274 119.71 273.29 119 272.41 119 Z M 260.62 134.42 L 260.62 125.98 L 271.09 125.98 L 271.09 134.42 Z M 272.41 125.4 L 259.59 125.4 C 259.03 125.4 258.58 124.95 258.58 124.39 L 258.58 124.24 L 262.07 124.24 L 262.07 123.65 L 258.58 123.65 L 258.58 120.59 C 258.58 120.03 259.03 119.58 259.59 119.58 L 272.41 119.58 C 272.97 119.58 273.42 120.03 273.42 120.59 L 273.42 123.65 L 265.27 123.65 L 265.27 124.24 L 273.42 124.24 L 273.42 124.39 C 273.42 124.95 272.97 125.4 272.41 125.4 Z M 261.64 130.07 C 261.64 129.98 261.67 129.9 261.74 129.85 L 263.58 128.22 L 263.97 128.66 L 262.37 130.06 L 263.96 131.43 L 263.58 131.87 L 261.74 130.29 C 261.68 130.23 261.64 130.15 261.64 130.07 Z M 267.48 131.45 L 269.07 130.04 L 267.48 128.66 L 267.86 128.22 L 269.71 129.82 C 269.77 129.88 269.81 129.96 269.81 130.04 C 269.81 130.12 269.77 130.2 269.71 130.26 L 267.86 131.88 Z M 264.88 133.01 L 264.35 132.79 L 266.56 127.4 L 267.09 127.62 Z M 262.95 124.24 L 264.4 124.24 L 264.4 123.65 L 262.95 123.65 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 127px; margin-left: 278px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                CodePipeline
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="278" y="131" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Code...
                </text>
            </switch>
        </g>
        <rect x="136" y="17" width="480" height="260" rx="39" ry="39" fill="none" stroke="#000000" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 478px; height: 1px; padding-top: 14px; margin-left: 137px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                pipeline-infraresources-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="376" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    pipeline-infraresources-stack
                </text>
            </switch>
        </g>
        <path d="M 416 167 L 456 167 L 456 207 L 416 207 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 449.99 177.08 L 446.71 174.08 L 443.43 177.08 Z M 445.46 173.74 L 438.8 173.75 L 442.35 176.58 Z M 442.45 183.42 L 437.22 185.35 L 447.29 185.35 Z M 434.76 196.37 L 449.53 196.37 L 449.53 186.45 L 434.76 186.45 Z M 441.22 177.08 L 437.6 174.2 L 433.99 177.08 Z M 432.84 176.6 L 436.39 173.76 L 429.32 173.77 Z M 431.69 177.08 L 428.07 174.18 L 424.45 177.08 L 429.84 177.08 Z M 429.3 178.18 L 424.87 178.18 L 429.3 182.57 Z M 429.3 184.29 L 424.1 189.02 L 429.3 194.1 Z M 429.3 195.89 L 423.83 201.24 L 423.83 201.32 L 429.3 201.32 Z M 423.83 190.29 L 423.83 199.7 L 428.64 195 Z M 423.83 187.78 L 428.61 183.43 L 423.83 178.7 Z M 423.28 176.61 L 426.82 173.77 L 423.28 173.78 Z M 422.19 172.68 L 421.09 172.68 L 421.09 178.18 L 422.19 178.18 L 422.19 177.63 L 422.19 173.23 Z M 451.92 177.83 C 451.84 178.04 451.63 178.18 451.41 178.18 L 442.97 178.18 L 442.97 182.59 L 450.28 185.39 L 450.28 185.39 C 450.48 185.47 450.62 185.67 450.62 185.9 L 450.62 196.92 C 450.62 197.22 450.38 197.47 450.08 197.47 L 434.22 197.47 C 433.92 197.47 433.67 197.22 433.67 196.92 L 433.67 185.9 C 433.67 185.66 433.82 185.46 434.03 185.39 L 434.03 185.38 L 441.87 182.59 L 441.87 178.18 L 430.39 178.18 L 430.39 201.88 C 430.39 202.18 430.15 202.43 429.84 202.43 L 423.28 202.43 C 422.98 202.43 422.73 202.18 422.73 201.88 L 422.73 179.29 L 420.55 179.29 C 420.25 179.29 420 179.04 420 178.74 L 420 172.12 C 420 171.82 420.25 171.57 420.55 171.57 L 422.73 171.57 C 423.04 171.57 423.28 171.82 423.28 172.12 L 423.28 172.67 L 446.79 172.68 L 451.78 177.23 C 451.94 177.38 452 177.62 451.92 177.83 Z M 440.57 195.43 L 443.75 188.15 L 442.75 187.7 L 439.57 194.99 Z M 443.56 193 L 444.27 193.84 L 446.63 191.82 C 446.75 191.73 446.82 191.58 446.82 191.43 C 446.83 191.28 446.77 191.13 446.67 191.02 L 444.69 189 L 443.92 189.78 L 445.47 191.37 Z M 436.46 191.4 C 436.34 191.29 436.27 191.13 436.28 190.96 C 436.29 190.8 436.37 190.65 436.5 190.55 L 439.19 188.55 L 439.84 189.43 L 437.69 191.04 L 439.49 192.7 L 438.75 193.52 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 214px; margin-left: 436px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                CodeBuild
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="436" y="226" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CodeBu...
                </text>
            </switch>
        </g>
        <path d="M 296 47 L 336 47 L 336 87 L 296 87 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 314.6 57.18 L 316.84 57.18 L 316.84 56.05 L 314.6 56.05 Z M 310.67 57.18 L 312.91 57.18 L 312.91 56.05 L 310.67 56.05 Z M 306.74 57.18 L 308.98 57.18 L 308.98 56.05 L 306.74 56.05 Z M 330.15 73.09 C 329.98 73.49 329.62 73.79 329.19 73.93 L 329.19 70.76 C 329.53 70.88 329.83 71.1 330.04 71.41 C 330.36 71.9 330.4 72.51 330.15 73.09 Z M 301.85 73.09 C 301.6 72.51 301.64 71.9 301.96 71.41 C 302.17 71.1 302.47 70.88 302.81 70.76 L 302.81 73.93 C 302.38 73.79 302.02 73.49 301.85 73.09 Z M 330.98 70.79 C 330.56 70.16 329.92 69.74 329.19 69.59 L 329.19 68.59 C 329.19 66.63 327.68 65.04 325.82 65.04 L 315.16 65.04 L 315.16 66.16 L 325.82 66.16 C 327.06 66.16 328.07 67.25 328.07 68.59 L 328.07 76.08 C 328.07 77.42 327.06 78.51 325.82 78.51 L 306.18 78.51 C 304.94 78.51 303.93 77.42 303.93 76.08 L 303.93 68.59 C 303.93 67.25 304.94 66.16 306.18 66.16 L 308.98 66.16 L 308.98 65.04 L 306.18 65.04 C 304.32 65.04 302.81 66.63 302.81 68.59 L 302.81 69.59 C 302.08 69.74 301.44 70.16 301.02 70.79 C 300.49 71.6 300.41 72.6 300.82 73.54 C 301.17 74.35 301.92 74.92 302.81 75.09 L 302.81 76.08 C 302.81 78.04 304.32 79.63 306.18 79.63 L 312.91 79.63 L 312.91 83 L 314.04 83 L 314.04 79.63 L 317.4 79.63 L 317.4 83 L 318.53 83 L 318.53 79.63 L 325.82 79.63 C 327.68 79.63 329.19 78.04 329.19 76.08 L 329.19 75.09 C 330.08 74.92 330.83 74.35 331.18 73.54 C 331.59 72.6 331.51 71.6 330.98 70.79 Z M 311.23 72.33 C 311.23 73.26 310.47 74.02 309.54 74.02 C 308.62 74.02 307.86 73.26 307.86 72.33 C 307.86 71.4 308.62 70.65 309.54 70.65 C 310.47 70.65 311.23 71.4 311.23 72.33 Z M 306.74 72.33 C 306.74 73.88 308 75.14 309.54 75.14 C 311.09 75.14 312.35 73.88 312.35 72.33 C 312.35 70.79 311.09 69.53 309.54 69.53 C 308 69.53 306.74 70.79 306.74 72.33 Z M 320.77 72.33 C 320.77 71.4 321.53 70.65 322.46 70.65 C 323.38 70.65 324.14 71.4 324.14 72.33 C 324.14 73.26 323.38 74.02 322.46 74.02 C 321.53 74.02 320.77 73.26 320.77 72.33 Z M 325.26 72.33 C 325.26 70.79 324 69.53 322.46 69.53 C 320.91 69.53 319.65 70.79 319.65 72.33 C 319.65 73.88 320.91 75.14 322.46 75.14 C 324 75.14 325.26 73.88 325.26 72.33 Z M 304.49 52.6 C 304.49 52.34 304.71 52.12 304.97 52.12 L 319.09 52.12 L 319.09 61.17 C 319.09 61.44 318.86 61.67 318.59 61.67 L 312.91 61.67 C 312.6 61.67 312.35 61.92 312.35 62.23 L 312.35 65.14 L 309.87 61.89 C 309.76 61.75 309.6 61.67 309.42 61.67 L 304.97 61.67 C 304.71 61.67 304.49 61.45 304.49 61.19 Z M 304.97 62.79 L 309.15 62.79 L 312.47 67.14 C 312.57 67.28 312.74 67.36 312.91 67.36 C 312.97 67.36 313.03 67.35 313.09 67.33 C 313.32 67.26 313.47 67.04 313.47 66.8 L 313.47 62.79 L 318.59 62.79 C 319.48 62.79 320.21 62.06 320.21 61.17 L 320.21 51.86 C 320.21 51.38 319.83 51 319.35 51 L 304.97 51 C 304.09 51 303.37 51.72 303.37 52.6 L 303.37 61.19 C 303.37 62.07 304.09 62.79 304.97 62.79 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 94px; margin-left: 316px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ChatBot
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="316" y="106" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ChatBot
                </text>
            </switch>
        </g>
        <path d="M 336 187 L 409.63 187" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 414.88 187 L 407.88 190.5 L 409.63 187 L 407.88 183.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 376px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ビルド
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="376" y="190" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ビルド
                </text>
            </switch>
        </g>
        <path d="M 296 167 L 336 167 L 336 207 L 296 207 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 327.94 188.65 L 328.16 187.11 C 330.18 188.32 330.21 188.82 330.21 188.83 C 330.2 188.84 329.86 189.13 327.94 188.65 Z M 326.83 188.34 C 323.33 187.29 318.46 185.05 316.49 184.12 C 316.49 184.11 316.49 184.11 316.49 184.1 C 316.49 183.34 315.88 182.72 315.12 182.72 C 314.36 182.72 313.75 183.34 313.75 184.1 C 313.75 184.85 314.36 185.47 315.12 185.47 C 315.45 185.47 315.75 185.35 315.99 185.15 C 318.31 186.25 323.14 188.45 326.67 189.49 L 325.27 199.32 C 325.27 199.35 325.27 199.37 325.27 199.4 C 325.27 200.27 321.43 201.86 315.17 201.86 C 308.84 201.86 304.97 200.27 304.97 199.4 C 304.97 199.37 304.97 199.35 304.97 199.32 L 302.05 178.06 C 304.57 179.8 309.99 180.71 315.18 180.71 C 320.35 180.71 325.76 179.8 328.29 178.07 Z M 301.75 175.84 C 301.79 175.09 306.11 172.14 315.18 172.14 C 324.24 172.14 328.56 175.09 328.6 175.84 L 328.6 176.1 C 328.11 177.79 322.51 179.57 315.18 179.57 C 307.83 179.57 302.23 177.78 301.75 176.09 Z M 329.75 175.86 C 329.75 173.88 324.07 171 315.18 171 C 306.28 171 300.6 173.88 300.6 175.86 L 300.66 176.29 L 303.83 199.44 C 303.9 202.03 310.81 203 315.17 203 C 320.59 203 326.34 201.76 326.41 199.45 L 327.78 189.79 C 328.54 189.97 329.17 190.07 329.67 190.07 C 330.35 190.07 330.8 189.9 331.08 189.57 C 331.31 189.3 331.4 188.97 331.33 188.62 C 331.18 187.83 330.24 186.98 328.33 185.89 L 329.69 176.31 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 214px; margin-left: 316px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                S3 Bucket
                                <br/>
                                (SourceArtifact)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="316" y="226" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buc...
                </text>
            </switch>
        </g>
        <path d="M 196 167 L 196 83.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 196 78.12 L 199.5 85.12 L 196 83.37 L 192.5 85.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 122px; margin-left: 196px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                イベント
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="196" y="126" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    イベント
                </text>
            </switch>
        </g>
        <path d="M 176 167 L 216 167 L 216 207 L 176 207 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 207.94 188.65 L 208.16 187.11 C 210.18 188.32 210.21 188.82 210.21 188.83 C 210.2 188.84 209.86 189.13 207.94 188.65 Z M 206.83 188.34 C 203.33 187.29 198.46 185.05 196.49 184.12 C 196.49 184.11 196.49 184.11 196.49 184.1 C 196.49 183.34 195.88 182.72 195.12 182.72 C 194.36 182.72 193.75 183.34 193.75 184.1 C 193.75 184.85 194.36 185.47 195.12 185.47 C 195.45 185.47 195.75 185.35 195.99 185.15 C 198.31 186.25 203.14 188.45 206.67 189.49 L 205.27 199.32 C 205.27 199.35 205.27 199.37 205.27 199.4 C 205.27 200.27 201.43 201.86 195.17 201.86 C 188.84 201.86 184.97 200.27 184.97 199.4 C 184.97 199.37 184.97 199.35 184.97 199.32 L 182.05 178.06 C 184.57 179.8 189.99 180.71 195.18 180.71 C 200.35 180.71 205.76 179.8 208.29 178.07 Z M 181.75 175.84 C 181.79 175.09 186.11 172.14 195.18 172.14 C 204.24 172.14 208.56 175.09 208.6 175.84 L 208.6 176.1 C 208.11 177.79 202.51 179.57 195.18 179.57 C 187.83 179.57 182.23 177.78 181.75 176.09 Z M 209.75 175.86 C 209.75 173.88 204.07 171 195.18 171 C 186.28 171 180.6 173.88 180.6 175.86 L 180.66 176.29 L 183.83 199.44 C 183.9 202.03 190.81 203 195.17 203 C 200.59 203 206.34 201.76 206.41 199.45 L 207.78 189.79 C 208.54 189.97 209.17 190.07 209.67 190.07 C 210.35 190.07 210.8 189.9 211.08 189.57 C 211.31 189.3 211.4 188.97 211.33 188.62 C 211.18 187.83 210.24 186.98 208.33 185.89 L 209.69 176.31 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 214px; margin-left: 196px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                S3 Bucket
                                <br/>
                                (SourceBucket)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="196" y="226" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buc...
                </text>
            </switch>
        </g>
        <path d="M 216 187 L 289.63 187" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 294.88 187 L 287.88 190.5 L 289.63 187 L 287.88 183.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 256px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                トリガー
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="256" y="190" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    トリガー
                </text>
            </switch>
        </g>
        <path d="M 56 187 L 169.63 187" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 174.88 187 L 167.88 190.5 L 169.63 187 L 167.88 183.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 116px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                アップロード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="116" y="190" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    アップロード
                </text>
            </switch>
        </g>
        <path d="M 216 67 L 289.63 67" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 294.88 67 L 287.88 70.5 L 289.63 67 L 287.88 63.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 67px; margin-left: 256px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                トリガー
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="256" y="70" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    トリガー
                </text>
            </switch>
        </g>
        <path d="M 193.04 67.82 L 193.04 66 L 212.8 66 L 208.95 62.15 L 210.24 60.86 L 215.64 66.27 C 216 66.62 216 67.2 215.64 67.55 L 215.64 67.55 L 210.24 72.96 L 208.95 71.67 L 212.8 67.82 Z M 201.19 51.36 L 199.52 50.65 L 197.28 55.89 L 198.96 56.6 Z M 208.07 57.22 L 206.98 55.76 L 202.39 59.21 L 203.49 60.66 Z M 199.93 76.83 L 203.4 81.35 L 204.85 80.24 L 201.38 75.72 Z M 194.09 78.68 L 194.8 84.31 L 196.61 84.08 L 195.9 78.45 Z M 185.87 82.64 L 187.54 83.35 L 189.78 78.11 L 188.11 77.4 Z M 178.99 76.78 L 180.08 78.24 L 184.67 74.79 L 183.57 73.34 Z M 181.71 67.58 L 176 68.3 L 176.23 70.11 L 181.93 69.39 Z M 183.01 61.66 L 177.71 59.47 L 177.01 61.15 L 182.31 63.34 Z M 187.13 57.17 L 183.66 52.65 L 182.22 53.76 L 185.69 58.28 Z M 192.97 55.32 L 192.26 49.69 L 190.45 49.92 L 191.16 55.55 Z M 200.47 71.19 L 198.39 74.8 C 198.22 75.08 197.92 75.26 197.6 75.26 L 189.55 75.26 C 189.22 75.26 188.92 75.08 188.76 74.8 L 184.73 67.83 C 184.57 67.55 184.57 67.2 184.73 66.92 L 188.76 59.95 C 188.92 59.66 189.22 59.49 189.55 59.49 L 197.6 59.49 C 197.92 59.49 198.22 59.66 198.39 59.95 L 199.76 62.33 L 198.18 63.24 L 197.07 61.31 L 190.07 61.31 L 186.57 67.37 L 190.07 73.44 L 197.07 73.44 L 198.89 70.28 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <image x="655.5" y="46.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAACjElEQVR4nO2WT26bQBTGWZn2CE0806pdeG9b2UVOLlJVTOUDVLg3qiIQCkcoyCy6jX2FtjIxRjHq/lWPxhjzd8bgglue9K14Qt9v5s18I0ldddVVpbq2gUzmYNzMIQjlgHnrwCDZN15+IsPFzBgu1AA1Wqrm+GGW6mvC/ObGAUjIx28J85vRcgaHUn381hhAuPJp8zvpuz5c+bT5SHpjAM8jkwkwmcN2D4Ajkw0wXMy2rQRA7foKVh9QzQE4YJ41wK0DAzywZwuAhbcNHlic+bME4KkOoFK6FhzSlHKS+K/uwHV+uoroIIlFAMZVnxwl6SoiXRRgXMeTQ3hsOJKYF2BYx5OjRoAncQC1+pOjLF25ARzQeIyNlion6L6vUrpyrr43+Qb9vTHVLDCnce5U1FcpXcvmHlc+bh4LbxE8iOmxUL2rh88x0LzxOexrpPAWwYOIs4zCFb1KmMqa+6y+f78e6fSVS5QvLlXWLmUQV35f2Hvnkum75pxLkuT131+6lD0mjScB8vuU9ZooF/F/gv2CgCUbYMkB2DJwy5IDsGQTnB5/Eq8o0/LMxwFK+u4S5jdCxu0UiI//4QMgbMsFUNC3Ikp0Z4crX8W8HYkviVeEBfnGWJSGRZBxUOGxsXN3gS+JXcLuCwCMxgBsmS+Jf75RXq+o4qfMU8X/8XZKjwAwawLQuAB2EM878SsUYfdx80IATm/w5xBWMd/z4OvLesOMFyC6iWxZxzkWnXuwZa1286IArSz3fwGAOtK1VQDHpGsrAUTStbUAFme6nqJ4nxwlSfzUJICRC0CZzpnEWmMA3y8+EJcqmwwAz7v82C9P4hOk6zEQuNo4MqEo0+LmM5P4lOnaVVddSWdXvwEVBGzQ4NLH4wAAAABJRU5ErkJggg==" preserveAspectRatio="none" pointer-events="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="661" y="95" width="31" height="15" stroke-width="0"/>
            <text x="675.5" y="104.5">
                Slack
            </text>
        </g>
        <path d="M 336 67 L 649.63 67" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 654.88 67 L 647.88 70.5 L 649.63 67 L 647.88 63.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 67px; margin-left: 496px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                通知
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="496" y="70" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    通知
                </text>
            </switch>
        </g>
        <path d="M 657.82 204.78 L 657.82 196.26 L 666.34 196.26 L 666.34 204.78 Z M 656 206.6 L 668.16 206.6 L 668.16 194.44 L 656 194.44 Z M 671.34 204.78 L 671.34 196.26 L 680.66 196.26 L 680.66 204.78 Z M 669.52 206.6 L 682.48 206.6 L 682.48 194.44 L 669.52 194.44 Z M 685.66 204.78 L 685.66 196.26 L 694.18 196.26 L 694.18 204.78 Z M 683.84 206.6 L 696 206.6 L 696 194.44 L 683.84 194.44 Z M 657.82 191.26 L 657.82 182.74 L 673.5 182.74 L 673.5 191.26 Z M 656 193.08 L 675.32 193.08 L 675.32 180.92 L 656 180.92 Z M 678.5 191.26 L 678.5 182.74 L 694.18 182.74 L 694.18 191.26 Z M 676.68 193.08 L 696 193.08 L 696 180.92 L 676.68 180.92 Z M 657.82 177.74 L 657.82 169.22 L 694.18 169.22 L 694.18 177.74 Z M 656 179.56 L 696 179.56 L 696 167.4 L 656 167.4 Z" fill="#b0084d" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 214px; margin-left: 676px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="676" y="226" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Stack
                </text>
            </switch>
        </g>
        <path d="M 456 187 L 649.63 187" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 654.88 187 L 647.88 190.5 L 649.63 187 L 647.88 183.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 556px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                デプロイ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="556" y="190" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    デプロイ
                </text>
            </switch>
        </g>
        <image x="15.5" y="166.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,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" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 214px; margin-left: 36px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                CDKスタック
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="36" y="226" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CDKスタック
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>