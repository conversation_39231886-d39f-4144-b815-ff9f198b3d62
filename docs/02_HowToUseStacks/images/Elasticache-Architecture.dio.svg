<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="161px" height="118px" viewBox="-0.5 -0.5 161 118" content="&lt;mxfile host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36&quot; version=&quot;24.8.4&quot; pages=&quot;2&quot;&gt;&#10;  &lt;diagram name=&quot;241113&quot; id=&quot;zPm-x9WjjF1-WoX4LGxz&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1539&quot; dy=&quot;818&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;U_Ui470bCDtXqS-rPKAd-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;U_Ui470bCDtXqS-rPKAd-1&quot; parent=&quot;U_Ui470bCDtXqS-rPKAd-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;U_Ui470bCDtXqS-rPKAd-3&quot; value=&quot;elasticache-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;U_Ui470bCDtXqS-rPKAd-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;300&quot; y=&quot;260&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;My1dqXmneihLRPVb3fgY-0&quot; value=&quot;ElastiCache&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticache;&quot; vertex=&quot;1&quot; parent=&quot;U_Ui470bCDtXqS-rPKAd-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;356&quot; y=&quot;280&quot; width=&quot;48&quot; height=&quot;48&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;iPCpmYeHDqjBAoUQL4SW&quot; name=&quot;240822&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1539&quot; dy=&quot;818&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;2&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;360&quot; y=&quot;280&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3&quot; value=&quot;elasticache-redis-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;300&quot; y=&quot;260&quot; width=&quot;160&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs/><g><g data-cell-id="U_Ui470bCDtXqS-rPKAd-0"><g data-cell-id="U_Ui470bCDtXqS-rPKAd-1"><g data-cell-id="U_Ui470bCDtXqS-rPKAd-3"><g><rect x="0" y="17" width="160" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 14px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">elasticache-stack</div></div></div></foreignObject><text x="80" y="14" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">elasticache-stack</text></switch></g></g></g><g data-cell-id="My1dqXmneihLRPVb3fgY-0"><g><path d="M 56 37 L 104 37 L 104 85 L 56 85 Z" fill="#c925d1" stroke="none" pointer-events="all"/><path d="M 87.54 75.44 L 87.54 71.42 C 85.81 72.55 82.68 73.08 79.7 73.08 C 76.45 73.08 73.91 72.51 72.46 71.54 L 72.46 75.44 C 72.46 76.6 75.16 77.8 79.7 77.8 C 84.32 77.8 87.54 76.56 87.54 75.44 Z M 79.7 66.98 C 76.45 66.98 73.91 66.41 72.46 65.44 L 72.46 69.36 C 72.48 70.51 75.18 71.71 79.7 71.71 C 84.31 71.71 87.52 70.47 87.54 69.35 L 87.54 65.33 C 85.81 66.45 82.68 66.98 79.7 66.98 Z M 87.54 63.26 L 87.54 58.62 C 85.81 59.74 82.68 60.27 79.7 60.27 C 76.45 60.27 73.91 59.71 72.46 58.73 L 72.46 63.26 C 72.48 64.42 75.18 65.61 79.7 65.61 C 84.31 65.61 87.52 64.37 87.54 63.26 Z M 72.46 56.54 C 72.46 56.54 72.46 56.54 72.46 56.54 L 72.46 56.54 L 72.46 56.55 C 72.48 57.71 75.18 58.9 79.7 58.9 C 84.73 58.9 87.52 57.51 87.54 56.55 L 87.54 56.54 L 87.54 56.54 C 87.54 56.54 87.54 56.54 87.54 56.54 C 87.54 55.57 84.75 54.18 79.7 54.18 C 75.16 54.18 72.46 55.38 72.46 56.54 Z M 88.91 56.56 L 88.91 63.25 L 88.92 63.25 C 88.92 63.25 88.91 63.26 88.91 63.26 L 88.91 69.34 L 88.92 69.34 C 88.92 69.35 88.91 69.35 88.91 69.36 L 88.91 75.44 C 88.91 78 84.14 79.17 79.7 79.17 C 74.47 79.17 71.09 77.71 71.09 75.44 L 71.09 69.36 C 71.09 69.36 71.08 69.35 71.08 69.34 L 71.09 69.34 L 71.09 63.27 C 71.09 63.26 71.08 63.25 71.08 63.25 L 71.09 63.25 L 71.09 56.56 C 71.09 56.55 71.08 56.55 71.08 56.54 C 71.08 54.27 74.46 52.8 79.7 52.8 C 84.14 52.8 88.92 53.97 88.92 56.54 C 88.92 56.54 88.91 56.55 88.91 56.56 Z M 98.51 47.71 C 98.89 47.71 99.2 47.4 99.2 47.03 L 99.2 43.51 C 99.2 43.14 98.89 42.83 98.51 42.83 L 61.49 42.83 C 61.11 42.83 60.8 43.14 60.8 43.51 L 60.8 47.03 C 60.8 47.4 61.11 47.71 61.49 47.71 C 62.32 47.71 63 48.39 63 49.22 C 63 50.05 62.32 50.73 61.49 50.73 C 61.11 50.73 60.8 51.04 60.8 51.41 L 60.8 65.46 C 60.8 65.84 61.11 66.14 61.49 66.14 L 68.34 66.14 L 68.34 64.77 L 64.91 64.77 L 64.91 62.72 L 68.34 62.72 L 68.34 61.34 L 64.23 61.34 C 63.85 61.34 63.54 61.65 63.54 62.03 L 63.54 64.77 L 62.17 64.77 L 62.17 52.02 C 63.44 51.71 64.38 50.57 64.38 49.22 C 64.38 47.87 63.44 46.73 62.17 46.42 L 62.17 44.2 L 97.83 44.2 L 97.83 46.42 C 96.56 46.73 95.62 47.87 95.62 49.22 C 95.62 50.57 96.56 51.71 97.83 52.02 L 97.83 64.77 L 96.46 64.77 L 96.46 62.03 C 96.46 61.65 96.15 61.34 95.77 61.34 L 91.66 61.34 L 91.66 62.72 L 95.09 62.72 L 95.09 64.77 L 91.66 64.77 L 91.66 66.14 L 98.51 66.14 C 98.89 66.14 99.2 65.84 99.2 65.46 L 99.2 51.41 C 99.2 51.04 98.89 50.73 98.51 50.73 C 97.68 50.73 97 50.05 97 49.22 C 97 48.39 97.68 47.71 98.51 47.71 Z M 71.77 52.43 L 71.77 46.94 C 71.77 46.56 71.46 46.26 71.09 46.26 L 66.97 46.26 C 66.59 46.26 66.29 46.56 66.29 46.94 L 66.29 58.6 C 66.29 58.98 66.59 59.29 66.97 59.29 L 69.03 59.29 L 69.03 57.92 L 67.66 57.92 L 67.66 47.63 L 70.4 47.63 L 70.4 52.43 Z M 92.34 57.92 L 91.66 57.92 L 91.66 59.29 L 93.03 59.29 C 93.41 59.29 93.71 58.98 93.71 58.6 L 93.71 46.94 C 93.71 46.56 93.41 46.26 93.03 46.26 L 88.91 46.26 C 88.54 46.26 88.23 46.56 88.23 46.94 L 88.23 52.43 L 89.6 52.43 L 89.6 47.63 L 92.34 47.63 Z M 86.86 51.74 L 86.86 46.94 C 86.86 46.56 86.55 46.26 86.17 46.26 L 81.37 46.26 C 80.99 46.26 80.69 46.56 80.69 46.94 L 80.69 51.06 L 82.06 51.06 L 82.06 47.63 L 85.49 47.63 L 85.49 51.74 Z M 77.94 51.06 L 77.94 47.63 L 74.51 47.63 L 74.51 51.74 L 73.14 51.74 L 73.14 46.94 C 73.14 46.56 73.45 46.26 73.83 46.26 L 78.63 46.26 C 79.01 46.26 79.31 46.56 79.31 46.94 L 79.31 51.06 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 92px; margin-left: 80px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ElastiCache</div></div></div></foreignObject><text x="80" y="104" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">ElastiCa...</text></switch></g></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>