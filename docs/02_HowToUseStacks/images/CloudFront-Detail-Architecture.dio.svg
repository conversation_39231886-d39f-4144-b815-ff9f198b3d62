<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="552px" height="562px" viewBox="-0.5 -0.5 552 562" content="&lt;mxfile&gt;&lt;diagram id=&quot;5WF90XhXiXhQGp5iWgBa&quot; name=&quot;240924&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;240902&quot; id=&quot;NFDjqxnjrLUd6tlO83R2&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;240828&quot; id=&quot;2zO62NsWnCo3Twle0CtQ&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;240820&quot; id=&quot;ByUj7Mviy0TtkZ0kAsi4&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;eDpK0C9BMz00SFkVoKBU&quot; name=&quot;240819&quot;&gt;7Vpbb+I6EP41kc6+VCTO9TEJsHukHqlSH7r7tDKJCdGaOHJCgfPrd0xscqWi2m5od9OC8Hwek/F4vvEER0Ph9vCZ43zzH4sJ1YxZfNDQXDMM3XYRfAjkKBEd2RWS8DSWWA08pv8TCc4kuktjUrQUS8ZomeZtMGJZRqKyhWHO2b6ttma0fdUcJ6QHPEaY9tGnNC43FeoaTo1/IWmyUVfWba/q2WKlLGdSbHDM9g0ILTQUcsbKqrU9hIQK7ym/VOOWF3rPhnGSlVcNkHY8Y7qTk9MWSIMV8uenhqH5C4kESCKBMr48Ko+QGBwkxYxl8BFsyi0FSYcmGMOPX0GY3VlK/CZEJcwPLenYlB4IT7ekJFyC/SnKWRdsxyNpjyGjAvOESC25BMLSxjDpls+EwTX4ERQ4obhMn9tLjWXEJGe92qnQkH694GNj2MfgWl8521eudWtnf0Afu7fycd/FIWW7eMkZTKDryOIHKaONnGrO0qw8XdkK4AWhHVZvC1RDgdwZ1gA4hDl9UO+rwYc+dIUuOIQ5fVDvqwlJWd0GhzDH6lvcHa0PjNY7o+GFArYraZqR8Jx5hY/XsAoho4yf/I/gfymWMUg4jlPS6vNMa740Gn3zlMMXpSw7RT0X+TNYp5Q2xlg+mgUi6IuSsx+k0bM+/UFPjIsNiaU5z4SXKWTze7wi9IEVqfz6FStLtm0o+DRNREfJckCxlCKwSjClyT4xQ7lP6YaSZcSJS+Iir9yxTg/CjgASfy46t4dEbJJ3eF+Yd5xU/Po3EvYEIFattlYkInt9iuwXmCrmQA4vslD1mjKfy+0YeVLe13ubUtk0tjWF/QpvUY+3T/5SM2wq5rbi0EpE658nsgI1P7z/NLH5A7F5aZmO6b2OzaGjI33517B5j9dvQ2MHtWlsjEhjq0dj/z54mam/FFqy/OlEjjk3HKikupEjld9d0JxyFeGLZ1KlLP1SIOE8p2CLMPE7ZTj+vsIUZ9HJgLcIHKMdOOZsvMCxe4HziAbSf4H8KCJFcc+SIthFEEfTPvCR9gF75iPkvG4fMBwHbpz/mn2gQG/DZsNps9kZkc2G+e5vWp3+Tat9q5tW57rkB7XvlPOmnDflvIs5z7TaOc9CI+Y8693nPHeEnCeHPogcVC8M6pSWtt3xeGWTHNVx+tmMq9bBvS6ZhpQVJJ7y6ZRPp3x6uYacdfKpMV4+9a7msfx5H+4JJzpPdJ7ofPkHntuxWTd7dJYHnK4jG56lTkM9dRoaKB33lbXVVeWQjvr1kDVOPWR3f2rzfls9pPfvLrWFp3lzzQM/O5rna66plkCdQwdhfQ793qvaoWV0bnUrfyblH/YcxZCTVQzf4EmK/nHhdMz/IXf/6Zh/9GP+87H+DY55VMqYqvo/nNdTVT9aVY/0Np9/Y1kPYv1IalWL1k/2osVP&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="0" width="550" height="560" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/>
        <rect x="10" y="285" width="270" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 300px; margin-left: 12px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                サービス終了時（createClouseBucketがtrue）
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="12" y="304" fill="#000000" font-family="Helvetica" font-size="12px">
                    サービス終了時（createClouseBucketがtrue）
                </text>
            </switch>
        </g>
        <rect x="10" y="0" width="270" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 15px; margin-left: 12px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                サービス運用時（createCloseBucketがfalse）
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="12" y="19" fill="#000000" font-family="Helvetica" font-size="12px">
                    サービス運用時（createCloseBucketがfalse）
                </text>
            </switch>
        </g>
        <rect x="10" y="30" width="530" height="240" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 528px; height: 1px; padding-top: 37px; margin-left: 12px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                cloudfront-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="12" y="49" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    cloudfront-stack
                </text>
            </switch>
        </g>
        <path d="M 78 127 L 134 127 L 134 183 L 78 183 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 118.1 164.7 C 118.1 163.38 117.02 162.3 115.7 162.3 C 114.37 162.3 113.3 163.38 113.3 164.7 C 113.3 166.02 114.37 167.1 115.7 167.1 C 117.02 167.1 118.1 166.02 118.1 164.7 Z M 119.7 164.7 C 119.7 166.9 117.9 168.7 115.7 168.7 C 113.49 168.7 111.7 166.9 111.7 164.7 C 111.7 162.49 113.49 160.7 115.7 160.7 C 117.9 160.7 119.7 162.49 119.7 164.7 Z M 98.31 153.15 C 98.31 151.83 97.24 150.75 95.91 150.75 C 94.59 150.75 93.51 151.83 93.51 153.15 C 93.51 154.48 94.59 155.55 95.91 155.55 C 97.24 155.55 98.31 154.48 98.31 153.15 Z M 99.91 153.15 C 99.91 155.36 98.12 157.15 95.91 157.15 C 93.71 157.15 91.91 155.36 91.91 153.15 C 91.91 150.95 93.71 149.15 95.91 149.15 C 98.12 149.15 99.91 150.95 99.91 153.15 Z M 105.78 140.61 C 105.78 141.93 106.86 143.01 108.18 143.01 C 109.51 143.01 110.58 141.93 110.58 140.61 C 110.58 139.28 109.51 138.21 108.18 138.21 C 106.86 138.21 105.78 139.28 105.78 140.61 Z M 104.18 140.61 C 104.18 138.4 105.98 136.61 108.18 136.61 C 110.39 136.61 112.18 138.4 112.18 140.61 C 112.18 142.81 110.39 144.61 108.18 144.61 C 105.98 144.61 104.18 142.81 104.18 140.61 Z M 126.8 155 C 126.8 147.58 122.82 140.72 116.41 137.01 C 115.26 137.24 114.15 137.56 112.76 138.06 L 112.22 136.55 C 112.94 136.29 113.59 136.08 114.21 135.9 C 111.64 134.79 108.84 134.2 106 134.2 C 104.65 134.2 103.32 134.34 102.02 134.59 C 102.96 135.14 103.8 135.69 104.61 136.31 L 103.64 137.58 C 102.5 136.72 101.31 135.98 99.76 135.17 C 91.9 137.64 86.24 144.57 85.35 152.7 C 86.99 152.37 88.58 152.19 90.33 152.14 L 90.37 153.74 C 88.53 153.79 86.95 153.98 85.22 154.36 C 85.21 154.58 85.2 154.79 85.2 155 C 85.2 161.93 88.63 168.31 94.26 172.16 C 93.26 169.17 92.76 166.35 92.76 163.6 C 92.76 162.03 93.03 160.74 93.32 159.37 C 93.38 159.05 93.45 158.73 93.52 158.4 L 95.09 158.71 C 95.02 159.05 94.95 159.38 94.88 159.7 C 94.6 161.04 94.36 162.19 94.36 163.6 C 94.36 166.72 95.05 169.96 96.44 173.47 C 99.42 175.01 102.63 175.8 106 175.8 C 108.2 175.8 110.36 175.45 112.41 174.77 C 113.22 173.18 113.81 171.68 114.31 169.96 L 115.85 170.4 C 115.48 171.66 115.07 172.8 114.57 173.94 C 115.86 173.36 117.08 172.64 118.23 171.8 C 117.95 171.13 117.65 170.45 117.33 169.8 L 118.76 169.08 C 119.04 169.64 119.29 170.21 119.54 170.78 C 124.16 166.82 126.8 161.13 126.8 155 Z M 128.4 155 C 128.4 161.98 125.23 168.44 119.7 172.72 C 118.33 173.78 116.84 174.67 115.27 175.38 C 114.6 175.68 113.93 175.96 113.23 176.2 C 110.92 176.99 108.49 177.4 106 177.4 C 102.32 177.4 98.67 176.48 95.43 174.75 C 88.13 170.84 83.6 163.28 83.6 155 C 83.6 154.45 83.61 154.03 83.65 153.65 C 84.19 144.3 90.6 136.21 99.6 133.53 C 101.65 132.91 103.8 132.6 106 132.6 C 109.85 132.6 113.63 133.59 116.95 135.46 C 124.01 139.42 128.4 146.91 128.4 155 Z M 103.85 143.37 L 102.8 142.16 C 101.01 143.73 99.61 145.39 97.98 147.9 L 99.32 148.77 C 100.87 146.4 102.18 144.83 103.85 143.37 Z M 101.42 153.74 L 100.9 155.25 C 104.57 156.52 107.78 158.53 110.99 161.59 L 112.09 160.43 C 108.71 157.21 105.32 155.08 101.42 153.74 Z M 112.12 144.33 C 115.12 148.91 116.81 153.94 117.14 159.27 L 115.54 159.37 C 115.23 154.32 113.63 149.55 110.78 145.21 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 190px; margin-left: 106px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="106" y="202" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudFront
                </text>
            </switch>
        </g>
        <path d="M 318 181 L 374 181 L 374 237 L 318 237 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 362.72 211.31 L 363.02 209.15 C 365.86 210.85 365.89 211.55 365.89 211.57 C 365.89 211.57 365.4 211.98 362.72 211.31 Z M 361.16 210.88 C 356.26 209.4 349.45 206.27 346.69 204.97 C 346.69 204.96 346.69 204.95 346.69 204.94 C 346.69 203.88 345.83 203.01 344.77 203.01 C 343.71 203.01 342.85 203.88 342.85 204.94 C 342.85 206 343.71 206.86 344.77 206.86 C 345.23 206.86 345.66 206.68 345.99 206.41 C 349.24 207.95 356 211.03 360.93 212.48 L 358.98 226.25 C 358.98 226.29 358.97 226.32 358.97 226.36 C 358.97 227.57 353.61 229.8 344.84 229.8 C 335.98 229.8 330.56 227.57 330.56 226.36 C 330.56 226.32 330.56 226.29 330.55 226.25 L 326.48 196.49 C 330 198.92 337.59 200.2 344.85 200.2 C 352.09 200.2 359.66 198.92 363.2 196.5 Z M 326.05 193.38 C 326.1 192.33 332.15 188.2 344.85 188.2 C 357.54 188.2 363.59 192.33 363.65 193.38 L 363.65 193.74 C 362.95 196.1 355.11 198.6 344.85 198.6 C 334.56 198.6 326.72 196.09 326.05 193.73 Z M 365.25 193.4 C 365.25 190.63 357.3 186.6 344.85 186.6 C 332.39 186.6 324.45 190.63 324.45 193.4 L 324.52 194 L 328.96 226.42 C 329.07 230.05 338.74 231.4 344.84 231.4 C 352.42 231.4 360.47 229.66 360.57 226.42 L 362.49 212.91 C 363.56 213.16 364.43 213.29 365.14 213.29 C 366.09 213.29 366.73 213.06 367.11 212.6 C 367.43 212.22 367.55 211.76 367.46 211.27 C 367.26 210.16 365.94 208.97 363.26 207.44 L 365.16 194.03 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 244px; margin-left: 346px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                WebBucket
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="346" y="256" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    WebBucket
                </text>
            </switch>
        </g>
        <rect x="286" y="50" width="224" height="112" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 222px; height: 1px; padding-top: 57px; margin-left: 287px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                ecs-app-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="398" y="69" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ecs-app-stack
                </text>
            </switch>
        </g>
        <path d="M 374 106 L 418.63 106" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 423.88 106 L 416.88 109.5 L 418.63 106 L 416.88 102.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <image x="317.5" y="77.5" width="56" height="56" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAIdQ/4xQ/4tQ/41O/4xP/4xP/4xP/4xP/4tO/4tQ/49Q/4xP/4xP/4pQ/4xP/4pQ/1FvZWIAAAARdFJOUwAgUHCPv8+vn4BAEP/vYN8wdqqV0AAAAAlwSFlzAAAXEQAAFxEByibzPwAAA8VJREFUWEftmNeSpiAQhc2JQX3/p91zmkZJpqnam639bgYJh06g81f/qZu26wfQj9O81Nr7HdNMw0+MHVcd/MQyWRVI+CpnGm/RMHbt2jTNus7j5vu+qC1OyU6N0R6l6Zyx21u1fRShfkmEHM0kasOrPKzc2o67PubUTq3Vx2uMTOyvlcjuJunTFYb+2edoiPH3fu5MVf8mFG7TG/NFqiuGPKe7FTOUOkLabrbEdkxYKXbhg+kxeIbKV2aK1XGUIZ42bScwOUGiZWEJHQcNnorZbDHQaZvgcdlTuFrHSbK9p0aW+zDsmJYHo461KmYzn4VeG2XwlRbTlXlJ4+MSfaUl8U9K2+BmGLWtvNMSf7SpoFbSunupZTLDYNakTc9LLeYyMozRSo/DW63UMDidROu9Fg0LFhvUVqPtA6wapxTWk44f7NGucDGuLYIZZXT8BDV2Fj92C0+P4+psDzp+gtvndBLLMherfT6AQqfNuc2vLLqlzcThHGbq7oLkuF8P3dzwgCctVqcPGG6brCJCHrUQsFmbKJA89NXSH0BLW30/5pGtEFBvDNKYHHXyfEcfIEj+4sG+hc10aY6OB0DL3/sIXVmrXVN4ket4AC6xR6135/FvahVi/03LFyhiH72YzMAXNFb9WBvekJ3r5WD8zYFb2ecxKDWBAT44jx+P2kG0ORb4PYNSEyKtRTsTrWjzoNjDc054ptxrGi5FWtZ1IyiRVhDw9LxRy7VQ+7GWa8GOUIvX8hE/LAkT+VUr8gtjR75MXSN+kidTc5PabVnXyLx8bhl+/HbaTaJ4802rTS4XbM1oCYNx32ZCE8zQNeg4zWTAtPL5sedosbuyymeDYzyzrHXBsdPIwMkLLYREybUwLywpTnXSWsK40qhF0+GdaPGUMCuaGc7AH5fF6DjDZWfYdy1EfotucLomhn3W4vdkWCAAhonT0BqWZYEAtSY0paihtaHNtyq0ep3BBehK3/mMGL2mgY53seeA8zUA21P/XLRiR6UJuqdzN5wVfk9GX8wCbzlGatocvTHj0Qy6a3POkKjZ85QdcL/Ca/IW+psE3oE9aPYHuP2YeUjo+ycx/hOJs1qEFfxBjFLX//Sx7sr+F2gpVYi7R64Z/0q5Rf4vt4XX6onE7H6KsPASe/xdgdn8me5nGSnjvEYz3BnprtWM/Hhi22cpBI1+Qq0c18X9DPPwC8ZJ6650m8qZZXb7vDNKUTXEd5pX3DBLs7bdIBZ9VCINLqgStv+qJLSS0xDbz091cM2+zlPPC2bgj2r7byz6l6iqP/QFSyhl7HHRAAAAAElFTkSuQmCC" preserveAspectRatio="none" pointer-events="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <text x="345.5" y="151.5">
                ALB
            </text>
        </g>
        <path d="M 425 78 L 481 78 L 481 134 L 425 134 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 471.46 112.09 L 465.17 108.32 L 465.17 99.34 C 465.17 99.06 465.02 98.8 464.78 98.66 L 455.75 93.39 L 455.75 85.8 L 471.46 95.08 Z M 472.64 93.95 L 455.36 83.75 C 455.12 83.6 454.82 83.6 454.57 83.74 C 454.33 83.88 454.18 84.14 454.18 84.42 L 454.18 93.85 C 454.18 94.12 454.33 94.38 454.57 94.52 L 463.6 99.79 L 463.6 108.77 C 463.6 109.04 463.75 109.3 463.98 109.44 L 471.84 114.15 C 471.96 114.23 472.1 114.26 472.24 114.26 C 472.37 114.26 472.51 114.23 472.63 114.16 C 472.87 114.02 473.03 113.76 473.03 113.48 L 473.03 94.63 C 473.03 94.35 472.88 94.1 472.64 93.95 Z M 452.96 126.72 L 434.54 116.93 L 434.54 95.08 L 450.25 85.8 L 450.25 93.41 L 441.98 98.68 C 441.75 98.82 441.61 99.07 441.61 99.34 L 441.61 112.69 C 441.61 112.99 441.78 113.25 442.04 113.39 L 452.6 118.89 C 452.83 119.01 453.1 119.01 453.32 118.89 L 463.57 113.59 L 469.88 117.38 Z M 471.86 116.73 L 464.01 112.02 C 463.77 111.88 463.48 111.87 463.24 112 L 452.96 117.31 L 443.18 112.22 L 443.18 99.77 L 451.46 94.51 C 451.68 94.36 451.82 94.11 451.82 93.85 L 451.82 84.42 C 451.82 84.14 451.67 83.88 451.43 83.74 C 451.18 83.6 450.88 83.6 450.64 83.75 L 433.36 93.95 C 433.12 94.1 432.97 94.35 432.97 94.63 L 432.97 117.41 C 432.97 117.7 433.13 117.96 433.39 118.1 L 452.59 128.31 C 452.71 128.37 452.84 128.4 452.96 128.4 C 453.09 128.4 453.22 128.37 453.34 128.3 L 471.84 118.09 C 472.08 117.96 472.24 117.7 472.24 117.42 C 472.25 117.14 472.1 116.88 471.86 116.73 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 141px; margin-left: 453px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="453" y="153" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS
                </text>
            </switch>
        </g>
        <path d="M 134 155 L 230 155 L 230 106 L 311.63 106" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 316.88 106 L 309.88 109.5 L 311.63 106 L 309.88 102.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 106px; margin-left: 268px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                オリジン
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="268" y="110" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    オリジン
                </text>
            </switch>
        </g>
        <path d="M 134 155 L 230 155 L 230 214 L 311.63 214" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 316.88 214 L 309.88 217.5 L 311.63 214 L 309.88 210.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="10" y="315" width="530" height="240" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 528px; height: 1px; padding-top: 322px; margin-left: 12px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                cloudfront-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="12" y="334" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    cloudfront-stack
                </text>
            </switch>
        </g>
        <path d="M 134 440 L 230 440 L 230 391 L 311.63 391" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 316.88 391 L 309.88 394.5 L 311.63 391 L 309.88 387.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 392px; margin-left: 273px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                オリジン
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="273" y="396" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    オリジン
                </text>
            </switch>
        </g>
        <path d="M 78 412 L 134 412 L 134 468 L 78 468 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 118.1 449.7 C 118.1 448.38 117.02 447.3 115.7 447.3 C 114.37 447.3 113.3 448.38 113.3 449.7 C 113.3 451.02 114.37 452.1 115.7 452.1 C 117.02 452.1 118.1 451.02 118.1 449.7 Z M 119.7 449.7 C 119.7 451.9 117.9 453.7 115.7 453.7 C 113.49 453.7 111.7 451.9 111.7 449.7 C 111.7 447.49 113.49 445.7 115.7 445.7 C 117.9 445.7 119.7 447.49 119.7 449.7 Z M 98.31 438.15 C 98.31 436.83 97.24 435.75 95.91 435.75 C 94.59 435.75 93.51 436.83 93.51 438.15 C 93.51 439.48 94.59 440.55 95.91 440.55 C 97.24 440.55 98.31 439.48 98.31 438.15 Z M 99.91 438.15 C 99.91 440.36 98.12 442.15 95.91 442.15 C 93.71 442.15 91.91 440.36 91.91 438.15 C 91.91 435.95 93.71 434.15 95.91 434.15 C 98.12 434.15 99.91 435.95 99.91 438.15 Z M 105.78 425.61 C 105.78 426.93 106.86 428.01 108.18 428.01 C 109.51 428.01 110.58 426.93 110.58 425.61 C 110.58 424.28 109.51 423.21 108.18 423.21 C 106.86 423.21 105.78 424.28 105.78 425.61 Z M 104.18 425.61 C 104.18 423.4 105.98 421.61 108.18 421.61 C 110.39 421.61 112.18 423.4 112.18 425.61 C 112.18 427.81 110.39 429.61 108.18 429.61 C 105.98 429.61 104.18 427.81 104.18 425.61 Z M 126.8 440 C 126.8 432.58 122.82 425.72 116.41 422.01 C 115.26 422.24 114.15 422.56 112.76 423.06 L 112.22 421.55 C 112.94 421.29 113.59 421.08 114.21 420.9 C 111.64 419.79 108.84 419.2 106 419.2 C 104.65 419.2 103.32 419.34 102.02 419.59 C 102.96 420.14 103.8 420.69 104.61 421.31 L 103.64 422.58 C 102.5 421.72 101.31 420.98 99.76 420.17 C 91.9 422.64 86.24 429.57 85.35 437.7 C 86.99 437.37 88.58 437.19 90.33 437.14 L 90.37 438.74 C 88.53 438.79 86.95 438.98 85.22 439.36 C 85.21 439.58 85.2 439.79 85.2 440 C 85.2 446.93 88.63 453.31 94.26 457.16 C 93.26 454.17 92.76 451.35 92.76 448.6 C 92.76 447.03 93.03 445.74 93.32 444.37 C 93.38 444.05 93.45 443.73 93.52 443.4 L 95.09 443.71 C 95.02 444.05 94.95 444.38 94.88 444.7 C 94.6 446.04 94.36 447.19 94.36 448.6 C 94.36 451.72 95.05 454.96 96.44 458.47 C 99.42 460.01 102.63 460.8 106 460.8 C 108.2 460.8 110.36 460.45 112.41 459.77 C 113.22 458.18 113.81 456.68 114.31 454.96 L 115.85 455.4 C 115.48 456.66 115.07 457.8 114.57 458.94 C 115.86 458.36 117.08 457.64 118.23 456.8 C 117.95 456.13 117.65 455.45 117.33 454.8 L 118.76 454.08 C 119.04 454.64 119.29 455.21 119.54 455.78 C 124.16 451.82 126.8 446.13 126.8 440 Z M 128.4 440 C 128.4 446.98 125.23 453.44 119.7 457.72 C 118.33 458.78 116.84 459.67 115.27 460.38 C 114.6 460.68 113.93 460.96 113.23 461.2 C 110.92 461.99 108.49 462.4 106 462.4 C 102.32 462.4 98.67 461.48 95.43 459.75 C 88.13 455.84 83.6 448.28 83.6 440 C 83.6 439.45 83.61 439.03 83.65 438.65 C 84.19 429.3 90.6 421.21 99.6 418.53 C 101.65 417.91 103.8 417.6 106 417.6 C 109.85 417.6 113.63 418.59 116.95 420.46 C 124.01 424.42 128.4 431.91 128.4 440 Z M 103.85 428.37 L 102.8 427.16 C 101.01 428.73 99.61 430.39 97.98 432.9 L 99.32 433.77 C 100.87 431.4 102.18 429.83 103.85 428.37 Z M 101.42 438.74 L 100.9 440.25 C 104.57 441.52 107.78 443.53 110.99 446.59 L 112.09 445.43 C 108.71 442.21 105.32 440.08 101.42 438.74 Z M 112.12 429.33 C 115.12 433.91 116.81 438.94 117.14 444.27 L 115.54 444.37 C 115.23 439.32 113.63 434.55 110.78 430.21 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 475px; margin-left: 106px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="106" y="487" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudFront
                </text>
            </switch>
        </g>
        <path d="M 318 363 L 374 363 L 374 419 L 318 419 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 362.72 393.31 L 363.02 391.15 C 365.86 392.85 365.89 393.55 365.89 393.57 C 365.89 393.57 365.4 393.98 362.72 393.31 Z M 361.16 392.88 C 356.26 391.4 349.45 388.27 346.69 386.97 C 346.69 386.96 346.69 386.95 346.69 386.94 C 346.69 385.88 345.83 385.01 344.77 385.01 C 343.71 385.01 342.85 385.88 342.85 386.94 C 342.85 388 343.71 388.86 344.77 388.86 C 345.23 388.86 345.66 388.68 345.99 388.41 C 349.24 389.95 356 393.03 360.93 394.48 L 358.98 408.25 C 358.98 408.29 358.97 408.32 358.97 408.36 C 358.97 409.57 353.61 411.8 344.84 411.8 C 335.98 411.8 330.56 409.57 330.56 408.36 C 330.56 408.32 330.56 408.29 330.55 408.25 L 326.48 378.49 C 330 380.92 337.59 382.2 344.85 382.2 C 352.09 382.2 359.66 380.92 363.2 378.5 Z M 326.05 375.38 C 326.1 374.33 332.15 370.2 344.85 370.2 C 357.54 370.2 363.59 374.33 363.65 375.38 L 363.65 375.74 C 362.95 378.1 355.11 380.6 344.85 380.6 C 334.56 380.6 326.72 378.09 326.05 375.73 Z M 365.25 375.4 C 365.25 372.63 357.3 368.6 344.85 368.6 C 332.39 368.6 324.45 372.63 324.45 375.4 L 324.52 376 L 328.96 408.42 C 329.07 412.05 338.74 413.4 344.84 413.4 C 352.42 413.4 360.47 411.66 360.57 408.42 L 362.49 394.91 C 363.56 395.16 364.43 395.29 365.14 395.29 C 366.09 395.29 366.73 395.06 367.11 394.6 C 367.43 394.22 367.55 393.76 367.46 393.27 C 367.26 392.16 365.94 390.97 363.26 389.44 L 365.16 376.03 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 426px; margin-left: 346px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ClosedBucket
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="346" y="438" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ClosedBuc...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
