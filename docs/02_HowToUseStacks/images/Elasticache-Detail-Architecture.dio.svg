<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="341px" height="118px" viewBox="-0.5 -0.5 341 118" content="&lt;mxfile host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36&quot; version=&quot;24.8.4&quot; pages=&quot;2&quot;&gt;&#10;  &lt;diagram name=&quot;241113&quot; id=&quot;4Rnx736bhhO2fOMOVGZK&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1539&quot; dy=&quot;818&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;W69iMLD8WM5jiUqYfehz-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;W69iMLD8WM5jiUqYfehz-1&quot; parent=&quot;W69iMLD8WM5jiUqYfehz-0&quot; /&gt;&#10;        &lt;mxCell id=&quot;W69iMLD8WM5jiUqYfehz-3&quot; value=&quot;elasticache-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; vertex=&quot;1&quot; parent=&quot;W69iMLD8WM5jiUqYfehz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;220&quot; y=&quot;260&quot; width=&quot;340&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;W69iMLD8WM5jiUqYfehz-4&quot; value=&quot;Secret Manager&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.secrets_manager;rounded=1;&quot; vertex=&quot;1&quot; parent=&quot;W69iMLD8WM5jiUqYfehz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480&quot; y=&quot;280&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;W69iMLD8WM5jiUqYfehz-5&quot; value=&quot;アタッチ&quot; style=&quot;edgeStyle=none;html=1;&quot; edge=&quot;1&quot; parent=&quot;W69iMLD8WM5jiUqYfehz-1&quot; source=&quot;W69iMLD8WM5jiUqYfehz-6&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;360&quot; y=&quot;300&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;W69iMLD8WM5jiUqYfehz-6&quot; value=&quot;SG&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;rounded=1;fillColor=none;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;W69iMLD8WM5jiUqYfehz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;280&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-O3XQC_fazBzCBeF4-4J-0&quot; value=&quot;ElastiCache&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.elasticache;&quot; vertex=&quot;1&quot; parent=&quot;W69iMLD8WM5jiUqYfehz-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;360&quot; y=&quot;280&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;iPCpmYeHDqjBAoUQL4SW&quot; name=&quot;240821&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1539&quot; dy=&quot;818&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;2&quot; value=&quot;ElastiCache for Redis&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#2E27AD;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_redis;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;360&quot; y=&quot;280&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3&quot; value=&quot;elasticache-redis-stack&quot; style=&quot;whiteSpace=wrap;html=1;rounded=1;fillColor=none;labelPosition=center;verticalLabelPosition=top;align=center;verticalAlign=bottom;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;220&quot; y=&quot;260&quot; width=&quot;340&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4&quot; value=&quot;Secret Manager&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#F54749;gradientDirection=north;fillColor=#C7131F;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.secrets_manager;rounded=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;480&quot; y=&quot;280&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7&quot; value=&quot;アタッチ&quot; style=&quot;edgeStyle=none;html=1;&quot; parent=&quot;1&quot; source=&quot;6&quot; target=&quot;2&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6&quot; value=&quot;SG&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;rounded=1;fillColor=none;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;240&quot; y=&quot;280&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0"><stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/></linearGradient></defs><g><g data-cell-id="W69iMLD8WM5jiUqYfehz-0"><g data-cell-id="W69iMLD8WM5jiUqYfehz-1"><g data-cell-id="W69iMLD8WM5jiUqYfehz-3"><g><rect x="0" y="17" width="340" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 338px; height: 1px; padding-top: 14px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">elasticache-stack</div></div></div></foreignObject><text x="170" y="14" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">elasticache-stack</text></switch></g></g></g><g data-cell-id="W69iMLD8WM5jiUqYfehz-4"><g><path d="M 260 37 L 300 37 L 300 77 L 260 77 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/><path d="M 279.29 58.92 C 279.29 59.31 279.61 59.63 280 59.63 C 280.39 59.63 280.71 59.31 280.71 58.92 C 280.71 58.53 280.39 58.21 280 58.21 C 279.61 58.21 279.29 58.53 279.29 58.92 Z M 278.15 58.92 C 278.15 57.9 278.98 57.07 280 57.07 C 281.02 57.07 281.85 57.9 281.85 58.92 C 281.85 59.74 281.31 60.43 280.57 60.67 L 280.57 62.14 L 279.43 62.14 L 279.43 60.67 C 278.69 60.43 278.15 59.74 278.15 58.92 Z M 285.14 55.86 L 274.86 55.86 L 274.86 63.29 L 285.14 63.29 L 285.14 61.57 L 283.43 61.57 L 283.43 60.43 L 285.14 60.43 L 285.14 58.71 L 283.43 58.71 L 283.43 57.57 L 285.14 57.57 Z M 276.57 54.71 L 283.43 54.71 L 283.43 51.86 C 283.43 50.36 281.8 49 280 49 L 280 49 C 279.13 49 278.24 49.33 277.58 49.9 C 276.94 50.45 276.57 51.16 276.57 51.86 Z M 284.57 51.86 L 284.57 54.71 L 285.71 54.71 C 286.03 54.71 286.29 54.97 286.29 55.29 L 286.29 63.86 C 286.29 64.17 286.03 64.43 285.71 64.43 L 274.29 64.43 C 273.97 64.43 273.71 64.17 273.71 63.86 L 273.71 55.29 C 273.71 54.97 273.97 54.71 274.29 54.71 L 275.43 54.71 L 275.43 51.86 C 275.43 50.83 275.94 49.8 276.83 49.03 C 277.7 48.29 278.86 47.86 280 47.86 L 280 47.86 C 282.44 47.86 284.57 49.73 284.57 51.86 Z M 268.12 65.6 L 269.04 64.93 C 267.48 62.77 266.62 60.24 266.51 57.57 L 268 57.57 L 268 56.43 L 266.51 56.43 C 266.63 53.78 267.49 51.26 269.04 49.11 L 268.12 48.44 C 266.42 50.79 265.49 53.54 265.37 56.43 L 264 56.43 L 264 57.57 L 265.37 57.57 C 265.48 60.48 266.41 63.24 268.12 65.6 Z M 287.91 67.98 C 285.75 69.53 283.23 70.4 280.57 70.51 L 280.57 69 L 279.43 69 L 279.43 70.51 C 276.77 70.4 274.25 69.54 272.09 67.98 L 271.42 68.9 C 273.77 70.6 276.53 71.54 279.43 71.65 L 279.43 73 L 280.57 73 L 280.57 71.65 C 283.47 71.54 286.23 70.6 288.58 68.9 Z M 272.09 46.07 C 274.25 44.51 276.77 43.64 279.43 43.53 L 279.43 45 L 280.57 45 L 280.57 43.53 C 283.23 43.64 285.75 44.51 287.91 46.07 L 288.58 45.14 C 286.23 43.44 283.47 42.5 280.57 42.39 L 280.57 41 L 279.43 41 L 279.43 42.39 C 276.53 42.5 273.77 43.44 271.42 45.14 Z M 294.63 56.43 C 294.51 53.54 293.58 50.79 291.88 48.44 L 290.95 49.11 C 292.51 51.26 293.37 53.78 293.49 56.43 L 292 56.43 L 292 57.57 L 293.49 57.57 C 293.38 60.24 292.52 62.77 290.95 64.93 L 291.88 65.6 C 293.59 63.24 294.52 60.48 294.63 57.57 L 296 57.57 L 296 56.43 Z M 289.36 48.47 L 293.84 43.99 L 293.04 43.18 L 288.56 47.66 Z M 270.64 65.58 L 266.16 70.06 L 266.96 70.87 L 271.44 66.39 Z M 273.52 49.72 L 265.04 41.24 L 264.23 42.04 L 272.72 50.52 Z M 288.11 64.3 L 295.76 71.96 L 294.96 72.76 L 287.3 65.11 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 84px; margin-left: 280px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">Secret Manager</div></div></div></foreignObject><text x="280" y="96" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Secret...</text></switch></g></g></g><g data-cell-id="W69iMLD8WM5jiUqYfehz-5"><g><path d="M 60 57 L 133.63 57" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 138.88 57 L 131.88 60.5 L 133.63 57 L 131.88 53.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 57px; margin-left: 100px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">アタッチ</div></div></div></foreignObject><text x="100" y="60" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">アタッチ</text></switch></g></g></g><g data-cell-id="W69iMLD8WM5jiUqYfehz-6"><g><ellipse cx="40" cy="57" rx="20" ry="20" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 57px; margin-left: 21px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SG</div></div></div></foreignObject><text x="40" y="61" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">SG</text></switch></g></g></g><g data-cell-id="-O3XQC_fazBzCBeF4-4J-0"><g><path d="M 140 37 L 180 37 L 180 77 L 140 77 Z" fill="#c925d1" stroke="none" pointer-events="all"/><path d="M 166.29 69.03 L 166.29 65.69 C 164.84 66.62 162.23 67.06 159.75 67.06 C 157.04 67.06 154.93 66.59 153.71 65.78 L 153.71 69.03 C 153.71 70 155.97 71 159.75 71 C 163.6 71 166.29 69.96 166.29 69.03 Z M 159.75 61.98 C 157.04 61.98 154.93 61.51 153.71 60.7 L 153.71 63.96 C 153.73 64.93 155.98 65.92 159.75 65.92 C 163.59 65.92 166.27 64.89 166.29 63.96 L 166.29 60.61 C 164.84 61.54 162.23 61.98 159.75 61.98 Z M 166.29 58.88 L 166.29 55.02 C 164.84 55.95 162.23 56.39 159.75 56.39 C 157.04 56.39 154.93 55.92 153.71 55.11 L 153.71 58.88 C 153.73 59.85 155.98 60.84 159.75 60.84 C 163.59 60.84 166.27 59.81 166.29 58.88 Z M 153.71 53.28 C 153.71 53.28 153.71 53.29 153.71 53.29 L 153.71 53.29 L 153.71 53.29 C 153.73 54.26 155.98 55.25 159.75 55.25 C 163.94 55.25 166.27 54.1 166.29 53.29 L 166.29 53.29 L 166.29 53.29 C 166.29 53.29 166.29 53.28 166.29 53.28 C 166.29 52.48 163.96 51.31 159.75 51.31 C 155.97 51.31 153.71 52.31 153.71 53.28 Z M 167.43 53.3 L 167.43 58.87 L 167.43 58.87 C 167.43 58.88 167.43 58.88 167.43 58.89 L 167.43 63.95 L 167.43 63.95 C 167.43 63.96 167.43 63.96 167.43 63.97 L 167.43 69.03 C 167.43 71.17 163.45 72.14 159.75 72.14 C 155.39 72.14 152.57 70.92 152.57 69.03 L 152.57 63.97 C 152.57 63.96 152.57 63.96 152.57 63.95 L 152.57 63.95 L 152.57 58.89 C 152.57 58.88 152.57 58.88 152.57 58.87 L 152.57 58.87 L 152.57 53.3 C 152.57 53.29 152.57 53.29 152.57 53.28 C 152.57 51.39 155.39 50.17 159.75 50.17 C 163.45 50.17 167.43 51.14 167.43 53.28 C 167.43 53.29 167.43 53.29 167.43 53.3 Z M 175.43 45.93 C 175.74 45.93 176 45.67 176 45.35 L 176 42.43 C 176 42.11 175.74 41.86 175.43 41.86 L 144.57 41.86 C 144.26 41.86 144 42.11 144 42.43 L 144 45.35 C 144 45.67 144.26 45.93 144.57 45.93 C 145.27 45.93 145.84 46.49 145.84 47.18 C 145.84 47.88 145.27 48.44 144.57 48.44 C 144.26 48.44 144 48.7 144 49.01 L 144 60.72 C 144 61.03 144.26 61.29 144.57 61.29 L 150.29 61.29 L 150.29 60.14 L 147.43 60.14 L 147.43 58.43 L 150.29 58.43 L 150.29 57.29 L 146.86 57.29 C 146.54 57.29 146.29 57.54 146.29 57.86 L 146.29 60.14 L 145.14 60.14 L 145.14 49.51 C 146.2 49.26 146.98 48.31 146.98 47.18 C 146.98 46.06 146.2 45.11 145.14 44.85 L 145.14 43 L 174.86 43 L 174.86 44.85 C 173.8 45.11 173.02 46.06 173.02 47.18 C 173.02 48.31 173.8 49.26 174.86 49.51 L 174.86 60.14 L 173.71 60.14 L 173.71 57.86 C 173.71 57.54 173.46 57.29 173.14 57.29 L 169.71 57.29 L 169.71 58.43 L 172.57 58.43 L 172.57 60.14 L 169.71 60.14 L 169.71 61.29 L 175.43 61.29 C 175.74 61.29 176 61.03 176 60.72 L 176 49.01 C 176 48.7 175.74 48.44 175.43 48.44 C 174.73 48.44 174.16 47.88 174.16 47.18 C 174.16 46.49 174.73 45.93 175.43 45.93 Z M 153.14 49.86 L 153.14 45.29 C 153.14 44.97 152.89 44.71 152.57 44.71 L 149.14 44.71 C 148.83 44.71 148.57 44.97 148.57 45.29 L 148.57 55 C 148.57 55.32 148.83 55.57 149.14 55.57 L 150.86 55.57 L 150.86 54.43 L 149.71 54.43 L 149.71 45.86 L 152 45.86 L 152 49.86 Z M 170.29 54.43 L 169.71 54.43 L 169.71 55.57 L 170.86 55.57 C 171.17 55.57 171.43 55.32 171.43 55 L 171.43 45.29 C 171.43 44.97 171.17 44.71 170.86 44.71 L 167.43 44.71 C 167.11 44.71 166.86 44.97 166.86 45.29 L 166.86 49.86 L 168 49.86 L 168 45.86 L 170.29 45.86 Z M 165.71 49.29 L 165.71 45.29 C 165.71 44.97 165.46 44.71 165.14 44.71 L 161.14 44.71 C 160.83 44.71 160.57 44.97 160.57 45.29 L 160.57 48.71 L 161.71 48.71 L 161.71 45.86 L 164.57 45.86 L 164.57 49.29 Z M 158.29 48.71 L 158.29 45.86 L 155.43 45.86 L 155.43 49.29 L 154.29 49.29 L 154.29 45.29 C 154.29 44.97 154.54 44.71 154.86 44.71 L 158.86 44.71 C 159.17 44.71 159.43 44.97 159.43 45.29 L 159.43 48.71 Z" fill="#ffffff" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 84px; margin-left: 160px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">ElastiCache</div></div></div></foreignObject><text x="160" y="96" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Elasti...</text></switch></g></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>