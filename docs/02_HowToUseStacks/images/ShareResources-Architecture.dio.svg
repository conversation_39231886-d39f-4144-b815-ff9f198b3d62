<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="522px" height="318px" viewBox="-0.5 -0.5 522 318" content="&lt;mxfile&gt;&lt;diagram id=&quot;2Y8jC7U9ZkPlZ7FMHuWo&quot; name=&quot;240822&quot;&gt;7XtXl+M4ku6vqXPufZg59OaRVqREURQ99bKH3nsr/vpLpOmuqsy+M7Nb3b3TU5kSEwgE4SLiQwCB/IJy9XYa/C67tlFcfUGgaPuC8l8QBCYg8vgDKM9XCkW8EdIhj96YfiUY+R6/EaE36pxH8fgN49S21ZR33xLDtmnicPqG5g9Du37LlrTVt612fhp/IBihX32kOnk0ZW+jQMhf6VKcp9l7yzBBv5bU/jvz20jGzI/a9SsSKnxBuaFtp9dUvXFxBSbvfV5e3xN/o/SXjg1xM/0zL2BvHVv8an4bXFmPfyvj59+OyRunYT6m77Wn0/N9+GuWT7HR+SHIr4eIv6BsNtXVkYOPZJJXFddW7XDkm7Y5mNihnZsojt4Y0sofgQSgI/3LBIBM5QdxpbVjPuVtc9DCYxTxUQ27xMOUH/OvfMcwtaBtv8rTT9mZt4Kgnaa2PgqOMU1+3gCmlwY/TtfbDIIa4u0r0tv0neK2jqfhebC8lWLQmyjfdBl5z6+/agb8Lu7sa614Z/TftDH9pe5fBXYk3mT2G/KjPsgvbNMmn9qf8vtn5fePxUf9XuJDsY/iy/zpGO9fUnw/QlzIt/IiPhEX9HuJC0c/iGvpwr+kqH5/S/tEcijxe0nuveKvJHe5GgfhEj8/SG0s4ynM3gbetXkzvbSMs8fnqId7/eIHKwcof0fwT4if0ciPRPgj2/EH/qyF74mf0ciPRPgjG8i99/pb4mc0Ev/Y4+/fhj95G/7u7eODsu08VYdacb94ZmCOk0PZ3u3gC4IevyIQI5sOfpTH35SJOEYeTsuvZXw+HBW9KnnTDkCVvrGr4x2OhFFYBCY0DW0Zf1WSvPwcJZE/Zi9WB/1/bOgX4/jean7DuL629GOEb34sjLzn3zQONOmP3et0JPkG+gGsvQOF9ZYCH/rv/jpifx/isZ2HMJZD0B/2yL6mvuU6nLf/qv3mcELroyf/NcbDkocfseUbPPkaaH4TW34ABsDEtyBAfQQB7BMMwH4EBMAfIMA48Bxi5/Cw9y8IUR2NscFwpFKQ+j9Km/7fn9jwb4QNBMSgKPmvYQNCksf27D8GG0b0fwcOfO8M/JE4QH7AAVs73oKEJnox6I82/zbfef1yLvCPdeBl5lg/LNOXqX5XqihO/PkFZX5DSb4X9EuDzDsVeqeAqvzJ/4Iyr1lE7Jr0sKTcZm/6Cl1OacscP6phZYKVHilhPB6swjFXQNdKefBAgnFVQ4dkZhixkLgDgt7cLZhlGG4r1oXy7hYg7pawWTool+n70RgWvTy7l+f08tw+UKYbeMIvz037J58Q6nkDBtq8R+L5Zq3MXeZZxjqxayp/QdhkOGQnagdqs1Rxai8XwMq5rOy4x8jYUAdjEu7S5bxcD0Yw8giqhLute/rxDuwhxGW4pOuITlw/JUBlEHFywpoVyvtMD00SP86CF5yzO3PwH4VleDy22g1SB6b3UrvDE6Vvc7LRRR6XKEmxjuj5T0W81bmrbFI+5BoaeY15k3GzUSI3WCYRPbMnyVoq2U4nicQbAo4QjfSPFginr2I0II5GspOeFL3jHqrKLgSrBSh94SJ1PbI73S2kYrbIBhGgdPXoGIpLxyCXwdQPk2CbYLNAHXXqPYkEy2J3Jvy+JHlm9itssaNrMFT9k+979ek/sr2rEXhEQgjtysf10udCWYqVmRtB0oWpU1V9SJboIf+ze4FnxppMFEa8U3atlVvpQcSjq5pqcyVCnxiSF9HD1ln9VCun8okWMCw2IvSk3aNDV+4snW3DkHJhSfPz7bS3VLO2T73Q6+lxkckn1Ef3pIeR+pGP40M9KjrhTo2xhMs/5R0XoYEdLmo0Iha7dXidDpnMWy1hStXqNw55U70J2/0WjL7I8bjZVzKNyNwXn83jnAa2ebPF+yQiWpddxUBfKxjrBoM6qWo8Uk+ODyTdEZCMEg8TPGG6en0wAi9KuusyBFR7d+10eoymiNc9x8MnUxqye17oVXtCqx0zzniUJOn9OjeMSHObyAXaQ17KQsSSQ5/ZPHUQknVcNbifuxDuMULA8WGaOc8twjW7lz7GS2LoimzlnW+KDJQthyJkMqlE5KrqNA3N/Ry3xTjxkwkMBJIN0qxuvSoj054aDCmh4to8O28sPJfIx6sQM+ZKF9Hd9QlVKVTWj8ozIsZhIxE8zo2+e31khwGJ0jlAA1fI14Wl6e1h3dgD+MQ27Og+5Be3SG7OAavigStsB4VKNg8xvdedoF6eaisAgTeGerXgtHcIzDxBq6zbqRmebIoV3KzX8vNzK0K5Qa/lNnmy6+iQSGRb2PEKtmbYCgW27c4svgcxQ0yolQ/u5Cn5CDQg75/YBe2bdXM7FhPrS+wqdnWqtVbcnIJ3rqFIxA7m0b4pM5ZzJTOHEYhTbmWqTaih60MRO2PQdtqgjWHZkDUvaYcIN2AqHY0ad/WhhOuDUliVVbzn4K6QQVIIU0cUq/CtscYZI54voS3fGEQzmjhpfb2RDw/ikjaig/Kb+JhNGyVr8yTIUx/j655W29XnUdhR7jva9AxeZc+sNfl+i6rhdPUe/K1sWbFBLgL+0IORWftdqgXlGhfN9jg69gwLR1UbsWAWf91RLlzn5y5FPJXzRUWX0XbLSxu2FUW4dJNqyhk0z0s+Hf6qiKgPtHC0QWkNWp93+Ho/li0xCC6+ndQWy2CBX2/hFKhwQTDZMCdF5JrNocSdG/fMJq7jmR5M2A6LmKD2m3zfO682uodsDG3qyqKQOE+kyIuLR6IPqSf6VUB8PtKkrt4DptkzMzsGsM1lqVXjyfF71sMmCcCnSVxmrYKFrMQNiBPRBPa0cqI0mK1XXZdt2Mm0bmsMnImSVGjIZe9WhR3bV2juVl0y7DYu1fb+KOIZhreJZqpYqRKwzJtgSUi2zsW8cJYZgEITk7nwrXYNTG92tj+3/HIL6S1fnlVVaMj56ec0STHmsFF1gxyTH3V6hywacgnxOSaCeSb2k0aeaR/A6TUptN3enSDwWQXhX9ZMoRLN0pjvNce9r8e/rvTfLPI/wE1CkT/NTQK++Wdu0s/90L/NfojGcF5E/rX9EM6gEIv/x+yHli78xVI/mOUnxvublkr9aYb6HhT9ylC5zJ/Y9pMzDaM6oOrg5SqgDj+N+Q805p/T+b8KG0UUw6h/ERtZDkbx/5yzordI5A8K/uB/HkB+jNqNzfiXjNr9DidznwbEPxPVj7nP8EFUZtvl4c8jud86klOpe3oLvfh2t+M5tJKb58RxeKQ96+gFa1nxnNogFb4804PjLfU19T1N1NaZa3/kARzbC18fwD3xiyCzjLmxYbtr9MFDGt5jVUREeU77KHS7fH/ITgSrL+dvbBprWqpM9ZEUd4uM6NMgNQqcLzXOogpsCBgeK5O6h0fvRHc5HuyhQiI4gkSptVOAwh37wK0RL0cV5iD2++NJ7bibTfejUlG3Mc9n0w1iUGM7m/TFPOks3xblmOTgqC2Es9hHOpa85JkpEkt+b/cZl67VLesN/XG5QGl+Mpqz6GsNJ7FNT0dS4qAQUURpx54ZZqzk3CZOOgO6xlp3G+OKM7cGIthgPuLVLFWChGcw/ed1z2u4Q07hBRI5giMfC+15CLkTJ9FR1VXMB/gmw/yAkqnDjPV+dniedAUaqW+qQpRhvKA+ooKGnAE/SyJyK0VYDsBmuhX63OhF6GkRJ1uGuZDKJe9WqTgtg7klOcOnWiU5LaM51Lu7mT41na06Yp3CPwXny/ksjcIIWBMh72IVYq+Gc6L3LTRmi5Lr2U2kIIVgpNPuNLU4EWIn6R2pie2wVlZy5w0cJhGum1qGfU5cp95YtTAi3bU3Vox7o+wukgZrqrdImXeiKEQldcL3OsZy8jFBAijFEqG8ZKnBmfbswrigY9Ttwg9JymmaSd0zrCTbFegje9YtXBjKc5qmAC3A54cgI/0dMiJ/3BqGfrynx73e8/rpxP87eZ0/by/8Q6/zTa3/6TDlD7Bs8s8z7Pc6vr6VEIdDPF1fbnAMfz3PR4IARtOvng+Hud3q/gueD/Li+UQQZN4eTRxnUKbSrP16XPz+YZ8qWNuDKzT3TAFcB9WbVT9YLvuiNQ80QZ+0ttREYoDgHgjD+d3C1cAjYy6msEcvrpjx4gF9W/N/88PcDu9DXDYZ1HsOhewRHiuVuAmvPlPY1tROHUUWaVg6a58ynnz0atmPvSDfC12NbA/dwczuaJQzLXbi+HEIOjuZw2Zjc9/zHhnMb3NQ429fbDQjzbNLxEoShEkKIkGGYIBMjETIoTZpHQSTTjWYAOAweGjuyiAKibuTmS+2BS887eZOBdy9abzJ15UAi78NgW5wZFkjoq3xouANLqNuEo5CKlGP3kOBIXQASnCWiyyrT2vBQuDF2o6r3l4sBcQz011PokUqCcI6K9fo2WWL65B9EzzoXIkdp3kkPhRrDNRmdw54Rq6HBi6i+Cci0A/gdFoHhBse94Rb9WNhZJ0AzPICAljXwqFAYQAazYETOuTRY9Sn2zZJzQJfhwnaCo1YQeCEXRu+eFq4jPgPCnkCyrNh92fitsToR0fWN8vzHXiC/PCEmi6RbYWLKqqyxvPTfVbG2lhuY089FBbCWD2xEjhbNIYgiTgVZDqKxrYEqkw9SRAPvHl5xQxIOdDXHQO1U9aZXLzEohANtpvZix2SncSFPS1osC7FE8T8JtPfHzsMP4I4kTrKO6uzNu0cXdTVojn8o7K2m0SoSk7dFtLsU4ItrCFURuF4N4H2NT+lwBrEKwgypU8ysmr0MQiOu2ePudVso5S0WXjSKlMAtMLyXmooOt7aQlivCSXqjeZ5y8D6V2JAufEunsTlYYyJlhnqGa6bPDQFY3CHMTufHSGhH1nLOwhk6pT96sNzh5axSrKubmlnd7dOzcxuMBaV9+upue8BthEUU95u+C27IvvpHJZ6GLKnoW2GkE5Hsq1ZfYPOtiQmYVT7OSydMQbBNIquTsMMrQ8lUG0Oy9UoQdLCaoxih4BDPWOIB2NcyAJl5yi/PpSqtnLLByZ/T1NHoij5dqTx2ZqGenOf10eBGEGFAM3wrbRj5n63AID0QJ/EXdrcs6Wxl3hiQdgt4mQIIjDMU2aQbfU2m+gwLNWnGy2OaHvrQD5hHU66pARNkrCLjoDTpK3ABaSrWfK2dUmKMTBTGr8tmmTXgqOUdKLBzZnL742NAPGx2bncYE3grw1oiQ3Gro7aa+kp9FUQTMKugqtAF/BYQi6Meph/6q+XlJ6ITrArMomhRw1dgqUP+UuFcarpAXykA6CEXensEntzOn33VHuDYI6+Zrw6oven1HNaSwRY72LsxBXK0De3Sbwm2pKNTX8TpSeW2c9OykWmfeQ38fIcZKKY9dCqxTiT6QTJYHyRiJUmNlxYtvtgnxT5Djab3YmYYzLobxOeurBy4/mea0IiF856dxt7BLsVAEWe/H3AmlWcGrVCZLIgmseEc6LjoSKcbhlcS0U3TTJMak05lDw9Gjupmy9qlzcu5A1X2CJTDFZxYA4tfVfkiNm8nDJUY5KNQ85Fj2VGzcixuJUqbfntlqDo9WoApbVYZK90etc1xoN56t5r6ll/SF2XybdtQfAmpZ2LriAXdN4GkheoNJ4PzRNPPh2c9Nh+SDWzmhzR7s0t50p/nNxBt/ze4eNwTOZp4G1ftUfVQ+ucSsAGe6j8VqjLaZFkYD0sv1W39iJTFVzNW5/WPDUqUGVVsaHGgjTZ+HUcz5tbFuHBreJxrMxFMfRgQVQk3C9SAY2URz9JC2ygi4BfUR8MDC3xKYZglyoT6HBIwU0Yrb/w/PVuONVukBgyKTzA1DMwhQtKa3MENutPGcNkorkn0GXOb6hIzWSVzRgAfN6js/UmUwqwFG+PtayDA8SzDuRrIFWa+pTSBk5Lb7HPuzWsPUAfEaT2G0fEPRzroyHY6+1uepBYZZgGxVsl9xKcb4cjw++XiHyiEz7GFNALbmc5kSxIlSIpIp14UjeCNajHQVcVijRWYPEykLjeO3R0X8qLDVT9Ns3CTKT5cjYJY+jAenc5XINXp8FEpz1E1bfvy92fpJDwPFeuSMMn3uWT8O8P8D1h/M9zPvGPx20/b8X/3Ff+JfeV/9Nb8T8iBvL90fofaOrkx1uvx1CH+G/vszf+bZxegsM/OBjyB8U8/kfB++9u2XwiFvyz/+BE/xsRD7Ah+eW/e1/KvvofaVT4fw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0">
            <stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="380" y="197" width="120" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 194px; margin-left: 381px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                kms-key-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="440" y="194" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    kms-key-construct
                </text>
            </switch>
        </g>
        <rect x="20" y="197" width="180" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 194px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                cognito-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="110" y="194" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    cognito-construct
                </text>
            </switch>
        </g>
        <rect x="400" y="57" width="100" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 54px; margin-left: 401px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                chatbot-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="450" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    chatbot-construct
                </text>
            </switch>
        </g>
        <rect x="20" y="57" width="360" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 54px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                vpc-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    vpc-construct
                </text>
            </switch>
        </g>
        <path d="M 140 77 L 180 77 L 180 117 L 140 117 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 167.38 104.56 L 171.69 104.56 L 171.69 103.33 L 167.38 103.33 Z M 161.85 104.56 L 166.15 104.56 L 166.15 103.33 L 161.85 103.33 Z M 156.31 104.56 L 160.61 104.56 L 160.61 103.33 L 156.31 103.33 Z M 163.08 99.63 C 163.08 98.96 163.63 98.4 164.31 98.4 C 164.99 98.4 165.54 98.96 165.54 99.63 C 165.54 100.31 164.99 100.86 164.31 100.86 C 163.63 100.86 163.08 100.31 163.08 99.63 Z M 166.77 99.63 C 166.77 98.28 165.67 97.17 164.31 97.17 C 162.95 97.17 161.85 98.28 161.85 99.63 C 161.85 100.99 162.95 102.1 164.31 102.1 C 165.67 102.1 166.77 100.99 166.77 99.63 Z M 158.15 98.4 C 158.83 98.4 159.38 98.96 159.38 99.63 C 159.38 100.31 158.83 100.86 158.15 100.86 C 157.47 100.86 156.92 100.31 156.92 99.63 C 156.92 98.96 157.47 98.4 158.15 98.4 Z M 158.15 102.1 C 159.51 102.1 160.61 100.99 160.61 99.63 C 160.61 98.28 159.51 97.17 158.15 97.17 C 156.79 97.17 155.69 98.28 155.69 99.63 C 155.69 100.99 156.79 102.1 158.15 102.1 Z M 176 92.86 L 176 107.02 C 176 107.36 175.72 107.63 175.38 107.63 L 156.31 107.63 L 156.31 106.4 L 174.77 106.4 L 174.77 93.48 L 161.23 93.48 L 161.23 92.25 L 175.38 92.25 C 175.72 92.25 176 92.52 176 92.86 Z M 154.27 95.97 C 154.02 96.05 153.84 96.29 153.84 96.56 L 153.84 109.51 L 152.37 110.77 L 150.77 108.94 L 150.77 107.33 C 150.77 107.16 150.7 107.01 150.59 106.89 L 149.18 105.48 L 150.59 104.07 C 150.7 103.95 150.77 103.8 150.77 103.63 L 150.77 102.4 C 150.77 102.24 150.7 102.08 150.59 101.97 L 149.18 100.56 L 150.59 99.15 C 150.7 99.03 150.77 98.87 150.77 98.71 L 150.77 96.56 C 150.77 96.29 150.59 96.06 150.34 95.98 C 147.17 94.97 145.3 91.72 145.99 88.4 C 146.48 86.02 148.32 84.1 150.67 83.53 C 152.66 83.04 154.72 83.46 156.29 84.7 C 157.87 85.94 158.77 87.79 158.77 89.79 C 158.77 92.59 156.92 95.13 154.27 95.97 Z M 160 89.79 C 160 87.41 158.92 85.2 157.05 83.73 C 155.18 82.26 152.74 81.75 150.37 82.33 C 147.57 83.02 145.37 85.3 144.78 88.16 L 144.78 88.16 C 144 91.94 146.03 95.66 149.54 97 L 149.54 98.46 L 147.87 100.12 C 147.63 100.36 147.63 100.75 147.87 100.99 L 149.54 102.66 L 149.54 103.38 L 147.87 105.05 C 147.63 105.29 147.63 105.68 147.87 105.92 L 149.54 107.58 L 149.54 109.17 C 149.54 109.32 149.59 109.47 149.69 109.58 L 151.84 112.04 C 151.96 112.18 152.13 112.25 152.31 112.25 C 152.45 112.25 152.59 112.2 152.71 112.1 L 154.86 110.26 C 155 110.14 155.08 109.97 155.08 109.79 L 155.08 96.99 C 158 95.87 160 92.97 160 89.79 Z M 152.31 91.25 C 151.46 91.25 150.77 90.56 150.77 89.71 C 150.77 88.86 151.46 88.17 152.31 88.17 C 153.15 88.17 153.84 88.86 153.84 89.71 C 153.84 90.56 153.15 91.25 152.31 91.25 Z M 152.31 86.94 C 150.78 86.94 149.54 88.18 149.54 89.71 C 149.54 91.23 150.78 92.48 152.31 92.48 C 153.83 92.48 155.08 91.23 155.08 89.71 C 155.08 88.18 153.83 86.94 152.31 86.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 160px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                KMS Key
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="160" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    KMS Key
                </text>
            </switch>
        </g>
        <path d="M 220 77 L 260 77 L 260 117 L 220 117 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 251.94 98.65 L 252.16 97.11 C 254.18 98.32 254.21 98.82 254.21 98.83 C 254.2 98.84 253.86 99.13 251.94 98.65 Z M 250.83 98.34 C 247.33 97.29 242.46 95.05 240.49 94.12 C 240.49 94.11 240.49 94.11 240.49 94.1 C 240.49 93.34 239.88 92.72 239.12 92.72 C 238.36 92.72 237.75 93.34 237.75 94.1 C 237.75 94.85 238.36 95.47 239.12 95.47 C 239.45 95.47 239.75 95.35 239.99 95.15 C 242.31 96.25 247.14 98.45 250.67 99.49 L 249.27 109.32 C 249.27 109.35 249.27 109.37 249.27 109.4 C 249.27 110.27 245.43 111.86 239.17 111.86 C 232.84 111.86 228.97 110.27 228.97 109.4 C 228.97 109.37 228.97 109.35 228.97 109.32 L 226.05 88.06 C 228.57 89.8 233.99 90.71 239.18 90.71 C 244.35 90.71 249.76 89.8 252.29 88.07 Z M 225.75 85.84 C 225.79 85.09 230.11 82.14 239.18 82.14 C 248.24 82.14 252.56 85.09 252.6 85.84 L 252.6 86.1 C 252.11 87.79 246.51 89.57 239.18 89.57 C 231.83 89.57 226.23 87.78 225.75 86.09 Z M 253.75 85.86 C 253.75 83.88 248.07 81 239.18 81 C 230.28 81 224.6 83.88 224.6 85.86 L 224.66 86.29 L 227.83 109.44 C 227.9 112.03 234.81 113 239.17 113 C 244.59 113 250.34 111.76 250.41 109.45 L 251.78 99.79 C 252.54 99.97 253.17 100.07 253.67 100.07 C 254.35 100.07 254.8 99.9 255.08 99.57 C 255.31 99.3 255.4 98.97 255.33 98.62 C 255.18 97.83 254.24 96.98 252.33 95.89 L 253.69 86.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 240px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                S3 Bucket
                                <br/>
                                (Log)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buc...
                </text>
            </switch>
        </g>
        <rect x="300" y="77" width="40" height="40" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/>
        <image x="299.5" y="76.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="282" y="125" width="77" height="15" stroke-width="0"/>
            <text x="319.5" y="134.5">
                VPC Endpoint
            </text>
        </g>
        <path d="M 60 77 L 100 77 L 100 117 L 60 117 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 91.91 98.36 L 88.77 97.1 L 88.77 108.46 C 89.69 108.37 90.43 108.07 90.95 107.54 C 91.92 106.56 91.91 105.11 91.91 105.1 Z M 87.63 108.47 L 87.63 97.1 L 84.5 98.36 L 84.5 105.09 C 84.51 105.21 84.64 108.14 87.63 108.47 Z M 93.04 105.09 C 93.05 105.15 93.08 106.99 91.77 108.33 C 90.92 109.2 89.72 109.64 88.2 109.64 C 84.46 109.64 83.4 106.67 83.36 105.1 L 83.36 97.97 C 83.36 97.74 83.51 97.53 83.72 97.44 L 87.99 95.73 C 88.13 95.68 88.28 95.68 88.42 95.73 L 92.69 97.44 C 92.9 97.53 93.04 97.74 93.04 97.97 Z M 94.75 103.97 L 94.75 96.36 L 88.2 93.74 L 81.65 96.36 L 81.65 103.95 C 81.65 104 81.58 107.5 83.67 109.65 C 84.77 110.78 86.29 111.35 88.2 111.35 C 90.13 111.35 91.66 110.78 92.76 109.64 C 94.85 107.48 94.76 104 94.75 103.97 Z M 93.58 110.43 C 92.26 111.8 90.45 112.49 88.2 112.49 C 85.97 112.49 84.17 111.8 82.84 110.43 C 80.42 107.93 80.51 104.09 80.52 103.93 L 80.52 95.98 C 80.52 95.74 80.66 95.53 80.87 95.45 L 87.99 92.6 C 88.13 92.55 88.28 92.55 88.42 92.6 L 95.53 95.45 C 95.75 95.53 95.89 95.74 95.89 95.98 L 95.89 103.95 C 95.9 104.09 96 107.92 93.58 110.43 Z M 70.3 101.67 L 78.81 101.67 L 78.81 102.81 L 70.3 102.81 C 66.83 102.81 64.19 100.5 64.01 97.32 C 64 97.19 64 97.04 64 96.89 C 64 92.98 66.74 91.54 68.33 91.03 C 68.32 90.85 68.31 90.66 68.31 90.48 C 68.31 87.37 70.53 84.12 73.47 82.92 C 76.91 81.51 80.55 82.19 83.19 84.75 C 84.08 85.61 84.76 86.54 85.25 87.56 C 85.93 86.97 86.76 86.65 87.65 86.65 C 89.53 86.65 91.52 88.13 91.87 90.96 C 93.11 91.27 94.67 91.94 95.77 93.35 L 94.87 94.05 C 93.86 92.75 92.33 92.2 91.22 91.98 C 90.97 91.93 90.78 91.71 90.77 91.45 C 90.62 88.94 89.05 87.79 87.65 87.79 C 86.82 87.79 86.08 88.18 85.52 88.93 C 85.39 89.09 85.19 89.18 84.98 89.14 C 84.77 89.11 84.6 88.97 84.53 88.78 C 84.09 87.59 83.39 86.54 82.4 85.56 C 80.09 83.33 76.91 82.74 73.9 83.97 C 71.36 85.01 69.45 87.81 69.45 90.48 C 69.45 90.79 69.47 91.09 69.51 91.38 C 69.54 91.67 69.36 91.93 69.08 92 C 67.61 92.37 65.14 93.49 65.14 96.89 C 65.14 97.01 65.14 97.12 65.15 97.24 C 65.29 99.82 67.46 101.67 70.3 101.67 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 80px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 430 77 L 470 77 L 470 117 L 430 117 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 448.6 87.18 L 450.84 87.18 L 450.84 86.05 L 448.6 86.05 Z M 444.67 87.18 L 446.91 87.18 L 446.91 86.05 L 444.67 86.05 Z M 440.74 87.18 L 442.98 87.18 L 442.98 86.05 L 440.74 86.05 Z M 464.15 103.09 C 463.98 103.49 463.62 103.79 463.19 103.93 L 463.19 100.76 C 463.53 100.88 463.83 101.1 464.04 101.41 C 464.36 101.9 464.4 102.51 464.15 103.09 Z M 435.85 103.09 C 435.6 102.51 435.64 101.9 435.96 101.41 C 436.17 101.1 436.47 100.88 436.81 100.76 L 436.81 103.93 C 436.38 103.79 436.02 103.49 435.85 103.09 Z M 464.98 100.79 C 464.56 100.16 463.92 99.74 463.19 99.59 L 463.19 98.59 C 463.19 96.63 461.68 95.04 459.82 95.04 L 449.16 95.04 L 449.16 96.16 L 459.82 96.16 C 461.06 96.16 462.07 97.25 462.07 98.59 L 462.07 106.08 C 462.07 107.42 461.06 108.51 459.82 108.51 L 440.18 108.51 C 438.94 108.51 437.93 107.42 437.93 106.08 L 437.93 98.59 C 437.93 97.25 438.94 96.16 440.18 96.16 L 442.98 96.16 L 442.98 95.04 L 440.18 95.04 C 438.32 95.04 436.81 96.63 436.81 98.59 L 436.81 99.59 C 436.08 99.74 435.44 100.16 435.02 100.79 C 434.49 101.6 434.41 102.6 434.82 103.54 C 435.17 104.35 435.92 104.92 436.81 105.09 L 436.81 106.08 C 436.81 108.04 438.32 109.63 440.18 109.63 L 446.91 109.63 L 446.91 113 L 448.04 113 L 448.04 109.63 L 451.4 109.63 L 451.4 113 L 452.53 113 L 452.53 109.63 L 459.82 109.63 C 461.68 109.63 463.19 108.04 463.19 106.08 L 463.19 105.09 C 464.08 104.92 464.83 104.35 465.18 103.54 C 465.59 102.6 465.51 101.6 464.98 100.79 Z M 445.23 102.33 C 445.23 103.26 444.47 104.02 443.54 104.02 C 442.62 104.02 441.86 103.26 441.86 102.33 C 441.86 101.4 442.62 100.65 443.54 100.65 C 444.47 100.65 445.23 101.4 445.23 102.33 Z M 440.74 102.33 C 440.74 103.88 442 105.14 443.54 105.14 C 445.09 105.14 446.35 103.88 446.35 102.33 C 446.35 100.79 445.09 99.53 443.54 99.53 C 442 99.53 440.74 100.79 440.74 102.33 Z M 454.77 102.33 C 454.77 101.4 455.53 100.65 456.46 100.65 C 457.38 100.65 458.14 101.4 458.14 102.33 C 458.14 103.26 457.38 104.02 456.46 104.02 C 455.53 104.02 454.77 103.26 454.77 102.33 Z M 459.26 102.33 C 459.26 100.79 458 99.53 456.46 99.53 C 454.91 99.53 453.65 100.79 453.65 102.33 C 453.65 103.88 454.91 105.14 456.46 105.14 C 458 105.14 459.26 103.88 459.26 102.33 Z M 438.49 82.6 C 438.49 82.34 438.71 82.12 438.97 82.12 L 453.09 82.12 L 453.09 91.17 C 453.09 91.44 452.86 91.67 452.59 91.67 L 446.91 91.67 C 446.6 91.67 446.35 91.92 446.35 92.23 L 446.35 95.14 L 443.87 91.89 C 443.76 91.75 443.6 91.67 443.42 91.67 L 438.97 91.67 C 438.71 91.67 438.49 91.45 438.49 91.19 Z M 438.97 92.79 L 443.15 92.79 L 446.47 97.14 C 446.57 97.28 446.74 97.36 446.91 97.36 C 446.97 97.36 447.03 97.35 447.09 97.33 C 447.32 97.26 447.47 97.04 447.47 96.8 L 447.47 92.79 L 452.59 92.79 C 453.48 92.79 454.21 92.06 454.21 91.17 L 454.21 81.86 C 454.21 81.38 453.83 81 453.35 81 L 438.97 81 C 438.09 81 437.37 81.72 437.37 82.6 L 437.37 91.19 C 437.37 92.07 438.09 92.79 438.97 92.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 450px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ChatBot
                                <br/>
                                Slack Client
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="450" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ChatBo...
                </text>
            </switch>
        </g>
        <rect x="220" y="197" width="140" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 194px; margin-left: 221px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                sns-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="290" y="194" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    sns-construct
                </text>
            </switch>
        </g>
        <image x="269.5" y="216.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAABqElEQVRYR+1Yy5KEIBATxBcozP9/7SYZwLF2LytzsEpzIQZIWd1N++gePPgLtm+FzU7d9GrHnL1ivm5B3L1SE45eLtNzcI/Xv/B4/Q/38wpLC/zBqxnFK+TrFqzZy8z5XhtQm/RV4YaBgx0A3SxJT9KTGRBDojksfi+vc1hea2pB7KihTF6viQozu5HMlOhqSRZKK0giGSnJFaPnCHBnq9dHfW306jdgpBJAAslIiV6GRA/BBUQV4CjJCwbFa/ZewTkN671u+cJwzim1p2GcK0FC6C7Zv9YY27z6GFWNNwFir1OI1Dr37gWAsmzJWDD7XE92mONL6kcev3a2N/BWr5JHF2OkF1Ibo/oEqiSqF0yU6GVI1Cc8iHYOlN59Im+7CSa8oLfGvsTrmzXxTa8tJUmdBWjaGbJKJJHsEsm+fEipeN0BMSWG9zxcSuU8ImuXfHbg012pPQ98vWd2VfgQ2m6xD+HXuxy/6XXgUSVJfXWmxBNjSdRXVxBFeqSkpKHP1PNYvHhYG882N7R6aYpAUWisf2b0g4fEkKhgSCTVuX15ER48OKDrfgCPPT8Qh4k7owAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="276" y="265" width="30" height="15" stroke-width="0"/>
            <text x="289.5" y="274.5">
                Topic
            </text>
        </g>
        <path d="M 50 217 L 90 217 L 90 257 L 50 257 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 56.83 234.61 L 64.18 234.61 L 64.18 233.48 L 56.83 233.48 Z M 82.12 242.83 L 82.92 243.62 L 78.72 247.82 C 78.61 247.93 78.46 247.98 78.32 247.98 C 78.18 247.98 78.03 247.93 77.92 247.82 L 75.81 245.71 L 76.61 244.91 L 78.32 246.62 Z M 84.72 245.94 C 84.59 247 84.16 247.99 83.47 248.79 C 82.98 249.37 82.36 249.84 81.68 250.16 C 80.77 250.6 79.75 250.76 78.74 250.64 C 77.76 250.52 76.83 250.13 76.06 249.52 C 74.58 248.34 73.83 246.52 74.06 244.64 C 74.27 242.86 75.35 241.31 76.94 240.49 C 77.7 240.1 78.53 239.9 79.38 239.9 C 79.6 239.9 79.82 239.91 80.04 239.94 C 81.81 240.15 83.36 241.24 84.17 242.83 C 84.66 243.79 84.85 244.87 84.72 245.94 Z M 85.18 242.32 C 84.19 240.39 82.32 239.08 80.17 238.81 C 78.88 238.66 77.58 238.89 76.42 239.49 C 74.5 240.48 73.2 242.35 72.94 244.5 C 72.66 246.77 73.57 248.98 75.36 250.4 C 76.29 251.14 77.42 251.61 78.6 251.76 C 78.87 251.79 79.13 251.81 79.39 251.81 C 80.35 251.81 81.3 251.6 82.16 251.18 C 82.99 250.79 83.74 250.22 84.33 249.53 C 85.16 248.55 85.69 247.36 85.84 246.07 C 86 244.78 85.77 243.48 85.18 242.32 Z M 62.48 238 L 64.74 238 L 64.74 236.87 L 62.48 236.87 Z M 56.83 238 L 61.35 238 L 61.35 236.87 L 56.83 236.87 Z M 56.34 223.32 L 81.62 223.32 C 82.29 223.32 82.83 223.98 82.83 224.79 L 82.83 228.97 L 81.14 228.97 L 81.14 226.15 C 81.14 225.83 80.88 225.58 80.57 225.58 L 67.57 225.58 C 67.26 225.58 67 225.83 67 226.15 L 67 228.97 L 55.13 228.97 L 55.13 224.79 C 55.13 223.99 55.68 223.32 56.34 223.32 Z M 74.08 228.8 C 75.39 228.8 76.46 229.86 76.46 231.16 C 76.46 232.01 75.99 232.8 75.23 233.21 C 74.51 233.6 73.64 233.6 72.92 233.21 C 72.17 232.8 71.7 232.01 71.7 231.16 C 71.7 229.86 72.76 228.8 74.08 228.8 Z M 55.13 241.05 L 55.13 230.1 L 67 230.1 L 67 239.69 C 67 240.01 67.26 240.26 67.57 240.26 L 73.48 240.26 L 73.48 239.13 L 68.76 239.13 C 68.81 236.87 70.31 234.92 72.47 234.29 C 73.47 234.76 74.66 234.77 75.68 234.29 C 76.91 234.65 77.99 235.47 78.65 236.58 L 79.62 235.99 C 78.94 234.86 77.9 233.98 76.69 233.47 C 77.26 232.84 77.59 232.02 77.59 231.16 C 77.59 229.24 76.01 227.67 74.08 227.67 C 72.14 227.67 70.57 229.24 70.57 231.16 C 70.57 232.02 70.9 232.83 71.46 233.46 C 69.95 234.1 68.77 235.29 68.13 236.76 L 68.13 226.71 L 80.01 226.71 L 80.01 235.74 L 81.14 235.74 L 81.14 230.1 L 82.83 230.1 L 82.83 238.56 L 83.96 238.56 L 83.96 224.79 C 83.96 223.36 82.91 222.19 81.62 222.19 L 56.34 222.19 C 55.05 222.19 54 223.36 54 224.79 L 54 241.05 C 54 242.48 55.05 243.64 56.34 243.64 L 71.53 243.64 L 71.53 242.52 L 56.34 242.52 C 55.68 242.52 55.13 241.84 55.13 241.05 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 264px; margin-left: 70px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Cognito
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="276" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cognito
                </text>
            </switch>
        </g>
        <image x="129.5" y="216.5" width="40" height="40" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="109" y="265" width="84" height="15" stroke-width="0"/>
            <text x="149.5" y="274.5">
                SecretManager
            </text>
        </g>
        <path d="M 420 217 L 460 217 L 460 257 L 420 257 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 447.38 244.56 L 451.69 244.56 L 451.69 243.33 L 447.38 243.33 Z M 441.85 244.56 L 446.15 244.56 L 446.15 243.33 L 441.85 243.33 Z M 436.31 244.56 L 440.61 244.56 L 440.61 243.33 L 436.31 243.33 Z M 443.08 239.63 C 443.08 238.96 443.63 238.4 444.31 238.4 C 444.99 238.4 445.54 238.96 445.54 239.63 C 445.54 240.31 444.99 240.86 444.31 240.86 C 443.63 240.86 443.08 240.31 443.08 239.63 Z M 446.77 239.63 C 446.77 238.28 445.67 237.17 444.31 237.17 C 442.95 237.17 441.85 238.28 441.85 239.63 C 441.85 240.99 442.95 242.1 444.31 242.1 C 445.67 242.1 446.77 240.99 446.77 239.63 Z M 438.15 238.4 C 438.83 238.4 439.38 238.96 439.38 239.63 C 439.38 240.31 438.83 240.86 438.15 240.86 C 437.47 240.86 436.92 240.31 436.92 239.63 C 436.92 238.96 437.47 238.4 438.15 238.4 Z M 438.15 242.1 C 439.51 242.1 440.61 240.99 440.61 239.63 C 440.61 238.28 439.51 237.17 438.15 237.17 C 436.79 237.17 435.69 238.28 435.69 239.63 C 435.69 240.99 436.79 242.1 438.15 242.1 Z M 456 232.86 L 456 247.02 C 456 247.36 455.72 247.63 455.38 247.63 L 436.31 247.63 L 436.31 246.4 L 454.77 246.4 L 454.77 233.48 L 441.23 233.48 L 441.23 232.25 L 455.38 232.25 C 455.72 232.25 456 232.52 456 232.86 Z M 434.27 235.97 C 434.02 236.05 433.84 236.29 433.84 236.56 L 433.84 249.51 L 432.37 250.77 L 430.77 248.94 L 430.77 247.33 C 430.77 247.16 430.7 247.01 430.59 246.89 L 429.18 245.48 L 430.59 244.07 C 430.7 243.95 430.77 243.8 430.77 243.63 L 430.77 242.4 C 430.77 242.24 430.7 242.08 430.59 241.97 L 429.18 240.56 L 430.59 239.15 C 430.7 239.03 430.77 238.87 430.77 238.71 L 430.77 236.56 C 430.77 236.29 430.59 236.06 430.34 235.98 C 427.17 234.97 425.3 231.72 425.99 228.4 C 426.48 226.02 428.32 224.1 430.67 223.53 C 432.66 223.04 434.72 223.46 436.29 224.7 C 437.87 225.94 438.77 227.79 438.77 229.79 C 438.77 232.59 436.92 235.13 434.27 235.97 Z M 440 229.79 C 440 227.41 438.92 225.2 437.05 223.73 C 435.18 222.26 432.74 221.75 430.37 222.33 C 427.57 223.02 425.37 225.3 424.78 228.16 L 424.78 228.16 C 424 231.94 426.03 235.66 429.54 237 L 429.54 238.46 L 427.87 240.12 C 427.63 240.36 427.63 240.75 427.87 240.99 L 429.54 242.66 L 429.54 243.38 L 427.87 245.05 C 427.63 245.29 427.63 245.68 427.87 245.92 L 429.54 247.58 L 429.54 249.17 C 429.54 249.32 429.59 249.47 429.69 249.58 L 431.84 252.04 C 431.96 252.18 432.13 252.25 432.31 252.25 C 432.45 252.25 432.59 252.2 432.71 252.1 L 434.86 250.26 C 435 250.14 435.08 249.97 435.08 249.79 L 435.08 236.99 C 438 235.87 440 232.97 440 229.79 Z M 432.31 231.25 C 431.46 231.25 430.77 230.56 430.77 229.71 C 430.77 228.86 431.46 228.17 432.31 228.17 C 433.15 228.17 433.84 228.86 433.84 229.71 C 433.84 230.56 433.15 231.25 432.31 231.25 Z M 432.31 226.94 C 430.78 226.94 429.54 228.18 429.54 229.71 C 429.54 231.23 430.78 232.48 432.31 232.48 C 433.83 232.48 435.08 231.23 435.08 229.71 C 435.08 228.18 433.83 226.94 432.31 226.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 264px; margin-left: 440px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                KMS Key
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="440" y="276" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    KMS Key
                </text>
            </switch>
        </g>
        <rect x="0" y="17" width="520" height="300" rx="45" ry="45" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 518px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                share-resources-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    share-resources-stack
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>