# HowToUseECS

ここでは、ECS スタックの利用方法と各デプロイパターンについて記載する。

## 概要

- AWS ECS / AWS Fargate を中心としたアーキテクチャと、CI/CD パイプラインをデプロイするスタックである。
- Web サービスを提供することを想定した AWS ECS とその他リソースによって構成されている。
- CI/CD パイプラインのデプロイパターンが ecspresso と Blue/Green デプロイの 2 つを用意している。
  - [ToDo] Blue/Green デプロイは CDK と相性が悪いため、要アップデート

## ECS サービス、タスクのデプロイについて

- デプロイパターンとして、以下の２パターン用意する。
  - [ecspresso](https://github.com/kayac/ecspresso)を使用したローリングデプロイ
  - CodeDeploy を使用した Blue/Green デプロイ
- デプロイパターン 2 種類 × パブリック（Frontend）/プライベート（Backend）2 種類で以下 4 パターンのサンプルをテンプレートとして用意している。

![](./images/ECS-Template-Patterns.dio.png)

- ECS 関連のリソースは`ecs-stack.ts`に一式まとめられており、共通リソースと各デプロイに必要なリソースはコンストラクト単位で管理されている。
  ![](./images/ECS-Constructs.dio.png)
- ローリングデプロイ利用時の使用リソースは以下のとおり。

  ![](./images/ECS-Rolling-Constructs.dio.png)

- Blue/Green 利用時の使用リソースは以下のとおり。

  ![](./images/ECS-BlueGreen-Constructs.dio.png)

- ローリングデプロイと Blue/Green デプロイでは ECS サービスと ECS タスクの管理方法が異なる。
- **ローリングデプロイの ECS サービス/タスク管理方法**

  - ローリングデプロイは ecspresso を利用して ECS サービスと ECS タスクを作成するため、CDK 初回デプロイ時には生成されない。
  - CodePipeline を通して、ecspresso コマンドを実行し、ECS サービス/タスクを作成・更新する。

    ![](./images/ECS-How-To-Manage-Rolling-Resources.dio.png)

  - 環境変数は CodeBuild 内で設定され、ECS サービス/タスクの json ファイルに直接、埋め込むことが可能
  - [公式ドキュメント](https://github.com/kayac/ecspresso#configuration-file)にも記載のとおり、`` {{ must_env `FOO` }} ``という形で環境変数を動的に渡すことが可能。`must_env`と記載すると、`FOO`が存在しない場合、処理は強制的に終了する。

    ![](./images/ECS-How-To-Manage-Rolling-Environment-Variable.dio.png)

  - ※`` {{ env `FOO` `bar` }} ``と記載すると、`FOO`が定義されていない場合、`bar`に置き換わる。
  - 詳細な利用方法は[公式ドキュメントを参照](https://github.com/kayac/ecspresso#ecspresso)

- **Blue/Green デプロイの ECS サービス/タスク管理方法**

  - Blue/Green デプロイについては CDK 側で ECS サービスと ECS タスクを作成する。
  - CodeDeploy の DeploymentGroup 上で ECS サービス/タスクを指定する必要があるため、初回デプロイ時は CDK で ECS サービス/タスクを作成する。

    ![](./images/ECS-How-To-Manage-BG-Resources.dio.png)

  - 環境変数は CDK 内の props で受け渡しが可能

    ![](./images/ECS-How-To-Manage-BG-Environment-Variable.dio.png)

  - 初回以降は CodePipeline を利用して、ECS タスクは`taskdef.json`、ECS サービスは`appspec.yaml`から更新する。

    ![](./images/ECS-How-To-Update-BG-Resources.dio.png)

## ECS サービス/タスク のデプロイを動的に制御している箇所について

- `ecs-app-construct` では下記のサンプルコードのように `map` や `forEach` を使用してコンストラクトを呼び出す回数を動的に制御している箇所がある。
- これは `params/` 配下の各環境ファイルで定義したタスクの数だけコンストラクトを呼び出すことで、デプロイする ECS サービス/タスク の数を動的に制御するためである。

```typescript
const frontEcsApps = props.ecsFrontTasks.map((ecsApp) => {
  return new EcsappConstruct(this, `${props.prefix}-${ecsApp.appName}-FrontApp-Ecs-Resources`, {
    ...,
  });
});
this.frontEcsApps = frontEcsApps;

frontEcsApps.forEach((ecsApp, index) => {
  const ecsTaskRole = new EcsTaskRole(this, `${props.prefix}-${ecsApp.appName}-EcsTaskRole`, {
    ...,
  });

  new PipelineEcspressoConstruct(this, `${props.prefix}-${ecsApp.appName}-FrontApp-Pipeline`, {
    ...,
  });
});
```

- 下記のサンプルコードは `params/` 配下の各環境ファイルで定義されているタスクの数を制御しているパラメータの一つである。
- `ecs-app-construct` では他にも `EcsBackTasks`, `EcsFrontBgTasks`, `EcsBackBgTasks` でデプロイする ECS サービス/タスク の数を制御している。
- 配列の要素の数だけ ECS サービス/タスク がデプロイされる。
- パラメーターの指定方法は以下を参照すること。
  - [[ecspresso(Rolling)]パラメータの指定方法](#ecspressorollingパラメータの指定方法)
  - [[Blue/Green]パラメータの指定方法](#bluegreenパラメータの指定方法)

```typescript
export const EcsFrontTasks: inf.IEcsAlbParam[] = [
  {
    appName: 'EcsApp',
    portNumber: 80,
  },
  {
    appName: 'EcsApp2',
    portNumber: 80,
    path: '/path',
  },
];
```

## ecspresso を使用したローリングデプロイパターン

### [ecspresso(Rolling)]作成リソース

- `ecs-stack.ts`

  - `ecs-common-construct.ts`

    - タスク実行ロール
    - ECS クラスター
    - ECS イベント監視

  - `alb-construct.ts`

    - ALB
    - Target Group（パラメータで指定された分の数を作成）⇒ リソースは`alb-target-group-construct.ts`で定義し、コンストラクトを呼び出し

  - `ecs-app-construct.ts`の作成リソース

    - ECS サービス用の ECR リポジトリ
    - Security Group
    - CloudWatch Logs
    - ECS サービスの監視設定

  - `ecs-task-role-construct` の作成リソース

    - タスクロール

  - `pipeline-ecspresso-construct.ts`

    - ECS サービス、ECS タスクデプロイ用の CodePipeline
    - デプロイ用 CodeBuild プロジェクト
    - ソース S3 バケット

### [ecspresso(Rolling)]パラメータの指定方法

- パラメータは params フォルダ配下の環境名ファイルで以下のとおり指定し、リストに指定した数だけ ECS サービス用の TG や ECR リポジトリといったリソースが作成される。
- Front に関しては、デフォルトルールを 必ず 1 つ設定する必要があり（0 でも 2 以上でも不可）。
  - `path` の指定がないタスクがデフォルトルールとなる。デフォルトルールではないタスクの場合は `path` を必ず指定する。
  - `path` はリクエストを振り分けるパスの文字列を指定する。
- Back に関してはアプリ名とポート番号のみ指定すれば良い。
  - `path` の指定は不要である。

```typescript
export const EcsFrontTasks: inf.IEcsAlbParam[] = [
  {
    appName: 'EcsApp',
    portNumber: 80,
  },
  {
    appName: 'EcsApp2',
    portNumber: 80,
    path: '/path',
  },
];
```

```typescript
export const EcsBackTasks: inf.IEcsInternalParam[] = [
  {
    appName: 'EcsBackend',
    portNumber: 8080,
  },
  {
    appName: 'EcsBackend2',
    portNumber: 8080,
  },
];
```

### [ecspresso(Rolling)]デプロイ方法

- デプロイ(ECS サービスの更新、ECS タスクの更新)は CodeBuild 上で ecspresso コマンドによって実行される
- S3 バケットに ecspresso 用設定ファイルを zip で格納すると、パイプラインが稼働する。ECS サービスが存在しない場合は新規作成される

- 初回および 2 回目以降のデプロイ方法以下のようになる。ecspresso の場合は初回も 2 回目以降も同様の管理方法となる。

| 管理対象リソース | 初回デプロイ         | 2 回目以降のデプロイ |
| ---------------- | -------------------- | -------------------- |
| ALB,TargetGroup  | CDK                  | CDK                  |
| ECS サービス     | ecspresso(CodeBuild) | ecspresso(CodeBuild) |
| AutoScale        | AWS CLI(CodeBuild)   | AWS CLI(CodeBuild)   |
| ECS タスク定義   | ecspresso(CodeBuild) | ecspresso(CodeBuild) |

## CodeDeploy を使用した Blue/Green デプロイパターン

### [Blue/Green]作成リソース

- `ecs-stack.ts`

  - `ecs-common-construct.ts`

    - タスク実行ロール
    - ECS クラスター
    - ECS イベント監視

  - `alb-construct.ts`

    - ALB（1 個）
    - Target Group（Blue）
    - Target Group（Green）
    - ※リソースは`alb-target-group-construct.ts`で定義し、パラメータで指定された分の数を作成

  - `ecsapp-construct.ts`

    - ECS サービス用の ECR リポジトリ
    - Security Group
    - CloudWatch Logs
    - ECS サービスの監視設定

  - `ecs-service-construct`

    - タスクロール
    - タスク定義
    - ECS サービス (Fargate)

  - `pipeline-blue-green-construct.ts`

    - ECS サービス、ECS タスクデプロイ用の CodePipeline
    - ソース S3 バケット

### [Blue/Green]パラメータの指定方法

- パラメータは以下のとおり指定し、リストに指定した数だけ ECS サービス用の TG や ECR リポジトリといったリソースが作成される。
- Front に関しては、デフォルトルールを 必ず 1 つ設定する必要があり（0 でも 2 以上でも不可）。
  - `path` の指定がないタスクがデフォルトルールとなる。デフォルトルールではないタスクの場合は `path` を必ず指定する。
  - `path` はリクエストを振り分けるパスの文字列を指定する。
- Back に関してはアプリ名とポート番号のみ指定すれば良い。
  - `path` の指定は不要である。

```typescript
export const EcsFrontBgTasks: inf.IEcsAlbParam[] = [
  {
    appName: 'EcsAppBg',
    portNumber: 80,
  },
  {
    appName: 'EcsApp2Bg',
    portNumber: 80,
    path: '/path',
  },
];
```

```typescript
export const EcsBackBgTasks: inf.IEcsAlbParam[] = [
  {
    appName: 'EcsBackendBg',
    portNumber: 80,
  },
  {
    appName: 'EcsBackend2Bg',
    portNumber: 80,
  },
];
```

### [Blue/Green]デプロイ方法

- 初回のリソースはすべて CDK で作成するが、2 回目以降のコンテナイメージ、ECS タスク定義の更新は CodeDeploy を経由して実行する。（CodeDeploy が新規作成に対応していないため）

![](./images/ECS-BlueGreen.dio.png)

- S3 バケットに CodeDeploy 用設定ファイルを zip で格納すると、パイプラインが稼働する。
- 実際のビルド（docker build および S3 へのイメージ格納）は GitHub Actions 上で実行する想定だが、本リポジトリではサンプルとして`build.sh`を container フォルダ配下に用意しており、各環境ファイルの`APP_NAME`と`PJ_PREFIX`を設定すると動的に`build.sh`内で`AWS_ACCOUNT_ID`、`S3_BUCKET`、`IMAGE_REPO_NAME`が取得されパイプラインが稼働する。

- タスク定義について、CodeDeploy でデプロイ後に`cdk deploy`によるタスク定義旧戻りの恐れが出てくるが、CodeDeploy デプロイ後に CDK で変更しようすると以下のようなエラーとなるため、旧戻りの恐れはない。(コード上には残る)

```
Resource handler returned message: "Invalid request provided: UpdateService error: Unable to update task definition on services with a CODE_DEPLOY deployment controller. Use AWS CodeDeploy to trigger a new deployment.
```

- ECS 関連リソースの管理は以下のようになる。

| 管理対象リソース | 初回デプロイ | 2 回目以降のデプロイ        |
| ---------------- | ------------ | --------------------------- |
| ALB,TargetGroup  | CDK          | CDK                         |
| ECS サービス     | CDK          | CodeDeploy [TODO]動作確認中 |
| AutoScale        | CDK          | CDK [TODO]動作確認          |
| ECS タスク定義   | CDK          | CodeDeploy                  |

- [TODO]初回デプロイ時に指定するダミーポート 80 番が SG ルールとして残存してしまうため、初回デプロイ以降、どのように対応するか検討

## 踏み台コンテナについて

- 踏み台コンテナとは、踏み台サーバとして機能する ECS コンテナである。
  - ※ Protected サブネット内に配置されたリソースは VPC 外部と通信できないため、接続には踏み台サーバを経由する必要がある。
- `bastion-ecs-construct` コンストラクトでは、以下の機能を実装している。
  - ECS コンテナ関連リソースのデプロイ。
  - cdk deploy 実行時に、踏み台コンテナ (Docker イメージ) のビルドと、ECR リポジトリへプッシュの実行。
- 踏台コンテナでは以下のリソースに接続することを目的としている。
  - Aurora
  - ElastiCache
  - OpenSearch
  - EFS

### bastion-ecs-construct 作成リソース

- `ecs-stack.ts`

  - `bastion-ecs-construct.ts`

    - ECR リポジトリ
    - タスクロール
    - CloudWatch ロググループ
    - ECS タスク定義
    - セキュリティグループ
