# SendGridLog

## Overview

At Mynavi, it is recommended to use SendGrid for sending emails.

This document describes the implementation of SendGrid event logging using AWS services. The solution enables collecting, storing, and analyzing SendGrid email events in a cost-effective way.

SendGrid posts event data to API Gateway based on the SendGrid Webhook configuration.
Logs are stored in S3 in Parquet format to reduce query costs when querying with <PERSON>.

## Architecture Flow

The flow is as follows: SendGrid → API Gateway → Kinesis Firehose → S3 ← Athena.
![](images/SendGridLog-Flow.png)

**Key Components:**

- **SendGrid**: Email service that generates event data (bounces, deliveries, opens, etc.)
- **Amazon Cognito** :
  - Manages OAuth 2.0 authentication for SendGrid webhooks
  - Provides secure token-based authentication
  - Components:
    - User Pool: Manages webhook application credentials
    - Resource Server: Defines custom scopes for webhook access
    - App Client: Used by SendGrid for OAuth token requests
    - Domain: Hosts the OAuth endpoints
- **API Gateway**: Receives webhook events from SendGrid
- **Kinesis Firehose**: Processes and transforms data into Parquet format
- **S3**: Storage destination for log data
- **Athena**: SQL query service for analyzing logs
- **CloudWatch Alarms**:
  - Monitors API Gateway metrics:
    - 4XX errors (authentication/validation failures)
    - 5XX errors (system/integration failures)
  - Integration with SNS for notifications

## OAuth 2.0 Configuration & Cost Optimization

### Access Token Expiration Configuration

Our investigation revealed that SendGrid webhooks cache access tokens based on the `expire_in` value configured in the OAuth 2.0 provider (Cognito). Key findings:

- **Token Caching Behavior**: SendGrid caches access tokens and respects the expiration time set by Cognito
- **Server Distribution**: SendGrid uses approximately 6 servers per SENDGRID_KEY for email delivery
- **Upper Limit**: Cognito's maximum `expires_in` value is 1 day (24 hours)

### Server Distribution Verification

To verify SendGrid's server distribution pattern, we conducted the following investigation:

**Testing Method:**

1. Configure Cognito `expires_in` to 1 day (24 hours)
2. Run automated email sending script: 1 email every 15 minutes (~100 emails/day)
3. Monitor event data and OAuth token requests

**Event Data Analysis:**
Each SendGrid event contains a `sg_message_id` with a pattern like `recvd-*`. Example event data:
![](images/SendGridLog-EventData.png)

**CloudTrail Token Request Correlation:**
CloudTrail logs showing TOKEN_POST requests when new servers appear:
![](images/SendGridLog-CloudTrailLog.png)

**Key Observations:**

- The `sg_message_id` contains server identifiers in the format `recvd-{server_id}`
- New server IDs appear when new OAuth tokens are requested
- CloudTrail logs show TOKEN_POST requests correlating with new server ID appearances

**Verification Query:**
Use Athena to extract server IDs and correlate with timestamps:

```sql
SELECT
  from_unixtime(timestamp) AT TIME ZONE 'Asia/Bangkok' AS utc_plus_7_time,
  regexp_extract(sg_message_id, '\.recvd-([^-]+)', 1) AS server_id
FROM "apigateway_sendgrid_test"
WHERE date = '2025-05-24'
ORDER BY timestamp;
```

**Query Results:**
Athena query results showing server ID patterns matching CloudTrail token requests:
![](images/SendGridLog-AthenaQuery1.png)
![](images/SendGridLog-AthenaQuery2.png)

**Results:**

- CloudTrail TOKEN_POST requests align with new server ID appearances in event data
- Consistent pattern of approximately 6 unique server IDs per SENDGRID_KEY
- Server rotation follows OAuth token expiration cycle

Based on this testing methodology and analysis, we can assume that **SendGrid uses approximately 6 servers per SENDGRID_KEY for email delivery**.

### Cognito Cost Estimation

**Pricing (Tokyo region):**

- Base cost: $6.00/month per application using client credential flow
- Token requests: $0.00225 per M2M token request

**Recommended Configuration (1-day expiration):**

- Cost: $6 + $0.00225 × 6 servers × 30 days = **$6.41/month**

Since Cognito's upper limit for `expires_in` is 1 day, and assuming SendGrid doesn't increase the number of servers, the Cognito cost will remain around **$6.41/month** per application.
This cost estimation is based on current AWS pricing and SendGrid server configuration. Actual costs may vary in the future due to pricing changes or modifications in SendGrid's infrastructure.

### Configuration Change Impact

⚠️ **Note**: Changes to OAuth configuration (such as access token expiration time) in Cognito only apply to tokens issued after the change. Previously issued tokens retain their original expiration time and remain valid until they naturally expire.

## Implementation Steps

### 1. Configure SendGrid Webhook

1. Log into SendGrid dashboard
2. Navigate to Settings → Mail Settings → Event Webhook
3. Configure the webhook:
   - HTTP Post URL: Your API Gateway endpoint
   - Select events to track (bounces, deliveries, opens, etc.)
   - Enable OAuth2.0 webhook for security
     - **Client ID**: Get from Cognito App Client settings
     - **Client Secret**: Get from Cognito App Client settings
     - **Token URL**: `https://{your-cognito-domain}.auth.{region}.amazoncognito.com/oauth2/token`
       ![](images/SendGridLog-OAuth-Webhook.png)

### 2. Data Storage

When an event is received from SendGrid, a log will be stored in Parquet format in the S3 bucket.

### 3. Querying Using Athena

1. Setting query Editor
   ![](images/SendGridLog-Setting-QueryEditor.png)
   Specify Query result location
   ![](images/SendGridLog-QueryResultLoc.png)
2. Choose Athena Database
   ![](images/SendGridLog-Database.png)
3. Sample Queries:
   ```sql
   select * from "dev21blea_xxxx_sendgrid_logs"."apigateway_sendgrid";
   ```
   ![](images/SendGridLog-QueryResult.png)

## Monitoring and Alerting:

### CloudWatch Alarms

1. **API Gateway 4XX errors**
   - Threshold: > 10 errors in 5 minutes
   - Action: SNS notification
2. **API Gateway 5XX errors**
   - Threshold: > 10 error in 5 minutes
   - Action: SNS notification

**4xx**
![](images/SendGridLog-4xx-Alarm.png)
**Message**
![](images/SendGridLog-4xx-Message.png)

**5xx**
![](images/SendGridLog-5xx-Alarm.png)
**Message**
![](images/SendGridLog-5xx-Message.png)

### Error Handling

In case SendGrid fails to send the event to API Gateway (4xx, 5xx errors):

- SendGrid will retry the POST request until it receives a 2xx response or the maximum time has elapsed
- All events are retried at increasing intervals for up to 24 hours after the event occurs
- This is a rolling 24-hour period, meaning new failing events will receive their own 24-hour retry period

Reference:

- [SendGrid Event Webhook Documentation](https://www.twilio.com/docs/sendgrid/for-developers/tracking-events/getting-started-event-webhook)
- [SendGrid Event Webhook Retry Logic](https://www.twilio.com/docs/sendgrid/for-developers/tracking-events/getting-started-event-webhook#retry-logic)

## Configuration

### `SendGridLogParams` Sample Code

```typescript
export const SendGridLogParams: inf.ISendGridLogParam[] = [
  {
    s3BucketArn: '', // If you have an existing bucket, enter the ARN here. If you don't have one, leave it blank, and a new bucket will be created.
    suffix: 'xxxx', // 4 character string
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
  {
    s3BucketArn: '', // If you have an existing bucket, enter the ARN here. If you don't have one, leave it blank, and a new bucket will be created.
    suffix: 'abcdef', // 4 character string
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
];
```
