# HowToUseOpenSearch

ここでは、OpenSearch スタックの利用方法を記載する

## 概要

- OpenSearch Service をデプロイするスタックである。
- OpenSearch では、比較的コストが高いサービスのため、dev や stg 環境では極力、最小スペックで稼働させること。
- 本スタックでは通常の OpenSearch Service と OpenSearch Serverless の 2 つのタイプを用意しており、環境ファイルの設定にて切り替えが可能である。
- マスターノードは各ノードへのルーティングなどドメイン全体の管理を行い、データノードはデータのコンピュートを実施する。

## OpenSearch 構成図

![OpenSearch構成図](images/OpenSearch-Detail-Architecture.dio.svg)

## OpenSearch Service と OpenSearch Serverless の切替について

- `params/` 配下の各環境ファイルに記載されている `OpensearchTypeParam` にて制御している。
- `openSearchType` を `PROVISION` のままデプロイすることで通常の OpenSearch Service がデプロイ可能である。
- `openSearchType` を `SERVERLESS` に変更することで OpenSearch Serverless に切り替えが可能。

```typescript
export const OpensearchTypeParam: inf.IOpenSearchTypeParam = {
  openSearchType: 'PROVISION',
};
```

### 各タイプの設定について

- `OpensearchParam` にて 2 つの設定を記載している。
- インスタンスタイプは `openSearchProvisionedParam` に記載

```typescript
export const OpensearchParam: inf.IOpenSearchParam = {
  openSearchProvisionedParam: {
    ...(略)
    masterNodeInstanceType: 't3.medium.search',
    dataNodeInstanceType: 't3.medium.search',
  },
  openSearchServerlessParam: {
    collectionType: 'SEARCH',
  },
};
```
