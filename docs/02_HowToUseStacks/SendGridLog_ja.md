# SendGridLog

## 概要

Mynavi では、メール送信に SendGrid を使用することが推奨されています。

このドキュメントでは、AWS サービスを使用した SendGrid イベントログの実装について説明します。このソリューションにより、SendGrid のメールイベントを効率的に収集、保存、分析することができます。

SendGrid は、SendGrid Webhook の設定に基づいてイベントデータを API Gateway に送信します。
ログは Athena でクエリする際のコスト削減のため、S3 に Parquet 形式で保存されます。

## アーキテクチャフロー

フローは次の通りです：SendGrid → API Gateway → Kinesis Firehose → S3 ← Athena
![](images/SendGridLog-Flow-ja.png)

**主要コンポーネント：**

- **SendGrid**：イベントデータ（bounces、deliveries、opens など）を生成するメール配信サービス
- **Amazon Cognito**：
  - SendGrid ウェブフックの OAuth 2.0 認証を管理
  - 安全なトークンベースの認証を提供
  - コンポーネント：
    - ユーザープール：ウェブフックアプリケーション認証情報を管理
    - リソースサーバー：ウェブフックアクセス用のカスタムスコープを定義
    - アプリクライアント：SendGrid が OAuth トークンをリクエストするために使用
    - ドメイン：OAuth エンドポイントをホスト
- **API Gateway**：SendGrid からウェブフックイベントを受信
- **Kinesis Firehose**：データを処理し Parquet 形式に変換
- **S3**：ログデータの保存先
- **Athena**：ログ分析用の SQL クエリサービス
- **CloudWatch Alarms**：
  - API Gateway メトリクスを監視：
    - 4XX エラー（認証/検証の失敗）
    - 5XX エラー（システム/統合の失敗）
  - SNS による通知連携

## OAuth 2.0 設定とコスト最適化

### アクセストークン有効期限設定

調査により、SendGrid の Webhook が OAuth 2.0 プロバイダー（Cognito）で設定された expire_in 値に基づいてアクセストークンをキャッシュすることが判明しました。主な発見：

- **トークンキャッシュ動作**：SendGrid はアクセストークンをキャッシュし、Cognito で設定された有効期限を遵守します
- **サーバー分散**：SendGrid は SENDGRID_KEY ごとに約 6 台のサーバーを使用してメール配信を行います
- **上限値**：Cognito の `expires_in` の最大値は 1 日（24 時間）です

### サーバー分散の検証

SendGrid のサーバー分散パターンを確認するため、以下の調査を実施しました：

**検証方法：**

1. Cognito の `expires_in` を 1 日（24 時間）に設定
2. 自動メール送信スクリプトを実行：15 分毎に 1 通のメール（～ 100 通/日）
3. イベントデータと OAuth トークンリクエストを監視

**イベントデータ分析：**
各 SendGrid イベントには `recvd-*` のパターンを含む `sg_message_id` が含まれています。イベントデータの例：
![](images/SendGridLog-EventData.png)

**CloudTrail トークンリクエストの相関関係：**
新しいサーバーが出現した際の TOKEN_POST リクエストを示す CloudTrail ログ：
![](images/SendGridLog-CloudTrailLog.png)

**主な観察事項：**

- `sg_message_id` には `recvd-{server_id}` 形式のサーバー識別子が含まれている
- 新しい OAuth トークンがリクエストされた際に新しいサーバー ID が出現
- CloudTrail ログの TOKEN_POST リクエストが新しいサーバー ID の出現と相関している

**検証クエリ：**
Athena を使用してサーバー ID を抽出し、タイムスタンプと相関させる：

```sql
SELECT
  from_unixtime(timestamp) AT TIME ZONE 'Asia/Bangkok' AS utc_plus_7_time,
  regexp_extract(sg_message_id, '\.recvd-([^-]+)', 1) AS server_id
FROM "apigateway_sendgrid_test"
WHERE date = '2025-05-24'
ORDER BY timestamp;
```

**クエリ結果：**
CloudTrail トークンリクエストと一致するサーバー ID パターンを示す Athena クエリ結果：
![](images/SendGridLog-AthenaQuery1.png)
![](images/SendGridLog-AthenaQuery2.png)

**結果：**

- CloudTrail の TOKEN_POST リクエストがイベントデータの新しいサーバー ID 出現と一致
- SENDGRID_KEY ごとに約 6 つのユニークなサーバー ID の一貫したパターン
- サーバーローテーションが OAuth トークンの有効期限サイクルに従っている

この検証手法と分析結果により、**SendGrid が SENDGRID_KEY あたり約 6 台のサーバーでメール配信を行っている**という仮定を立てることができます。

### Cognito コスト見積もり

**料金（東京リージョン）：**

- 基本料金：クライアントクレデンシャルフローを使用するアプリケーションあたり月額 $6.00
- トークンリクエスト：M2M トークンリクエストあたり $0.00225

**推奨設定（1 日有効期限）：**

- コスト：$6 + $0.00225 × 6 サーバー × 30 日 = **月額 $6.41**

Cognito の expires_in の上限が 1 日であるため、SendGrid がサーバーの数を増やさない限り、Cognito のコストは月額約$6.41 のままです。

### 設定変更の影響

⚠️ **注意**：Cognito での OAuth 設定変更（アクセストークンの有効期限など）は、変更後に発行されたトークンにのみ適用されます。変更前に発行されたトークンは元の有効期限を保持し、自然に期限が切れるまで有効です。

## 実装手順

### 1. SendGrid Webhook の設定

1. SendGrid ダッシュボードにログイン
2. Settings → Mail Settings → Event Webhook に移動
3. ウェブフックを設定：
   - HTTP Post URL：API Gateway エンドポイント
   - 追跡するイベント（bounces、deliveries、opens など）を選択
   - セキュリティのための OAuth2.0 ウェブフックを有効化
     - **クライアント ID**：Cognito App Client 設定から取得
     - **クライアントシークレット**：Cognito App Client 設定から取得
     - **トークン URL**：`https://{あなたのCognitoドメイン}.auth.{リージョン}.amazoncognito.com/oauth2/token`
       ![](images/SendGridLog-OAuth-Webhook.png)

### 2. データ保管

SendGrid からイベントを受信すると、ログは S3 バケットに Parquet 形式で保存されます。

### 3. Athena を使用したクエリ

1. クエリエディター
   ![](images/SendGridLog-Setting-QueryEditor.png)
   クエリ結果の保存先を指定
   ![](images/SendGridLog-QueryResultLoc.png)
2. Athena データベースの選択
   ![](images/SendGridLog-Database.png)
3. サンプルクエリ：
   ```sql
   select * from "dev21blea_xxxx_sendgrid_logs"."apigateway_sendgrid";
   ```
   ![](images/SendGridLog-QueryResult.png)

## 監視とアラート：

### CloudWatch アラーム

1. **API Gateway 4XX エラー**
   - 閾値: 5 分間に 10 件を超えるエラー
   - アクション: SNS 通知
2. **API Gateway 5XX エラー**
   - 閾値: 5 分間に 10 件を超えるエラー
   - アクション: SNS 通知

**4xx**
![](images/SendGridLog-4xx-Alarm.png)
**メッセージ**
![](images/SendGridLog-4xx-Message.png)

**5xx**
![](images/SendGridLog-5xx-Alarm.png)
**メッセージ**
![](images/SendGridLog-5xx-Message.png)

### エラー処理

SendGrid が API Gateway にイベントを送信できない場合（4xx、5xx エラー）：

- SendGrid は 2xx レスポンスを受信するか、最大時間が経過するまで POST リクエストをリトライします
- すべてのイベントは、イベント発生後最大 24 時間まで、増加する間隔でリトライされます
- これは 24 時間のロールイング期間であり、新しい失敗イベントにはそれぞれ 24 時間のリトライ期間が与えられます
- 参考：[SendGrid Event Webhook Documentation](https://www.twilio.com/docs/sendgrid/for-developers/tracking-events/getting-started-event-webhook)

## 設定

### `SendGridLogParams` サンプルコード

```typescript
export const SendGridLogParams: inf.ISendGridLogParam[] = [
  {
    s3BucketArn: '', // 既存のバケットがある場合はARNを入力。ない場合は空白のままにすると、新しいバケットが作成されます。
    suffix: 'xxxx', // 4文字の文字列
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
  {
    s3BucketArn: '', // 既存のバケットがある場合はARNを入力。ない場合は空白のままにすると、新しいバケットが作成されます。
    suffix: 'abcdef', // 4文字の文字列
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
];
```
