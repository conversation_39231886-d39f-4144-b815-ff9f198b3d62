# Overall_Architecture

## テンプレートの全体構成図

![全体構成図](images/overall-architecture.dio.svg)

## テンプレートの概要

- マイナビ社内では複数の内製案件が進行中であり、それらのインフラリソースを迅速に展開するために、AWS の CDK（IaC）という開発フレームワークを利用している。
- 内製案件では ECS を中心としたアーキテクチャを採用することが多いため、[BLEA](https://aws.amazon.com/jp/blogs/news/announcing-baseline-environment-on-aws/)を基に ECS ベースのテンプレートを開発している。
- このテンプレート（以下汎用テンプレート）を各案件毎にカスタマイズして利用することで、迅速に AWS 環境の構築を行うことを目的としている。

**ベースコードのカスタマイズイメージ**

![ベースコードのカスタマイズイメージ](images/Template-Histroy.dio.svg)

## 本ドキュメントの目的

- 汎用テンプレートは、ECS をベースとしたアーキテクチャを想定して作成されているが、様々な要件に対応できるように柔軟性の高いテンプレートとなっている。
- 柔軟性が高い分、初学者にとって学習ハードルが高いため、ドキュメントを作成することで体系的に汎用テンプレートの理解を深めることを目的としている。

### 想定読者

- 汎用テンプレートを利用して、CDK のカスタマイズを実施する人
  ※特に新卒で入られた人やキャリア入社で入られた人。

### ドキュメントの構成

- 汎用テンプレートの利用方法については、本ドキュメントを含めて、全部で３つのパートに分かれている。採番の順番で読むことを推奨する。
- 各ドキュメントで記載されている内容は以下のとおり、
  - 01_Overall_Architecture(当ドキュメント)
    - 汎用テンプレートの目的や機能についての概要
  - 02_HowToUseStacks
    - 各スタックの具体的な利用方法
  - 03_HowToDeploy
    - 汎用テンプレートの具体的な利用方法

## デプロイによって展開されるリソース

- 汎用テンプレートは、複数のスタックによって構成されており、各スタックは複数のコンストラクトによって成り立っている。

**汎用テンプレートのツリー構造イメージ**

![テンプレートのツリー構造](images/cdk-tree.dio.svg)

- 汎用テンプレートで用意しているスタックは以下のとおり。
- 各案件毎にカスタマイズする際は利用するスタックを取捨選択する。

**汎用テンプレートで定義されているスタック一覧**

| スタックファイル名            | 概要                                                                             | デプロイされるリソース                                                                                                                                 |
| ----------------------------- | -------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| share-resources-stack         | VPC や KMS など複数の AWS リソース（ECS や Aurora 等）で共有されるリソースを作成 | VPC, Cognito, ChatBot, KMS, SNS                                                                                                                        |
| pipeline-infraresources-stack | CDK Deploy を自動的に行うパイプラインを作成<br> ※要アップデート                  | CodePipeline, CodeBuild                                                                                                                                |
| oidc-stack                    | GitHub Actions 連携用の Open ID Connect Provider と、連携用の IAM ロールを作成   | Open ID Connect Provider, IAM Role                                                                                                                     |
| cloudfront-stack              | S3 をオリジンとした CloudFront を作成                                            | CloudFront, S3 Bucket                                                                                                                                  |
| waf-cloudfront-stack          | CloudFront にアタッチする WAF を作成                                             | WAF                                                                                                                                                    |
| ecs-app-stack                 | ECS 関連のリソース一式と、ECS コンテナのデプロイを自動化する CI/CD を作成        | ECS 関連リソース (ECS Cluster, ECS Service, Fargate 等)<br> ALB 関連リソース (TargetGroup, Listener 等), CI/CD 関連リソース (CodeBuild, CodeDeploy 等) |
| waf-alb-stack                 | ALB にアタッチする WAF を作成                                                    | WAF                                                                                                                                                    |
| db-aurora-stack               | DB エンジンとして PostgreSQL または MySQL を選択できる Aurora を作成             | Aurora                                                                                                                                                 |
| monitor-stack                 | ダッシュボード                                                                   | CloudWatch Dashboard                                                                                                                                   |
| opensearch-stack              | Provisioned と Serverless を選択できる OpenSearch を作成                         | OpenSearch                                                                                                                                             |
| elasticache-stack             | ElastiCache                                                                      | ElastiCache                                                                                                                                            |
| efs-stack                     | EFS                                                                              | EFS, AWS Backup                                                                                                                                        |
| backup-plan-stack             | 作成した Backup Vault の 設定を行うスタック                                      | AWS Backup Plans                                                                                                                                       |
| backup-vault-stack            | バックアップ用に Backup Vault をデプロイするスタック                             | AWS Backup Vault                                                                                                                                       |

- 汎用テンプレートによってデプロイされるスタック別の構成図は以下のとおり。

**スタック別の全体構成図**

![スタック別の全体構成図](images/overall-detail-architecture.dio.svg)

## 環境切替

- ここでは汎用テンプレートで実装している環境切替について記載する。

### 環境切替の概要

- 汎用テンプレートでは環境毎にパラメータファイルを用意しており、同じスタックでも任意の設定値で上書きすることが可能

- 例えば、VPC の場合、Cidr や NAT Gateway の数をパラメーターから調整することができる。開発環境ではコストの観点で NAT Gateway を１つにして、本番環境では可用性を考慮して２つにするといった柔軟な設定が可能。
- VPC 以外にもインスタンスタイプやドメイン名等、様々なリソース毎のパラメータが用意されている。
- `cdk deploy`コマンドを実行する際に引数として環境名を指定することで、簡単に環境毎の値を設定したスタックをデプロイすることが可能。

**環境ファイルを利用したデプロイのイメージ**

![環境ファイルのデプロイ](images/Environment-Switting-Image.dio.svg)

### 環境の切替方法

- 環境の切替方法は幾つかあるが、大まかに分けると 2 つ存在する。
  1. CDK 独自の `Context` と呼ばれる キーバリュー形式で環境パラメーターを管理
  2. 各プログラミングの拡張子ファイルで環境パラメーターを管理
- 汎用テンプレートでは、`各プログラミングの拡張子ファイル（TypeScript）で環境パラメーターを管理`する方法を採用している。

### TypeScript ファイルを使用する理由

- TypeScript ファイルを利用する理由は大きく分けて２つある。

- 1.コードエディターの補完が使用できる。

  - AWS 公式のライブラリをインポートすることで補完の恩恵を得られる。
  - ライブラリに存在しないプロパティを指定するとコードエディタ―が検知してくれる。
  - 例えば、EC2 インスタンスタイプを環境パラメーターに記述する際に `MEDIUM`・`LARGE` など、指定可能なサイズが補完で表示される  
    ![](images/typescript-image03.jpg)
  - 存在しないサイズを指定するとエラーになる  
    ![](images/typescript-image02.jpg)

- 2.型定義を使用できる。
  - TypeScript の `interface` を使用することで事前に型定義を行うことができる。
    ```typescript
    export interface ISampleParam {
      instanceType: ec2.InstanceType;
      SampleName: string;
    }
    ```
  - 型定義を行うことでコードエディタ―が型チェックを行い、パラメーターに不正な値の入力を防ぐことができる。
  - string に対して number を指定すると IDE 上でエラー検知される  
    ![](images/typescript-image01.jpg)

## ECS サービス/タスク のデプロイ方式

- ここでは汎用テンプレートで用意している ECS のデプロイ方法と、各デプロイパターンで利用される AWS サービスの概要ついて記載する。
- ECS は汎用テンプレートのコアサービスとなるため、本ドキュメントで概要を記載する。詳細な利用方法については`03_HowToUseStacks`配下の`ECS.md`を参照。

### 汎用テンプレートで利用可能なデプロイ方式

- ベースコードでは ローリングデプロイと、Blue/Green デプロイの 2 つのデプロイ方法を用意している。
- それぞれのデプロイ方式の管理・実行方法は以下のとおりである。
  - ローリングデプロイでは、AWS ECS 用のデプロイツールである OSS の ecspresso を利用している。
  - Blue/Green デプロイでは、AWS CodeDeploy を利用している。

### 1. ecspresso を用いた ローリングデプロイ

#### ローリングデプロイとは

アプリケーションの新しいバージョンをデプロイする際に、コンテナ全体を一度に更新するのではなく、徐々に更新することでコンテナ全体を最新のバージョンにデプロイする手法。

![ローリングデプロイとは](images/RollingDeploy-Image.dio.svg)

#### ecspresso とは

- ecspresso は、面白法人カヤック社によって開発された AWS の ECS（Elastic Container Service）専用のデプロイツール
- このツールは、ECS サービスやタスクの管理を簡素化し、効率的なデプロイを実現することが可能。
  参照：[ecspresso 公式ドキュメント](https://github.com/kayac/ecspresso?tab=readme-ov-file#readme)

#### ecspresso の選定理由

- ECS サービス/タスク はアプリチーム側で設定変更を行いたい需要が高いため、CDK（インフラ）管轄ではなく、ecspresso を利用することでアプリチーム側が任意のタイミングで更新をかけられるようにしている。
- また、ecspresso は json ファイルから簡単に ECS サービス/タスクの管理ができるため、アプリチームの方にとっても利便性が高い。

#### デプロイ方法

- S3⇒CodePipeline⇒CodeBuild の構成になっており、S3 に設定ファイルを配置することで、パイプラインが発火する仕組みとなっている。
- 発火後 CodeBuild 内で ecspresso コマンドが実行され、ECS サービス/タスクが作成・更新される。
  ※ECS サービス/タスクは ecspresso 管理となるため、CDK では作成・管理しない。
- ecspresso の設定ファイルは `config.yml`, `ecs-service-def.json`, `ecs-task-def.json` の 3 つ。
  - `ecspresso.yml`: ecspresso の設定ファイル
  - `ecs-service-def.json`: ECS サービスの設定ファイル
  - `ecs-task-def.json`: タスク定義の設定ファイル

#### デプロイイメージ

![ローリングデプロイイメージ](images/ecspresso-image.dio.svg)

### 2. CodeDeploy を用いた Blue/Green デプロイ

#### Blue/Green デプロイとは

稼働中のコンテナ（ブルーと呼ぶ）とは別に、アップデート済みのコンテナ（グリーンと呼ぶ）を作成し、ルーティングを切り替えることで新バージョンをデプロイする手法。

![Blue/Greenデプロイとは](images/BlueGreen-Deploy-Image.dio.svg)

#### デプロイ方法

- S3⇒CodePipeline⇒CodeDeploy の構成になっており、S3 に設定ファイルを配置することで、パイプラインが発火する仕組みとなっている。
- 発火後、設定ファイルに基づき CodeDeploy から Blue/Green が実行される。
- なお、ecspresso とは異なり ECS サービス/タスクは CDK から作成する方針としている。理由として、CodeDeploy 上の設定で ECS サービス/タスクを指定する必要があり、CodeDeploy を CDK で作成しているため、ECS サービス/タスクも CDK から作成している。
- 初回デプロイ以降は下記記載の設定ファイルから、変更を加える方針とする。理由は ecspresso と同様で、アプリチーム側で管理できるようにするため。
- Blue/Green 用設定ファイルは `imageDetail.json`, `taskdef.json`, `appspec.yaml` の３つ。
  - `imageDetail.json`: イメージ uri 設定ファイル
  - `taskdef.json`: タスク定義の設定ファイル
  - `appspec.yaml`: CodeDeploy 用設定ファイル

#### デプロイイメージ

![Blue/Green デプロイイメージ](images/BlueGreen-Image.dio.svg)

### ToDo

- Blue/Green デプロイのデメリットとして、CDK と相性が悪い点がある。  
  参考：https://tech.uzabase.com/entry/2024/07/22/121220
- ECS サービスの更新が CDK では行えないため、CDK のバージョンアップで ECS サービスの更新が入るとスタックの更新ができなくなる恐れがある。

上記の理由から CDK で Blue/Green を扱うにはまだ改修の余地がある。
