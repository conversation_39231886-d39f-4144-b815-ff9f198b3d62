# HowToDeploy

ここでは、スタックをデプロイするための方法について記載する。

## スタックのデプロイ方法

- ここではスタックのデプロイ方法の種類について記載する。

- スタックのデプロイ方法には、以下 3 パターンが存在する。

  1. 全てのスタックをデプロイ

     - 全てのスタックを一式デプロイする際に用いる手法で、環境の初期構築等の際に利用する。
     - 実行するコマンドは`npx cdk deploy --all -c environment=環境名`で`lib/stack`配下の全てのスタックがデプロイされる。

  1. 任意のスタックをデプロイ
     - 全てのスタックではなく任意のスタックをデプロイする際に用いる手法で、特定のスタックに対して修正・変更を加えたい場合に利用する。
     - この場合はデプロイするスタックの名称を直接指定する。スタックの名称は、各スタックの初期化処理の第二引数に記載されている。
     - 例えば、OIDC スタックの場合`${pjPrefix}-OIDC`がスタックの名称となる。  
       **bin/blea-guest-ecsapp-sample.ts**
       ```typescript
       new OidcStack(app, `${pjPrefix}-OIDC`, {
         OrganizationName: config.OidcParam.OrganizationName,
         RepositoryNames: config.OidcParam.RepositoryNames,
         env: getProcEnv(),
       });
       ```
     - `pjPrefix`は環境名＋任意の文字列で構成されている。したがって、環境名が Prod の場合、OIDC スタックの名称は`ProdBLEA-OIDC`となる。この場合、実行するコマンドは`npx cdk deploy ProdBLEA-OIDC -c environment=prod`となる。
       ```typescript
       const pjPrefix = config.Env.envName + 'BLEA';
       ```
  1. パイプラインによる自動デプロイ
     - CodePipeline による自動デプロイ。詳細については docs/02_HowToUseStacks 配下の[PipelineInfraResources.md](./02_HowToUseStacks/PipelineInfraResources.md)を参照

## 環境切替の方法

- ここでは環境切替の仕組みや具体的な方法について記載する。

### 環境切替の概要

- 環境別にパラメータファイルを作成することで、環境別に異なる設定でリソースをデプロイすることが可能。
- デプロイ時に環境ファイルを呼び出すことで、環境の切替を行う。  
  **パラメータファイルを利用した環境切替のイメージ**  
  ![環境切替の概要](./images/Environment-Switting-Image.dio.svg)

### ファイル構成

- 各環境の設定ファイルは`params`配下で管理されており、環境用のファイルとインターフェイス専用のファイルがある。
  ```
    ├── params
      │── dev.ts
      │── stage.ts
      │── prod.ts
      └── interface.ts
  ```

### 環境ファイル詳細

- 各環境ファイルでは、AWS リソース毎のオブジェクトが定義されており、そのオブジェクト内でパラメータを定義している。  
  **例）VPC**
  ```typescript
  export const VpcParam: inf.IVpcParam = {
    cidr: '**********/16',
    maxAzs: 3,
  };
  ```
- パラメータファイルとして外だし管理することで、同じリソースでも環境毎に一意の値を設定できるようにする。
- また、環境ファイルの各オブジェクトには interface.ts 内で定義されているインターフェイスを指定している。これによって、タイピングミスなどのヒューマンエラーを防止する。
- この場合は cidr で`number`を入力すると IDE 上、もしくはコンパイル時にエラーとなる。  
  **例）VPC**
  ```typescript
  expexport interface IVpcParam {
  cidr: string;
  maxAzs: number;
  }
  ```

### 環境の切替方法

- 環境の切替は`params`配下にある環境ファイルを`cdk deploy`コマンド実行時に指定する。
- 実際に使用するコマンドは、`cdk deploy -c environment=環境名` を利用する。
- ユーザがコマンドラインから入力した環境名が`envkey`に格納され、`params`配下にある環境ファイルが config オブジェクトに格納される。
- 例えば、ユーザが`cdk deploy -c environment=stage`と入力した場合、`params/stage.ts`が config オブジェクトに格納される。
  ![環境の切替方法](./images/parameter.switch.dio.svg)

### パラメータのファイル間の流れ

- 先述のとおり、パラメータは`params`配下の環境ファイルに記載されているが、実際には複数のファイルを跨いで最終的に各リソースにパラメータが渡る。

  **ファイル間のパラメータの受け渡し概要図**
  ![パラメータの受け渡し概要図](./images/parameter-flow-overall.dio.svg)

- ファイル間の具体的なパラメータの渡り方については以下のとおり。  
   **例）VPC**

  1.  bin/blea-guest-ecsapp-sample.ts からの読込

      - 環境ファイル内のパラメータが`config`オブジェクトに格納される。

        ```typescript
        const config: IConfig = require('../params/' + envKey);
        ```

      - `VpcParam`パラメータの`cidr`を`myVpcCidr`パラメータとしてスタックファイルに渡す。
        ```typescript
        const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
          //中略
          myVpcCidr: config.VpcParam.cidr,
          myVpcMaxAzs: config.VpcParam.maxAzs,
          //中略
        });
        ```

  2.  lib/stack/share-resources-stack.ts 内での読込

      - blea-guest-ecsapp-sample.ts から受け取った`myVpcCidr`パラメータの型を宣言する。
        ```typescript
        export interface ShareResourcesStackProps extends cdk.StackProps {
          // 中略
          myVpcCidr: string;
          myVpcMaxAzs: number;
          // 中略
        }
        ```
      - ドットノーテーション（`props.`）を使用して、受け取った`myVpcCidr`パラメータをコンストラクトへと渡す。
        ```typescript
        const vpc = new Vpc(this, `${props.pjPrefix}-Vpc`, {
          myVpcCidr: props.myVpcCidr,
          myVpcMaxAzs: props.myVpcMaxAzs,
          //中略
        });
        ```

  3.  lib/construct/vpc-construct.ts 内での読込

      - 前工程と同様に、スタックファイルから受け取ったパラメータを interface で型を宣言する。
        ```typescript
        export interface VpcProps {
          //中略
          myVpcCidr: string;
          myVpcMaxAzs?: number; //中略
        }
        ```
      - ドットノーテーション（`props.`）を使用して、宣言したパラメータをクラスへと渡してリソースを作成する。
        ```typescript
        const myVpc = new ec2.Vpc(this, 'Vpc', {
          ipAddresses: ec2.IpAddresses.cidr(props.myVpcCidr),
          maxAzs: props.myVpcMaxAzs,
          //中略
        });
        ```

      **パラメータの受け渡しフロー図**
      ![パラメータの受け渡しフロー図](./images/parameter.value.flow.dio.svg)

## CDK コードの修正

- デフォルトのコードのままデプロイするとエラーが発生するため、スタックをデプロイ前に修正が必要な箇所について記載する。

  **修正箇所一覧**
  |No.|対象ファイル|内容|
  |-|-|-|
  |1|bin/blea-guest-ecsapp-sample.ts|pjPrefix の設定|
  |1|params/stage.ts</br>params/prod.ts|アカウント ID の設定|
  |2|bin/blea-guest-ecsapp-sample.ts|CloudFront 用 WAF スタックの設定|
  |3|lib/stack/share-resources-stack.ts|接続先（Chatbot・Cognito）の設定|
  |4|lib/stack/pipeline-infraresources-stack.ts|パイプラインの Slack 通知先の設定|

### 修正箇所詳細

1.  pjPrefix の設定

    - スタックや各リソースに共通で付与する接頭語を設定する。
    - 既に同一 AWS アカウント上に同じ`pjPrefix`が設定されたスタックが存在する場合、既存のスタックに対して上書き処理が走る。
    - そのため、DEV 環境などの複数人でデプロイする環境下では、`pjprefix`が重複しないように設定することを推奨する。

      **bin/blea-guest-ecsapp-sample.ts**

      ```typescript
      // Add envName to Stack for avoiding duplication of Stack names.
      const pjPrefix = config.Env.envName + 'BLEA'; // BLEAを任意の文字列に変更
      ```

2.  アカウント ID の設定

    - リソースを作成する環境の AWS アカウント ID を設定する。
      ※DEV 環境は自動でアカウント ID を取得するため、この対応は不要

      **params/prod.ts または stage.ts**

      ```typescript
      export const Env: inf.IEnv = {
        envName: 'Stg',
        account: '************', // 使用する環境のアカウントIDに変更
        region: 'ap-northeast-1',
      };
      ```

[TODO] 以降の操作は、暫定対応としてコメントイン・コメントアウトで対応。将来的にはパラメータ等で制御できるように改修予定

3. CloudFront 用 WAF スタックの設定

   - WafCloudfront スタックを一式コメントアウトする
   - Cloudfront スタック内の`wafCloudfront`を呼び出している箇所をコメントアウトする

     **bin/blea-guest-ecsapp-sample.ts**

     ```typescript
     // const wafCloudfront = new WafCfStack(app, `${pjPrefix}-WafCloudfront`, {
     //   scope: 'CLOUDFRONT',
     //   env: {
     //     account: getProcEnv().account,
     //     region: 'us-east-1',
     //   },
     //   crossRegionReferences: true,
     //   ...config.WafParam,
     //   wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
     //   logRemovalPolicyParam: config.LogRemovalPolicyParam,
     // });

         ～
         省略
         ～

     const cloudfront = new CloudfrontStack(app, `${pjPrefix}-Cloudfront`, {
       pjPrefix: pjPrefix,
       // webAcl: wafCloudfront.webAcl,
       CertificateIdentifier: config.CertificateIdentifier,
       cloudFrontParam: config.CloudFrontParam,
       appAlbs: [ecs.app.frontAlbBg.appAlbBg],
       preSharedKey: wafAlb.preSharedKey,
       logRemovalPolicyParam: config.LogRemovalPolicyParam,
       webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
       env: getProcEnv(),
       crossRegionReferences: true,
       accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
     });
     ```

4. 接続先（Chatbot・Cognito）の設定

   - 実際のチャンネル ID やドメインを設定する必要がある。
   - 検証で使用しない場合には、該当箇所を一式コメントアウトする。

     **lib/stack/share-resources-stack.ts**

     ```typescript
     // const chatbot = new Chatbot(this, `${props.pjPrefix}-Chatbot`, {
     //   topicArn: alarmTopic.topic.topicArn,
     //   workspaceId: props.workspaceId,
     //   channelId: props.channelId,
     // });

     // const cognito = new Cognito(this, `${props.pjPrefix}-Cognito`, {
     //   domainPrefix: props.domainPrefix,
     //   urlForCallback: props.urlForCallback,
     //   urlForLogout: props.urlForLogout,
     // });
     ```

5. パイプラインの Slack 通知先の設定

   - 実際のチャンネル ID を設定する必要がある。
   - 検証で使用しない場合には、該当箇所を一式コメントアウトする。

     **lib/stack/pipeline-infraresources-stack.ts**

     ```typescript
     // // Slack使用時のChatBot作成
     // const target = new chatbot.SlackChannelConfiguration(this, `SlackChannel`, {
     //   slackChannelConfigurationName: props.slackChannelName,
     //   slackWorkspaceId: props.slackWorkspaceId,
     //   slackChannelId: props.slackChannelId,
     // });

     // // Slack向け通知ルール作成
     // buildProject.notifyOnBuildSucceeded('NotifyOnBuildSucceeded', target);
     // buildProject.notifyOnBuildFailed('NotifyOnBuildfailed', target);
     ```

## オプション

### 依存パッケージの最新化

- 最新の CDK を使用する場合は、依存する NPM パッケージをアップデートする必要がある。アップデートの手順は次の通りです。これは BLEA のトップディレクトリで行う。

  ```sh
  # BLEAのトップディレクトリで
  npm update
  ```

  > NOTE
  >
  > ここで依存パッケージのバージョン不整合が発生した場合、適宜 package.json を修正する。例えば、`jest` はこのプロジェクトのテストツールとして使用されているため、 package.json に `devDependencies` として記載されている。`aws-cdk` も同様に `jest` に依存しており、 `ncu -u` によって package.json に記載された `jest` のバージョンが `aws-cdk` が必要とするバージョンと一致しなくなるおそれがある。

---

### デプロイ時の承認をスキップしロールバックさせない

- cdk コマンドにオプションを指定することで、デプロイ時の挙動をコントロールできる。ここではよく利用される便利な設定について記載する。

- See: [https://docs.aws.amazon.com/cdk/latest/guide/cli.html#cli-deploy]

- デプロイ時の承認をスキップする

  通常、CDK によるデプロイを行う場合、承認を求めるプロンプトが表示されるが、cdk デプロイのコマンドラインに `--require-approval never` オプションを指定することで確認のプロンプトが表示されなくなる。（ただし利用には注意すること）

- デプロイに失敗してもロールバックさせない

  CDK は CloudFormation を使ってデプロイするが、通常デプロイ時にエラーが発生すると対象スタックはロールバックされ、デプロイを開始する前の状態にまで戻る。cdk のコマンドラインに`-R`または`--no-rollback`オプションを指定すると、デプロイエラーになったときもロールバックされず、エラーになった時点でデプロイの処理が停止する。エラー内容を修正して再度デプロイすると処理が停止した時点から再開される。設定の試行錯誤を行う場合に便利

- cdk.json に設定する方法

  以下のように`requireApproval`と`rollback`を cdk.json に設定することで、コマンドで都度設定する必要が無くなる。

  ```json
  {
    "app": "npx ts-node --prefer-ts-exts bin/blea-guest-ecsapp-sample.ts",
    "requireApproval": "never",
    "rollback": false,
    "context": {
      "dev": {
  ```

---
