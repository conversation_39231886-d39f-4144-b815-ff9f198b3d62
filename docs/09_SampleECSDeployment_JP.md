# サンプル ECS フロントエンドおよびバックエンドデプロイメントガイド

本ドキュメントでは、インフラストラクチャチーム向けに、ecspresso デプロイメントツールを使用してサンプルの ECS フロントエンドおよびバックエンドアプリケーションをデプロイする手順を詳しく解説します。

## 概要

このデプロイ手順では、ecspresso を用いてコンテナ化されたアプリケーションを AWS ECS にデプロイします。構成は以下のとおりです：

1. **フロントエンドアプリケーション**: nginx によって提供される静的 HTML/JavaScript アプリケーション
2. **バックエンドアプリケーション**: `/backend/docs`にレスポンスを返す Flask ベースの REST API
3. **インフラストラクチャ**: コンテナオーケストレーションを活用した AWS ECS サービス
4. **接続性**: フロントエンドは環境変数`FLASK_APP_URL`を介してバックエンドに接続

**全体構成図**
![](./images/SampleECSDeployment-Overall.png)

## 前提条件

デプロイを開始する前に、以下の準備が整っていることを確認してください：

- **AWS CLI が設定済み** であり、ECS、ECR、SSM に対する適切な権限が付与されていること
- **Docker がインストール済み**であること（コンテナイメージを作成するビルドスクリプトで必要）
- **ECS インフラストラクチャ**が本リポジトリの CDK コードを介してデプロイ済みであること
- **CI/CD パイプライン**（CodePipeline + CodeBuild）がデプロイ済みであること（CDK により自動化されたソースコードデプロイを実現）
- **ワークフローリポジトリ**がローカルで利用可能であること: `csys-infra-baseline-environment-on-aws-change-homemade-workflow`

**注意**: まだインフラをデプロイしていない場合は、まず lib/ ディレクトリから CDK デプロイを実行してください。これにより、ECS インフラおよび CI/CD パイプライン（CodePipeline + CodeBuild）が構築されます。

**重要**: サンプル ECS デプロイメント設定は**dev**環境プレフィックス用に事前設定されています。異なる環境プレフィックス（例：stage、prod）で CDK インフラストラクチャをデプロイする場合は、すべての ECS デプロイメント設定ファイルで対応するプレフィックスを更新する必要があります。
このサンプルの ECS デプロイ構成は、**dev**環境プレフィックスを前提として設定されています。CDKインフラストラクチャを異なる環境プレフィックス（例：stage、prod）でデプロイする場合は、すべてのECSデプロイメント設定ファイルで対応するプレフィックスを更新する必要があります。

## セットアップ後のプロジェクト構造

```
csys-infra-baseline-environment-on-aws-change-homemade/
├── container/
│   ├── sample-frontend/
│   │   ├── build_rolling/           # デプロイ設定（ワークフローリポジトリからコピー）
│   │   ├── Dockerfile               # フロントエンドコンテナ定義
│   │   ├── index.html               # サンプルフロントエンドアプリケーション
│   │   └── nginx.conf               # Nginx設定
│   └── sample-backend/
│       ├── build_rolling/           # デプロイメント設定（ワークフローリポジトリからコピー）
│       ├── Dockerfile               # バックエンドコンテナ定義
│       ├── app.py                   # Flaskバックエンドアプリケーション
│       └── requirements.txt         # Python依存関係
├── lib/                             # CDKインフラストラクチャコード
└── docs/                            # ドキュメント
```

---

## 詳しい手順解説

## ステップ 1: デプロイ設定ファイルのコピー

まずは、ecspresso 用の設定ファイルをワークフロー用リポジトリからメインリポジトリへコピーします。

```bash
# ワークフロー用リポジトリのパスを設定
WORKFLOW_REPO="/path/to/csys-infra-baseline-environment-on-aws-change-homemade-workflow"

# フロントエンドのデプロイ用ファイルをコピー
cp -r $WORKFLOW_REPO/01_Rolling/build_frontend/ ./container/sample-frontend/build_rolling/

# バックエンドのデプロイ用ファイルをコピー
cp -r $WORKFLOW_REPO/01_Rolling/build_backend/ ./container/sample-backend/build_rolling/

# GitHub Actions 用ワークフローファイルをコピー（任意、自動 CI/CD を行う場合）
cp $WORKFLOW_REPO/.github/workflows/ecs-deploy-frontend.yml ./.github/workflows/
cp $WORKFLOW_REPO/.github/workflows/ecs-deploy-backend.yml ./.github/workflows/

# 新しいディレクトリ構成に合わせて、ワークフローファイルのパスを更新
sed -i '' 's|bash ./01_Rolling/build_backend/build.sh|bash ./container/sample-backend/build_rolling/build.sh dev|g' ./.github/workflows/ecs-deploy-backend.yml
sed -i '' 's|bash ./01_Rolling/build_frontend/build.sh|bash ./container/sample-frontend/build_rolling/build.sh dev|g' ./.github/workflows/ecs-deploy-frontend.yml

# ビルドスクリプトに実行権限を付与
chmod +x ./container/sample-frontend/build_rolling/build.sh
chmod +x ./container/sample-backend/build_rolling/build.sh
```

## ステップ 2：フロントエンドとバックエンドの接続設定

フロントエンドはバックエンド API と通信する必要があります。以下のいずれかの方法で接続を構成できます。

### オプション A: SSM パラメータストアを使用

1. **バックエンド URL 用の SSM パラメータを作成**:

   ```bash
   # バックエンドURL用のSSMパラメータを作成
   # CloudMap のサービスディスカバリ形式: <service-name>.<namespace>
   aws ssm put-parameter \
     --name "/ecs/frontend/backend_url" \
     --value "backend.devblea" \
     --type "String" \
     --description "フロントエンドアプリケーション用のバックエンドAPI URL"

   # パラメータが作成されたことを確認
   aws ssm get-parameter --name "/ecs/frontend/backend_url"
   ```

   **バックエンド URL の形式について:**

   - `backend.devblea`は AWS CloudMap サービスディスカバリパターンに従います: `<service-name>.<namespace>`
   - この DNS レコードは、バックエンドサービスがデプロイされた際に ECS サービスディスカバリによって自動的に作成されます
   - この DNS は VPC 内で解決可能で、フロントエンドがバックエンドと内部的に通信できます
   - 参考: [AWS ECS サービスディスカバリドキュメント](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/service-discovery.html)

2. **ECS タスク定義に SSM パラメータ参照を追加**:
   `container/sample-frontend/build_rolling/ecs-task-def.json`ファイルを編集して SSM パラメータ参照を追加します：
   ```json
   "environment": [
     {
       "name": "FLASK_APP_URL",
       "value": "{{ssm `/ecs/frontend/backend_url`}}"
     }
   ]
   ```

### オプション B: 環境ファイル（.env）を使用

ローカルの環境ファイルで構成管理したいチーム向けの代替手法です。

1. **フロントエンドソースに.env ファイルを作成**:

   ```bash
   # sample-frontendディレクトリに.envファイルを作成
   # CloudMapサービスディスカバリDNS名を使用
   echo "FLASK_APP_URL=http://backend.devblea:8080" > ./container/sample-frontend/.env
   ```

2. **Dockerfile を更新して.env ファイルをコピー**:
   ```dockerfile
   # sample-frontend/Dockerfileに追加
   COPY .env /usr/share/nginx/html/.env
   ```

**注意**: このサンプルアプリケーションでは、オプション A（SSM）と B（.env）のどちらでも同様に機能します
デプロイの簡便さを重視するなら A（SSM）がおすすめです（URL の設定が一元化できるため）。一方で、構成をコンテナ内に閉じたい場合は B（.env）を選んでも構いません。

## ステップ 3: アプリケーションのデプロイ

**注意**: 接続が正しく行われるように、必ず先にバックエンドをデプロイし、その後にフロントエンドをデプロイしてください。フロントエンドは、バックエンドの CloudMap サービスディスカバリの DNS レコードが利用可能であることを前提としています。

### バックエンドのデプロイ:

```bash
bash ./container/sample-backend/build_rolling/build.sh dev # dev環境設定を使用
```

### フロントエンドのデプロイ:

```bash
bash ./container/sample-frontend/build_rolling/build.sh dev # dev環境設定を使用
```

## ステップ 4: デプロイの確認

アプリケーションが正しく動作していることを確認します：

```bash
# ECSサービスステータスを確認
aws ecs describe-services --cluster <your-cluster-name> --services <your-service-name>

# フロントエンドをテスト（HTMLが返されるはず）
curl https://your-application-loadbalancer-endpoint/

# バックエンドAPIエンドポイントをテスト
curl https://your-application-loadbalancer-endpoint/backend/docs
```

## よくある問題と対応策：

### 一般的な問題:

1. **ビルドスクリプトの権限が拒否される**:

   ```bash
   chmod +x ./container/*/build_rolling/build.sh
   ```

2. **タスクの起動に失敗する**:

   - タスク定義に問題がないか確認
   - IAM ロールに必要な権限があるか確認
   - ECR に対象のイメージが存在するか確認

3. **Apple Silicon（M1/M2）Mac で Docker ビルドに失敗する**:
   Apple Silicon Mac を使用していて Docker ビルドの問題が発生した場合、`build.sh`のビルドコマンドを更新してください：

   ```bash
   # container/sample-frontend/build_rolling/build.shとcontainer/sample-backend/build_rolling/build.shを編集
   # この行を置き換える:
   # docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG .

   docker buildx build --platform linux/amd64 -t $IMAGE_REPO_NAME:$IMAGE_TAG .
   ```

   これにより、AWS ECS に対応したアーキテクチャ（linux/amd64）でイメージがビルドされます。

4. **テンプレート解決エラーによる Ecspresso デプロイの失敗**:

   ecspresso がテンプレート解決エラーで失敗した場合、`ecs-task-def.json`に未使用またはサンプルのフィールドが残っていないか確認してください：

   ```json
   // 以下のような未設定のサンプルフィールドは削除：
   "string": "{{ ssm `/path/to/string` }}"
   "secrets": [{"name": "FOO", "valueFrom": "{{ secretsmanager_arn `foo` }}:bar::"}]
   ```

   **解決策**: 実際に使用するフィールドのみを記載し、プレースホルダや存在しない SSM/SecretsManager の参照は削除してください。

5. **フロントエンドがバックエンドに接続できない**:

   ```bash
   # SSM パラメータの存在確認
   aws ssm get-parameter --name "/ecs/frontend/backend_url"

   # 外部からのバックエンド接続確認
   curl https://your-backend-loadbalancer-url/backend/docs

   # フロントエンドコンテナから内部バックエンド接続確認
   # まず、サービスでexecuteコマンドが有効になっているかを確認
   aws ecs describe-services --cluster <your-cluster-name> --services DevBLEA-EcsApp-Service --query 'services[0].enableExecuteCommand'

   # enableExecuteCommandがfalseの場合、ecs-service-def.jsonを更新して再デプロイ（推奨）
   # container/sample-frontend/build_rolling/ecs-service-def.jsonを編集:
   # 編集: "enableExecuteCommand": true
   # その後再デプロイ: bash ./01_Rolling/build_frontend/build.sh dev

   # 代替案: CLI経由で一時的に有効化（任意）
   # aws ecs update-service --cluster <your-cluster-name> --service DevBLEA-EcsApp-Service --enable-execute-command --force-new-deployment

   # フロントエンドタスクIDを取得
   TASK_ID=$(aws ecs list-tasks --cluster <your-cluster-name> --service-name DevBLEA-EcsApp-Service --query 'taskArns[0]' --output text | cut -d'/' -f3)

   # フロントエンドコンテナ内からバックエンド接続確認（内部DNS）
   aws ecs execute-command \
     --cluster <your-cluster-name> \
     --task $TASK_ID \
     --container EcsApp \
     --interactive \
     --command "curl http://backend.devblea:8080/backend/docs"
   ```

   注意: 内部通信は CloudMap DNS（`backend.devblea`）を使用し、外部通信は LoadBalancer URL を使用します。

## オプション: GitHub Actions を使用した自動 CI/CD

GitHub ワークフローファイルをコピーした場合、自動デプロイメントを設定できます：

### GitHub OIDC 統合のセットアップ:

**OIDC 用の GitHub 変数を設定**:
GitHubワークフローは、AWSでOIDC認証を使用するように既に設定されています。必要なリポジトリ変数を設定するだけです。

```bash
# 以下の変数を GitHub のリポジトリ設定で追加：
# AWS_ROLE_ARN - GitHub Actions用のIAMロールのARN（例: arn:aws:iam::123456789012:role/GitHubActionsRole）
```

ワークフローの OIDC 設定に基づいて、AWS IAM ロール ARN が認証手順で参照されるロールと一致していることを確認してください。

```yaml
- name: configure aws credentials
  uses: aws-actions/configure-aws-credentials@v4
  with:
    role-to-assume: ${{ vars.AWS_ROLE_ARN }}
    role-session-name: GitHubActions
    aws-region: ap-northeast-1
```

### ワークフローファイル:

- `.github/workflows/ecs-deploy-frontend.yml` - フロントエンドの手動デプロイ（環境選択付き）
- `.github/workflows/ecs-deploy-backend.yml` - バックエンドの手動デプロイ（環境選択付き）

### 手動トリガー:

1. リポジトリの GitHub Actions タブに移動
2. 実行したいワークフローを選択
3. 「Run workflow」ボタンをクリック
4. 実行する環境（dev/stg/prd）を選択して実行

---

**注意**: 本ドキュメントは簡略化されたデプロイ手順を提供しています。簡易的なデプロイ手順を提供しています。詳細な設定や応用的なシナリオ、トラブル対応については、以下の完全版ドキュメントを参照してください： [HowToUseEcspresso.md](https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade-workflow/blob/main/doc/HowToUseEcspresso.md)
