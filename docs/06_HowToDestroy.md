# HowToDestroy

ここでは、スタックをデストロイするための方法について記載する。

## スタックのデストロイ方法

- ここではスタックのデストロイ方法の種類と、各種類のデストロイ方法について記載する。
- スタックのデストロイ方法には、以下３パターンが存在する。  
  ※以下どのパターンを実施場合でも、[ECS サービスの事前対応](#ecs-サービスの事前対応)を完了しておく必要がある。

  1. 全てのスタックをデストロイ

     - 全てのスタックを一式デストロイする際に用いる手法
     - 実行するコマンドは`npx cdk destroy --all -c environment=環境名`で`bin/blea-guest-ecsapp-sample.ts`で設定した`${pjPrefix}-{スタック名}`にマッチする全てのスタックがデストロイされる。

  2. 任意のスタックをデストロイ

     ※ **任意のスタックをデストロイする際は、任意のスタックで作成されるリソースを参照しているスタックも連鎖的にデストロイされる。**

     - 全てのスタックではなく、任意のスタックをデストロイする際に用いる手法で、特定のスタックに対して削除・再作成を行いたい場合に利用する。
     - この場合はデストロイするスタックの名称を直接指定する。スタックの名称は、各スタックの初期化処理の第二引数に記載されている。
     - 例えば、OIDC スタックの場合`${pjPrefix}-OIDC`がスタックの名称となる。  
       **bin/blea-guest-ecsapp-sample.ts**
       ```typescript
       new OidcStack(app, `${pjPrefix}-OIDC`, {
         OrganizationName: config.OidcParam.OrganizationName,
         RepositoryNames: config.OidcParam.RepositoryNames,
         env: getProcEnv(),
       });
       ```
     - `pjPrefix`は環境名＋任意の文字列で構成されている。したがって、環境名が Prod の場合、OIDC スタックの名称は`ProdBLEA-OIDC`となる。  
        この場合、実行するコマンドは`npx cdk destroy ProdBLEA-OIDC -c environment=prod`となる。
       ```typescript
       const pjPrefix = config.Env.envName + 'BLEA';
       ```

  3. サービス終了時に不要スタックをデストロイ
     - サービス終了時に サービス終了画面表示用の CloudFront スタックを残し、利用しない ECS や Aurora などをデストロイする場合に利用する。
     - デストロイ実行前に、CloudFront のオリジンを切り替えて、CloudFront スタックと ECS スタック間の依存関係を解消しておくこと。  
       参照:[CloudFront ディストリビューションのオリジン切替](./02_HowToUseStacks/CloudFront.md#cloudfront-ディストリビューションのオリジン切替について)
     - オリジンの切替が完了したら、`npx cdk destroy {環境名}BLEA-ECS -c environment={環境名}`で ECS とその配下の不要スタックを削除する。  
       ![CloudFront構成図](./02_HowToUseStacks/images/CloudFront-Detail-Architecture.dio.svg)

- デストロイコマンドを実行すると、下記のような確認コマンドが表示されるため、デストロイで問題ない場合には、`y`を入力、実行する。
  ```sh
  Are you sure you want to delete: ProdBLEA-OIDC (y/n)?
  ```

## スタック間の依存関係

- **参照先**のスタックを削除する際は、**参照元**のスタックを全て削除する必要がある。
- cdk コマンドで任意のスタックを指定した場合、任意のスタックで作成されるリソースを参照しているスタックも連鎖的にデストロイされる。  
  ![スタック間の依存関係](./images/Stack-Dependencies.dio.svg)

## 対応一覧

- ここでは各種スタックをデストロイする際に事前に対応が必要な事項について記載する。

### ECS サービスの事前対応

- ECS スタックをデストロイすると、以下エラーにより、スタックをデストロイできない。

  ```sh
  The stack named ProdBLEA-ECS is in a failed state. You may need to delete it from the AWS console : DELETE_FAILED (The following resource(s) failed to delete: [ProdBLEAECSAppProdBLEAECSCommonCluster38F91958]. ): Resource handler returned message: "The specified capacity provider is in use and cannot be removed. (Service: AmazonECS; Status Code: 400; Error Code: ResourceInUseException; Request ID: f90e49ba-3230-4bbf-8a06-8fb402b399a2; Proxy: null)" (RequestToken: a4cbcff0-cd51-e10c-d7ba-da73b85e0eb4, HandlerErrorCode: null)
  ```

- そのため、ECS スタックをデストロイする前にコンソール上で ECS サービスを消す必要がある。手順は以下の通り。
  1. ECS コンソールにアクセス
  1. デプロイした ECS サービスをすべて選択する
  1. 「サービスを削除」ボタンを押下する
  1. 「強制削除」にチェックを入れ、入力フィールドに「削除」と入力し、削除する。

### RemovalPolicy の設定

- S3 バケットや CloudWatch ロググループなどに RemovalPolicy が設定されているため、スタックデストロイ時にリソースを保持するか、削除するか選択が可能
- リソースによって異なる RemovalPolicy を適用できるように、パラメータを 2 つに分けている。

  - `LogRemovalPolicyParam`
    - ログ格納用 S3 バケット
    - CloudWatch ロググループ
  - `OtherRemovalPolicyParam`
    - コンテンツ格納用 S3 バケット
    - パイプライン用 S3 バケット
    - ECR

- stage 環境と prod 環境では、サービス終了後もログを 5 年間保持する必要があるため、RemovalPolicy は`cdk.RemovalPolicy.RETAIN`をデフォルトとしている。  
  **params/stage.ts または prod.ts**

  ```typescript
  export const LogRemovalPolicyParam = inf.RetainRemovalPolicyParam;
  export const OtherRemovalPolicyParam = inf.RetainRemovalPolicyParam;
  ```

- dev 環境ではデストロイ時には作成したリソースは不要となるため、RemovalPolicy は`cdk.RemovalPolicy.DESTROY`をデフォルトとしている。  
  **params/dev.ts**

  ```typescript
  export const LogRemovalPolicyParam = inf.DestroyRemovalPolicyParam;
  export const OtherRemovalPolicyParam = inf.DestroyRemovalPolicyParam;
  ```
