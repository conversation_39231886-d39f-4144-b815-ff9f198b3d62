# Aurora リストア対応手順

Aurora のデータ復元の実行手順を記載する。

# 概要

- Aurora のデータ復元の方法としてバックトラックとポイントインタイムリカバリ（PITR）を使用する。

| サービス  | データ復元方法 | 復元にかかる時間(ダウンタイム) | 追加作業  |
| --- | ---- | ---- | --- |
| バックトラック<br>(※ MySQL のみ) | 戻したい状態に既存クラスターを上書き | 5 分程度 | なし |
| PITR  | 戻したい状態のクラスターを複製 | 30 分程度 | Aurora 接続先変更,CDK Import |

# バックトラックによるデータ復元

- バックトラックを使用することで、迅速にデータを復元ができる。
- 実行するとダウンタイムが発生するため、一時的に Aurora に接続不可となる。

## 手順

- RDS のコンソールから復元したいクラスターを選択し、アクション＞バックトラックを選択する。  
  ![](./images/Aurora-backtrack-action.png)

- どの時点に戻したいか選択する。※24 時間前まで可能
- バイナリログの有効化にチェックを入れて、バックトラックを開始する。  
  ![](./images/Aurora-backtrack-settuing.png)

- データの復元処理によって、約 5 分間ダウンタイムが発生する。

# PITR によるデータ復元

- ポイントインタイムリカバリ（PITR）は、DB インスタンスを特定の時点に復元し、新しい DB インスタンスを作成することができる。
- 障害発生時には PITR を実行し、Aurora インスタンスの復元を行う。再作成された Aurora インスタンスを再度 CDK 管理下とするために CDK Import を実施する。

## 手順

- リストア手順の概要は以下のとおりである。

![](./images/AuroraImport.dio.svg)

- １と２に関しては障害発生時に対応する方針とし、３番以降については障害発生から落ち着いたタイミングで実施する。  
![](./images/AuroraImport-flow.dio.svg)

## 1. ポイントインタイムリカバリ(PITR)実施

- RDS のコンソールから復元したいクラスターを選択し、アクション＞特定時点への復元を選択する。  
  ![](./images/Aurora-PITR.png)

- どの時点に戻したいか選択する。
![](./images/Aurora-time.png)

- 既存のAuroraと同じ設定となるようにタブを開きながら、以下項目の設定を変更する。  
（\*マークのあるものは、後から変更できない）  
  ※記載のない項目はデフォルト値のまま

| 項目 | 設定項目 | 作業 |
| --- | ---- | ----- |
| 設定| \*DB インスタンス識別子 | 一意なインスタンス名を設定する。※既存の Aurora と名前が被らないようにする</br>(例)「prodblea-db」と設定した場合</br>クラスター名：prodblea-db-cluster</br>インスタンス名：prodblea-db</br>![](./images/Aurora-Cluster.png)</br>※PITR ではインスタンスの作成は 1 つのみである。|
| インスタンスの設定 | 容量の範囲 | 初期にデプロイされている Aurora インスタンスの値と同一になるように最小値・最大値を設定する</br>![](./images/Aurora-acu.png)|
| 追加設定  | 最初のデータベース名| 「mydbname」と設定する。|
| 追加設定  | DB パラメータグループ  | デフォルトで「default:aurora-mysql-8-0」が選択されているため、カスタム DB インスタンスパラメータグループを選択する</br>【実際の値の確認方法】</br>Aurora コンソール＞インスタンスを選択＞「設定」タブ＞インスタンス＞設定＞ DB インスタンスパラメータグループ</br>![](./images/Aurora-instancePG.png)|
| 追加設定  | バックアップ  | バックアップ保持期間を入力する。</br>「スナップショットにタグをコピー」にチェックを入れる。|
| 追加設定  | \*バックトラック(MySQL の場合のみ) | 有効化にチェックし、ターゲットバックトラックウィンドウに「24」時間と設定する</br>![](./images/Aurora-backtrack.png)  |
| 追加設定  | \*AWS KMS キー | 以下を選択する</br>STG環境の場合：StgBLEA-AppKey-for-app</br>本番環境の場合：ProdBLEA-AppKey-for-app|
| 追加設定  | ログのエクスポート  | 項目全てにチェックを入れる</br>※ PostgreSQL は `PostgreSQL ログ` のみ。</br>![](./images/Aurora-log-export.png)|
| 追加設定  | メンテナンスウィンドウ | アップデート時間を設定する。※指定しない場合ランダムな時間で実行される。</br>![](./images/Aurora-maintenance-window.png)</br>【実際の値の確認方法】</br>Aurora コンソール＞クラスターを選択＞「メンテナンスとバックアップ」タブ＞メンテナンス＞メンテナンスウィンドウ</br>※UTC と日本時間表記になっていることを注意する</br>![](./images/Aurora-maintenance-window-actual.png) |

## 2. ECS コンテナの接続先切り替え

- ECS コンテナの接続先を PITR で作成した Aurora クラスターに切り替えるために、secrets manager コンソールにアクセスする。

- `ecs-task-def.json`で指定している ARN のシークレットを選択する。

  【対象シークレットの選択方法】

  1. Secret コンソールにアクセスする。
  2. 検索欄に「BLEA-DBAurora」と入力し、検索する。
  3. 表示されたシークレットを選択し、次の操作に進む  
  ※もし複数シークレットが表示された場合には、シークレットの ARN と`ecs-task-def.json`で指定しているシークレット ARN を照合していく。

- 「シークレットの値を取得する」を選択し、シークレットの値を変更するため、「編集する」から値を書き換える
  ![](./images/Aurora-secrets.png)

  - 以下 2 点を変更する。  

  | キー | 値 |
  | --- | --- |
  | dbClusterIdentifier | PITR で作成した Aurora クラスター名 |
  | host | PITR で作成した Aurora クラスターのライターエンドポイント名 |

  ![](./images/Aurora-secrets-edit.png)

- ECS コンテナの接続先を変更するために、CodePipeline を発火して、ecspresso で ECS タスクを切り替える。

## 3. CDK Import 事前準備

- cdk import 事前準備として、下記手順を実行していく。

  ### 3-1. import するリソースの情報を取得する

  - cdk import 時に取り込むリソース名が必要になるため、事前に取得しておく。

  - CloudFormation コンソールにアクセスし、Aurora スタックを選択する。

  - リソースタブを選択し、以下タイプで検索をかけて、物理 ID を取得していく。

    - AWS::RDS::DBClusterParameterGroup
    - AWS::RDS::DBParameterGroup
    - AWS::EC2::SecurityGroup
    - AWS::RDS::DBSubnetGroup
    - AWS::SecretsManager::Secret

    ![](./images/Aurora-cloudformation.png)

  ### 3-2. Monitor スタックをデストロイする

  - スタックの依存関係から Aurora スタックをデストロイするために、事前に Monitor スタックをデストロする必要がある。

  ### 3-3. Aurora スタックをデストロイする

  - Monitor スタックのデストロイが完了したら、Aurora スタックをデストロイしていく。

  - Aurora スタックをデストロイすることで、PITR で指定したリソースを別スタックで定義することが可能となる。

  ### 3-4. Aurora スタックの空ファイルのデプロイする

  - cdk import では 既存の CloudFormation に変更を加える処理のため、事前に対象のスタックを用意しておく必要がある。

  - 同一の CDK ファイルを流用するために、Aurora スタック内の作成コードを一式コメントアウトして、空スタックをデプロイする。

  - deploy コマンドを実行する。  
 `npx cdk deploy <環境名>BLEA-DBAurora -c environment=<環境名>`

## 4. CDK Import

- 事前準備で一式コメントアウトした Aurora スタックのコードと Monitor スタックを一式コメントインして元に戻す。

- PITR で作成した Aurora の設定と合わせるために、コードを修正する。
  ※この修正を忘れると、該当の設定は CDK 側で操作できなくなってしまうため注意する。

  1. Performance Insight を無効化する。  

      **params/環境ファイル**
        ```typescript
        export const AuroraParam: inf.IAuroraParam = {
          ~
          enablePerformanceInsights: false,
          ~
        }
        ```

  2. Aurora スタック内の CloudWarch アラーム箇所をコメントアウトする。

- cdk import コマンドを実行する。  
  `npx cdk import <環境名>BLEA-DBAurora -c environment=<環境名>`

- import するリソースの入力が開始するため、メモと PITR で作成した Aurora を参照しながら入力していく。

    ```
    StgBLEA-DBAurora
    StgBLEA-DBAurora/AuroraPostgresqlCluster/AuroraPostgresqlCluster/AuroraClusterParameterGroup/Resource (AWS::RDS::DBClusterParameterGroup): enter DBClusterParameterGroupName (empty to skip):
    ```

- 事前準備で用意したメモを参照しながら、CDK コードと実リソースを連携していく。
  - 入力するリソースは以下のとおりである。

    - AWS::RDS::DBClusterParameterGroup
    - AWS::RDS::DBParameterGroup
    - AWS::EC2::SecurityGroup （※Security group ID を入力する）
    - AWS::RDS::DBSubnetGroup
    - AWS::SecretsManager::Secret
    
    `※Aurora のクラスター名とインスタンス名については PITR で作成した Aurora の名前を設定する。`

    - AWS::RDS::DBCluster
    - AWS::RDS::DBInstance(instance1)
    ※instance2 はスキップ

  - 上記以外のリソースは何も入力せずに Skip する。

## 5. CDK Deploy

`※留意事項`  
`再デプロイするとPITRで作成されなかったリーダーインスタンスが追加されるため、一時的に Aurora のサービスが停止する。`

- cdk import 時に変更したコードを元に戻す。

  1. Performance Insight を有効化する。  
      **params/環境ファイル**
      ```typescript
      export const AuroraParam: inf.IAuroraParam = {
        ~
        enablePerformanceInsights: true,
        ~
      }
      ```

  2. Aurora スタック内の CloudWarch アラーム箇所をコメントインする。

- Aurora スタックを再デプロイする。

- Monitor スタックをデプロイする。

## 6. ドリフト検出

- CloudFormation コンソールから Aurora スタックを選択し、スタックアクション＞ドリフトの検出を実行する。
- ドリフト検出が完了したら、スタックアクション＞ドリフト結果を表示を選択し、ドリフト検出のステータスを確認する。  
  ![](./images/Aurora-drift-search.png)

- スタックのドリフト検出ステータスが`DRIFTED`だった場合には、リソースを選択し「ドリフトの詳細を表示」からコードの差分を確認する。

- 予定のコードが現在のコードと同一になるように CDK コードを修正し、再デプロイを実施する。

  ※インスタンスの「**タグ**」のドリフト検出は PITR の際に設定できない項目であるため、特段の対応は不要である。
  インスタンスにタグを追加する場合にはコンソールから追加する。