<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="571px" height="312px" viewBox="-0.5 -0.5 571 312" content="&lt;mxfile&gt;&lt;diagram name=&quot;ページ4&quot; id=&quot;DjNUw80YBsA5NYK32GAp&quot;&gt;7VnZbqMwFP0aP7YCm8V+BEpn1yytNOq8jAg4CRoSI8dJmvn6scFOINCm02abRapSc70Ezjm+95gAFE3uX/GkHH9gGS0AtLJ7gK4AhI7jyk8VWNUBF+nAiOdZHbI3gZv8J9VBS0fneUZnrYGCsULkZTuYsumUpqIVSzhny/awISva31omI9oJ3KRJ0Y1+zTMxrqMY+pv4a5qPxvqbXYzqjklixuoHmY2TjC0bIRQDFHHGRN2a3Ee0UNAZWJJv7oc3Pr1NV++FCC+i1bsvi4t6sevfmbJ+Ak6nYr9LO/XSi6SYa7j0s4qVwY9mEk59OWVT+S8ci0khr2zZrIerMVto77hXPW7G5jylj4zTWhMJH9HH1vPXhEgdUzahgq/kPE6LROSL9s0lWlGj9bgNbLKhkfsNFN0Oivblpze3X0DsgiAG5BrEGAQ2wFEHXc7m04yqxS2J5nKcC3pTJhUkS7kd22AP86KIWMF4NRdlLsWZI+MzwdkP2ujBcIA8T/aMeJLlkomrnMu9lbOp7KfJTKyZW1Au6P0zuOtibVYxG9/kC3253Gw+22ypcWPjedaB2PE77MDLOLoBMQLYVvzEHsAxCFwQ+yD0AAkUb1j+YQC9QmE14LI1qlCLqzj2q+kQ4EBNJyEIr/WCatYeSR7ilKZpH8kD7DqudUAmoX9mTOJzz1bkidnKfgD446Qr0oERXabZDzVtUjIu+lTvgDAAuJY/AfhKqV5GiFdFICBkv6ofDmG/6jNv4LneEfOX7Z1a9uaGzlf3tjF2O4UPTyl8c5sNIJ0t5VsHrtknFPZ2Oj8DYcMOH67mI6NlwVZ/NR+Oc3Z8dI8DwZwznjS8UuV6QrdqIBBGukGQ6SLGB0UP1BHpnqzKNBE1XpUPG5BIRYIrEHRTmzx3laopj3QFm4vdFJeU5xIQyjeTPm1CYclmubLCUAnmEroylDXs8ZJW9niQzNTqUElqVh9qkdWYXU12oFJUkZeve9Sl0/DxfZuL2sKCVo+wUI+w8B6E9f2zdZun/O7Ou8OLV/Dj18Xntxc9BSwmymgr3qv9HSLlvqUkpKlQDafa8WuR1PKr7EdwDcJYNaTjJi9MBm1uNGFJkY8UvQUdin5KD0Uc2UoIT8wH6FC0dctlywhKGnD0j1ECT81Jt2TutR6e5zuFLRag02XB6SEBHooEdFgSTnjm30baPzHST3hB+Yfavy2kUU+RPirSXk+yrzI6divr7akS3CzEdbKvIwSbLk/7wAAaZ+jrRhDohl6n4R4Dy5jG0Ex3zBhk1jGeMyC71wn8fdelnc5OBx/JjltV7FDCsp3d2XKtvqNIq/sG9rki+c+pycvwxJz2vIt96c79T+7DpWBf5MrLzS+WVV/jV18U/wI=&lt;/diagram&gt;&lt;diagram name=&quot;ページ3のコピー&quot; id=&quot;-ds0GfamgJ2ht_QGn8LM&quot;&gt;3VjbcqM4EP0aPU4KIcDiETCZzMPspDY7l31UQMZUZEQJObbn61cCKYBxbjueCZWqlNNqdQvp6LS6JYCSzf6jIPX6M88pA66T7wFaAtdFyFG/WnHoFItF0CkKUeadCvaKm/InNUrjV2zLnDYjQ8k5k2U9Vma8qmgmRzoiBN+NzVacjb9ak4JOFDcZYVPt9zKX606L3UWvv6JlsTZf9jHqOjbE2pqFNGuS891AhVKAEsG57KTNPqFMQ2dh2dH8C97Dz3ffYHi4oz7Zia8fusEuX+PysAJBK3neod1u6HvCtgYus1Z5sPjRXMFpmhWv1L94LTdMtaASXzg1s4SGb0VGn7Az2EsiCvrUeH5np+c22GWz8I+Ub6gUB2UgKCOyvB9TgRhGFQ92PWxKMMi9AkU0RTENQRiBMAGpD6IUxAikCxCGII5awQPh5QRpwbdVTvWHHIXsbl1KelOTFq+dCs0x8KuSsYQzLlpftMIZzTKlb6Tgd3TQc4t9z3devVX3VEi6fxJc22sj3RwQvmnu+miDNobWg0gLnN+0Hd7MSO2/kNSLWZHan6AIL64//fO3YbTib4pBBAFO3guR3cXbEflL/JUVf32/zVn1U1wXzrcruPngTJAd8pYLueYFrwhLe208xn6AswJLHH4Yfdv4VzcufNtc7oedy4Nt7Uv5w46h5IGXavVOumF9zhxAZw8M43rNSzW/Rw8yGB5tbBfBxqvf20gIchiY1dqgefw7/hHP8FHifsYcIv9J+/DXzMPRbJTQLa9n8QPg//9sWUzOFvciTW5AigCG+nRJA4BTEPk6WcaBTqXq1MHqDwM3YIpT8a1QUqGltgsDvGjdXYAj7R7GIL40A2qv93FEeWhmudaGzGySLbR3jOeyLUSzSrd23gMg0UWW32m/Ta3O+lPE93Q9ibsICAFeauIrTRi0GlcXnGcl/mrlniZ+HtwGfvAHi0wYvDnz53Z3gi+9PEFvXsyfXp+8I+a/Dw4f15cz4PD0quQb6HNaM354L9B73htCf7K2P/VmgIEqvuKlLYNg+1SwBKo+NHWMZ4Tw0tQ6sRWirgvZLtQ+OXQ202tEsya1FjPCGN/K53eypqJU66aid7ruVXHNm1KWvHLbu4GrLwd5KWimdXo82rSZizR6dFczp+neCZEz8G6dPfd3ZpExC1znBAvQCRbgM7Bgmt+jreCCDIrebrt8u4GJ3dKHnQwtEZJHqgHckiXQiV/Z6yIAto9QAYiWIJomqLkTYcXK+mp2VbiP/hyPVLN/Yu7uX/0zPUr/Aw==&lt;/diagram&gt;&lt;diagram id=&quot;VSJ7gtybkRq23LFmrAGB&quot; name=&quot;ページ3&quot;&gt;3Vhdb5swFP01flwENh/mEQjpqq1StEzq8kjBCawEZ47zwX79bDABQhuxtV3SSpVy7/W9xhyf4+sCkL863LBwndzRmGQAavEBoDGAUNcNXfzISFFFbNuqAkuWxiqpCczS30QFNRXdpjHZdBI5pRlP191gRPOcRLwTCxmj+27agmbdp67DJekFZlGY9aP3acyTKoqh3cQ/k3SZqCebGFUDq7DOVS+yScKY7lshFADkM0p5Za0OPskkdjUsVd3kmdHjuhjJ+ZACMyl2u9ubL3v9fvrz7td0jr9OPqnF7sJsq95XLZYXNQAkFngoN6e5+PESvsqEpwuzSpc5zy5LhTZ0yyJyZi317oZsSfiZPHgET5CO0BXhrBB1jGQhT3fddYRq95fHvAYhYSiQ/gIwvQ9Y4ADHBY4PAhO4AfAQCGzgOMBzS8MAzqQHKqPbPCbyQZoAcZ+knMzWYQnPXuioi/EizTKfZpSVtWiBIxJFIr7hjD6S1sgDNg1TO+7KjjBODuf3pY/jUbmKt0q3pnL3jQj0mtpJSwCW9kbIm1dEVTiQqsYlqQp7gOmj6e33b4qngpUBBq4OsP8B6KlfnJ86OstHynhClzQPs6CJtmEUL86KHxLykVm7c7UDpTM+dLxCed29IoeUtyYR3ryeX9jNFNKpZ3hFYRgDhWG9UBhlqctYWLQS1jTN+aY181QGWpyxu5xB5knHPMmHtvmi/NOGfJKOsHYmXRjV+zWMPAL17yQ1emcCHAX+DAQIYF2eCoEFcABcU7Yuz5KNTZwWWPxhAK1MbKn3wIS1lFY5hAG2y3IIsCvLHQ94EzWhrHr3R8vlWx++otZnDVS4fcnWZ/UBCwx5IcMlab1ANj3ZBifSlpGJvMA9wXCR4EsaD7nkKc57tRwc2WAdDXjjWl96mTwGQvkq2VCGKoe1diBwqyFUD6HyoVWOVi/MeV19LRbwaX3F1oNlWq+jLwSv7GZZ6/0q9GUP1JdzSX3ZPcDQKIofZdlqLW46TymppT9BWzyWvUJEHKuMQCmm98/lK7iG9q/9lyOzM5DM+jMo/x82Oz3EjBM2v3teXsEh2/9yYiqUY7LOaPEBUEZviLJwm2+F1b8DzQdXFPwB&lt;/diagram&gt;&lt;diagram name=&quot;Page2&quot; id=&quot;tTMEFJ9T8fNta-eTUCJM&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;Page1&quot; id=&quot;_CB-omHLXsE8ykfsfXeA&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 140 70 L 173.63 70" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 178.88 70 L 171.88 73.5 L 173.63 70 L 171.88 66.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="20" y="40" width="120" height="60" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 70px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                1.PITR実行
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="74" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    1.PITR実行
                </text>
            </switch>
        </g>
        <rect x="180" y="40" width="120" height="60" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 70px; margin-left: 181px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                2.ECSの接続先
                                <br/>
                                切り替え
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="74" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    2.ECSの接続先...
                </text>
            </switch>
        </g>
        <path d="M 140 180 L 173.63 180" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 178.88 180 L 171.88 183.5 L 173.63 180 L 171.88 176.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="20" y="150" width="120" height="60" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 180px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                3.cdk import
                                <br/>
                                事前準備
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    3.cdk import...
                </text>
            </switch>
        </g>
        <path d="M 300 180 L 343.63 180" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 348.88 180 L 341.88 183.5 L 343.63 180 L 341.88 176.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="180" y="150" width="120" height="60" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 180px; margin-left: 181px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                4.cdk import 実行
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    4.cdk import 実行
                </text>
            </switch>
        </g>
        <rect x="350" y="150" width="120" height="60" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 180px; margin-left: 351px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                5.cdk deploy 実行
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="410" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    5.cdk deploy 実行
                </text>
            </switch>
        </g>
        <path d="M 440 190 L 570 190 L 570 240 L 514.6 240 L 472.5 270 L 494.6 240 L 440 240 Z" fill="none" stroke="#b85450" stroke-miterlimit="10" transform="translate(505,0)scale(-1,1)translate(-505,0)rotate(-180,505,230)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 245px; margin-left: 441px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Auroraのサービスが
                                <br/>
                                一時停止
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="505" y="249" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Auroraのサービスが...
                </text>
            </switch>
        </g>
        <rect x="0" y="0" width="120" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 15px; margin-left: 2px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                障害発生時に対応
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2" y="19" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    障害発生時に対応
                </text>
            </switch>
        </g>
        <rect x="0" y="110" width="120" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 125px; margin-left: 2px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                事後対応
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2" y="129" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    事後対応
                </text>
            </switch>
        </g>
        <rect x="0" y="230" width="40" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <rect x="0" y="260" width="40" height="20" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <rect x="0" y="290" width="40" height="20" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <rect x="50" y="230" width="290" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 240px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                必要に応じてアプリチームかインフラチームで対応
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="244" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    必要に応じてアプリチームかインフラチームで対応
                </text>
            </switch>
        </g>
        <rect x="50" y="262" width="290" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 272px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                アプリチーム対応
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="276" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    アプリチーム対応
                </text>
            </switch>
        </g>
        <rect x="50" y="290" width="290" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 300px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                インフラチーム対応
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="304" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    インフラチーム対応
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>