# Sample ECS Frontend and Backend Deployment Guide

This guide provides step-by-step instructions for infrastructure teams to deploy sample ECS frontend and backend applications using ecspresso deployment tools.

## Overview

This deployment process uses **Ecspresso** (ECS deployment tool) to deploy containerized applications to AWS ECS. The deployment consists of:

1. **Frontend Application**: Static HTML/JS application served via nginx
2. **Backend Application**: Flask-based REST API responding on `/backend/docs`
3. **Infrastructure**: AWS ECS services with container orchestration
4. **Connectivity**: Frontend connects to backend via environment variable `FLASK_APP_URL`

**Overall configuration diagram**
![](./images/SampleECSDeployment-Overall.png)

## Prerequisites

Before starting the deployment, ensure you have:

- **AWS CLI configured** with appropriate permissions for ECS, ECR, and SSM
- **Docker installed** (required for build scripts to create container images)
- **ECS infrastructure** deployed via the CDK code in this repository
- **CI/CD pipeline** (CodePipeline + CodeBuild) deployed via CDK for automated source code deployment
- **Workflow repository** available locally: `csys-infra-baseline-environment-on-aws-change-homemade-workflow`

**Note**: If you haven't deployed the infrastructure yet, run the CDK deployment from the `lib/` directory first. This will create both the ECS infrastructure and the CI/CD pipeline (CodePipeline + CodeBuild) for automated deployments.

**Important**: The sample ECS deployment configurations are pre-configured for the **dev** environment prefix. If you deploy your CDK infrastructure with a different environment prefix (e.g., stage, prod), you must update the corresponding prefixes in all ECS deployment configuration files

## Project Structure After Setup

```
csys-infra-baseline-environment-on-aws-change-homemade/
├── container/
│   ├── sample-frontend/
│   │   ├── build_rolling/           # Deployment config (copied from workflow repo)
│   │   ├── Dockerfile               # Frontend container definition
│   │   ├── index.html               # Sample frontend application
│   │   └── nginx.conf               # Nginx configuration
│   └── sample-backend/
│       ├── build_rolling/           # Deployment config (copied from workflow repo)
│       ├── Dockerfile               # Backend container definition
│       ├── app.py                   # Flask backend application
│       └── requirements.txt         # Python dependencies
├── lib/                             # CDK infrastructure code
└── docs/                            # Documentation
```

---

## Detailed Step-by-Step Guide

## Step 1: Copy Deployment Configuration Files

Copy the ecspresso configuration files from the workflow repository to your main repository:

```bash
# Set the path to your workflow repository
WORKFLOW_REPO="/path/to/csys-infra-baseline-environment-on-aws-change-homemade-workflow"

# Copy frontend deployment files
cp -r $WORKFLOW_REPO/01_Rolling/build_frontend/ ./container/sample-frontend/build_rolling/

# Copy backend deployment files
cp -r $WORKFLOW_REPO/01_Rolling/build_backend/ ./container/sample-backend/build_rolling/

# Copy GitHub workflow files (optional, for automated CI/CD)
cp $WORKFLOW_REPO/.github/workflows/ecs-deploy-frontend.yml ./.github/workflows/
cp $WORKFLOW_REPO/.github/workflows/ecs-deploy-backend.yml ./.github/workflows/

# Update workflow file paths to match new directory structure
sed -i '' 's|bash ./01_Rolling/build_backend/build.sh|bash ./container/sample-backend/build_rolling/build.sh dev|g' ./.github/workflows/ecs-deploy-backend.yml
sed -i '' 's|bash ./01_Rolling/build_frontend/build.sh|bash ./container/sample-frontend/build_rolling/build.sh dev|g' ./.github/workflows/ecs-deploy-frontend.yml

# Make build scripts executable
chmod +x ./container/sample-frontend/build_rolling/build.sh
chmod +x ./container/sample-backend/build_rolling/build.sh
```

## Step 2: Configure Frontend-Backend Connectivity

The frontend needs to communicate with the backend API. You have two options:

### Option A: Use SSM Parameter Store

1. **Create SSM parameter for backend URL**:

   ```bash
   # Create SSM parameter for backend URL
   # Using CloudMap service discovery format: <service-name>.<namespace>
   aws ssm put-parameter \
     --name "/ecs/frontend/backend_url" \
     --value "backend.devblea" \
     --type "String" \
     --description "Backend API URL for frontend application"

   # Verify the parameter was created
   aws ssm get-parameter --name "/ecs/frontend/backend_url"
   ```

   **About the backend URL format:**

   - `backend.devblea` follows AWS CloudMap service discovery pattern: `<service-name>.<namespace>`
   - This DNS record is automatically created by ECS Service Discovery when the backend service is deployed
   - The DNS is resolvable within your VPC, allowing frontend to communicate with backend internally
   - Reference: [AWS ECS Service Discovery Documentation](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/service-discovery.html)

2. **Add SSM parameter reference to ECS task definition**:
   Edit the `container/sample-frontend/build_rolling/ecs-task-def.json` file and add the SSM parameter reference:
   ```json
   "environment": [
     {
       "name": "FLASK_APP_URL",
       "value": "{{ssm `/ecs/frontend/backend_url`}}"
     }
   ]
   ```

### Option B: Use Environment File (.env)

Alternative approach for teams who prefer local environment files:

1. **Create .env file in frontend source**:

   ```bash
   # Create .env file in sample-frontend directory
   # Using CloudMap service discovery DNS name
   echo "FLASK_APP_URL=http://backend.devblea:8080" > ./container/sample-frontend/.env
   ```

2. **Update Dockerfile to copy .env file**:
   ```dockerfile
   # Add to sample-frontend/Dockerfile
   COPY .env /usr/share/nginx/html/.env
   ```

**Note**: For this sample application, both options work equally well. Choose based on your preference - Option A (SSM) is simpler for deployment as it centralizes the backend URL configuration, while Option B (.env) keeps configuration local to the container.

## Step 3: Deploy Applications

**Note**: Always deploy the backend first, then the frontend to ensure proper connectivity. The frontend depends on the backend's CloudMap service discovery DNS record being available.

### Deploy Backend:

```bash
bash ./container/sample-backend/build_rolling/build.sh dev # Uses dev environment configuration
```

### Deploy Frontend:

```bash
bash ./container/sample-frontend/build_rolling/build.sh dev # Uses dev environment configuration
```

## Step 4: Verify Deployment

Check that your applications are running correctly:

```bash
# Check ECS service status
aws ecs describe-services --cluster <your-cluster-name> --services <your-service-name>

# Test frontend (should return HTML)
curl https://your-application-loadbalancer-endpoint/

# Test backend API endpoint
curl https://your-application-loadbalancer-endpoint/backend/docs
```

## Troubleshooting

### Common Issues:

1. **Build script permission denied**:

   ```bash
   chmod +x ./container/*/build_rolling/build.sh
   ```

2. **Task fails to start**:

   - Check task definition configuration
   - Verify IAM role permissions
   - Check ECR image availability

3. **Docker build fails on Apple Silicon (M1/M2) Macs**:
   If you're using an Apple Silicon Mac and encounter Docker build issues, update the build command in `build.sh`:

   ```bash
   # Edit container/sample-frontend/build_rolling/build.sh and container/sample-backend/build_rolling/build.sh
   # Replace this line:
   # docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG .

   docker buildx build --platform linux/amd64 -t $IMAGE_REPO_NAME:$IMAGE_TAG .
   ```

   This ensures the Docker image is built for the correct architecture (linux/amd64) that AWS ECS expects.

4. **Ecspresso deployment fails due to template resolution errors**:

   If ecspresso fails with template resolution errors, ensure `ecs-task-def.json` doesn't contain example/unused fields:

   ```json
   // Remove these example fields if they exist and are not configured:
   "string": "{{ ssm `/path/to/string` }}"
   "secrets": [{"name": "FOO", "valueFrom": "{{ secretsmanager_arn `foo` }}:bar::"}]
   ```

   **Solution**: Only include fields that are actually configured for your application. Remove any placeholder/example template fields that reference non-existent SSM parameters or secrets.

5. **Frontend cannot connect to backend**:

   ```bash
   # Check SSM parameter exists
   aws ssm get-parameter --name "/ecs/frontend/backend_url"

   # Test external backend connectivity (from your machine)
   curl https://your-backend-loadbalancer-url/backend/docs

   # Test internal backend connectivity from frontend container
   # First, check if execute command is enabled for the service
   aws ecs describe-services --cluster <your-cluster-name> --services DevBLEA-EcsApp-Service --query 'services[0].enableExecuteCommand'

   # If enableExecuteCommand is false, update ecs-service-def.json and redeploy (recommended)
   # Edit container/sample-frontend/build_rolling/ecs-service-def.json:
   # Edit: "enableExecuteCommand": true
   # Then redeploy: bash ./01_Rolling/build_frontend/build.sh dev

   # Alternative: Enable it temporarily via CLI (optional)
   # aws ecs update-service --cluster <your-cluster-name> --service DevBLEA-EcsApp-Service --enable-execute-command --force-new-deployment

   # Get frontend task ID
   TASK_ID=$(aws ecs list-tasks --cluster <your-cluster-name> --service-name DevBLEA-EcsApp-Service --query 'taskArns[0]' --output text | cut -d'/' -f3)

   # Test backend connectivity from within frontend container (internal DNS)
   aws ecs execute-command \
     --cluster <your-cluster-name> \
     --task $TASK_ID \
     --container EcsApp \
     --interactive \
     --command "curl http://backend.devblea:8080/backend/docs"
   ```

   Note: Internal communication uses CloudMap DNS (`backend.devblea`), while external uses LoadBalancer URL.

## Optional: Automated CI/CD with GitHub Actions

If you copied the GitHub workflow files, you can set up automated deployment:

### Setup GitHub OIDC Integration:

**Configure GitHub Variables for OIDC**:
The GitHub workflows are already configured to use OIDC authentication with AWS. You just need to set up the required repository variables:

```bash
# In your GitHub repository settings, add these variables:
# AWS_ROLE_ARN - ARN of the IAM role for GitHub Actions (e.g., arn:aws:iam::123456789012:role/GitHubActionsRole)
```

Based on the OIDC configuration in the workflow, ensure your AWS IAM role ARN matches the role referenced in the authentication step:

```yaml
- name: configure aws credentials
  uses: aws-actions/configure-aws-credentials@v4
  with:
    role-to-assume: ${{ vars.AWS_ROLE_ARN }}
    role-session-name: GitHubActions
    aws-region: ap-northeast-1
```

### Workflow Files:

- `.github/workflows/ecs-deploy-frontend.yml` - Manual deploy frontend with environment selection
- `.github/workflows/ecs-deploy-backend.yml` - Manual deploy backend with environment selection

### Manual Trigger:

1. Go to GitHub Actions tab in your repository
2. Select the appropriate workflow
3. Click "Run workflow"
4. Choose your environment (dev/stg/prd)

---

**Note**: This guide provides a simplified deployment process. For detailed configuration options, advanced deployment scenarios, and comprehensive troubleshooting, refer to the complete documentation: [HowToUseEcspresso.md](https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade-workflow/blob/main/doc/HowToUseEcspresso.md)
