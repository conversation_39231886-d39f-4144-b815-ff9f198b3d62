import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { OpenSearch } from '../construct/opensearch-constructs/opensearch-construct';
import { OpenSearchServerless } from '../construct/opensearch-constructs/opensearch-serverless-construct';
import { IOpenSearchParam, IOpenSearchTypeParam } from 'params/interface';
// IAMロールで許可する場合など、IAMリソースを使う場合はコメントイン
// import * as iam from 'aws-cdk-lib/aws-iam';

export interface OpenSearchStackProps extends cdk.StackProps {
  vpc: ec2.Vpc;
  appServerSecurityGroup?: ec2.SecurityGroup;
  bastionSecurityGroup?: ec2.SecurityGroup;
  openSearchType: IOpenSearchTypeParam;
  openSearchProps: IOpenSearchParam;
}

export class OpenSearchStack extends cdk.Stack {
  constructor(scope: cdk.App, id: string, props: OpenSearchStackProps) {
    super(scope, id, props);

    // OpenSearchドメインに設定するセキュリティグループ、アウトバウンドルールのみ設定
    // インバウンドルールはメソッドを使用して追加する
    // 本コードのサンプルでは、以下2パターンのサンプルを用意しているため、必要に応じてコメントインして使用する
    // 1. 特定セキュリティグループＩＤからの許可：特定のコンテナリソースなどアクセス元リソースが絞れる場合
    // 2. サブネットの指定：特定リソースの絞り込みが難しく、サブネット全体で指定したい場合
    const domainsg = new ec2.SecurityGroup(this, 'domainsg', {
      vpc: props.vpc,
      allowAllOutbound: true,
    });

    // 1. 特定セキュリティグループＩＤからの許可 を使用する場合はこちらをコメントイン、 許可するSecurityGroupはpropsなど別スタックやIDで指定する
    // 例:ecs のsecurity group をインバウンドルールに追加

    // Allow app access
    if (props.appServerSecurityGroup) {
      domainsg.connections.allowFrom(props.appServerSecurityGroup, ec2.Port.tcp(443));
    }
    // For Bastion Container
    if (props.bastionSecurityGroup) {
      domainsg.connections.allowFrom(props.bastionSecurityGroup, ec2.Port.tcp(443));
    }

    // 「2. サブネットの指定」を使用する場合はこちらをコメントイン
    // private subnetのCIDR内からのアクセスをすべて許可するインバウンドルール追加
    // private subnet内に多くのサービスがあり、個別の設定を受け付けるのが難しい場合使用
    //  props.myVpc.selectSubnets({ subnets: props.myVpc.privateSubnets }).subnets.forEach((x:ec2.ISubnet) => {
    //     domainsg.addIngressRule(ec2.Peer.ipv4(x.ipv4CidrBlock), ec2.Port.tcp(443));
    //   });

    if (props.openSearchType.openSearchType === 'SERVERLESS') {
      // Create opensearch serverless mode
      new OpenSearchServerless(this, 'OpenSearchServerless', {
        ...props.openSearchProps.openSearchServerlessParam,
        vpc: props.vpc,
        domainSecurityGroup: domainsg,
      });
    } else {
      // Create opensearch provision mode
      new OpenSearch(this, 'OpenSearch', {
        ...props.openSearchProps.openSearchProvisionedParam,
        vpc: props.vpc,
        domainSecurityGroup: domainsg,
      });
    }
  }
}
