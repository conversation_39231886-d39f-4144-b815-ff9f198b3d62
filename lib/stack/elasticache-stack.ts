import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as cwlog from 'aws-cdk-lib/aws-logs';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { IElastiCacheTypeParam, IElastiCacheParam } from 'params/interface';
import { ElastiCache } from '../construct/elasticache-contructs/elasticache-contruct';
import { ElastiCacheServerless } from '../construct/elasticache-contructs/elasticache-serverless-contruct';

export interface ModuleStackProps extends cdk.StackProps {
  pjPrefix: string;
  myVpc: ec2.Vpc;
  appKey: kms.IKey;
  alarmTopic: sns.Topic;
  appServerSecurityGroup: ec2.SecurityGroup;
  bastionSecurityGroup?: ec2.SecurityGroup;
  ElastiCacheParam: IElastiCacheParam;
  logGroupRemovalPolicy?: cdk.RemovalPolicy;
  elastiCacheType: IElastiCacheTypeParam;
}

export class ElastiCacheStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: ModuleStackProps) {
    super(scope, id, props);

    const secret = new secretsmanager.CfnSecret(this, 'ElastiCacheSecret', {});

    const logGroup = new cwlog.LogGroup(this, 'ElastiCacheLoggroup', {
      retention: cwlog.RetentionDays.THREE_MONTHS,
      removalPolicy: props.logGroupRemovalPolicy ?? cdk.RemovalPolicy.RETAIN,
    });

    const securityGroupForElastiCache = new ec2.SecurityGroup(this, 'ElastiCacheSecuritygGroup', {
      vpc: props.myVpc,
    });
    // 1. 特定セキュリティグループＩＤからの許可 を使用する場合はこちらをコメントイン。
    // 例:ecs のsecurity group をインバウンドルールに追加
    // securityGroupForElastiCache.connections.allowFrom(props.appServerSecurityGroup,ec2.Port.tcp(6379));
    // For Bastion Container
    if (props.bastionSecurityGroup) {
      securityGroupForElastiCache.connections.allowFrom(props.bastionSecurityGroup, ec2.Port.tcp(6379));
    }

    // 「2. サブネットの指定」を使用する場合はこちらをコメントイン
    // private subnetのCIDR内からのアクセスをすべて許可するインバウンドルール追加
    // private subnet内に多くのサービスがあり、個別の設定を受け付けるのが難しい場合使用
    // props.myVpc.selectSubnets({ subnets: props.myVpc.privateSubnets }).subnets.forEach((x:ec2.ISubnet) => {
    //   securityGroupForElastiCache.addIngressRule(ec2.Peer.ipv4(x.ipv4CidrBlock), ec2.Port.tcp(6379));
    // });

    const subnetgroup = new elasticache.CfnSubnetGroup(this, 'ElastiCacheSubnetGroup', {
      cacheSubnetGroupName: cdk.Stack.of(this).stackName + '-Subnetgroup',
      description: 'for elasticache',
      subnetIds: props.myVpc.isolatedSubnets.map(({ subnetId }) => subnetId),
    });

    if (props.elastiCacheType.elastiCacheType === 'SERVERLESS') {
      // Create elasticache serverless mode
      new ElastiCacheServerless(this, 'ElastiCacheServerless', {
        pjPrefix: props.pjPrefix,
        myVpc: props.myVpc,
        appKey: props.appKey,
        ElastiCacheParam: props.ElastiCacheParam.ElastiCacheServerlessParam,
        securityGroup: securityGroupForElastiCache,
      });
    } else {
      // Create elasticache provision mode
      new ElastiCache(this, 'ElastiCache', {
        pjPrefix: props.pjPrefix,
        myVpc: props.myVpc,
        appKey: props.appKey,
        alarmTopic: props.alarmTopic,
        ElastiCacheParam: props.ElastiCacheParam.ElastiCacheSelfDesignedParam,
        secret: secret,
        subnetgroup: subnetgroup,
        logGroup: logGroup,
        securityGroup: securityGroupForElastiCache,
      });
    }
  }
}
