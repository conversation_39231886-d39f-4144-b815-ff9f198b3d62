import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as kms from 'aws-cdk-lib/aws-kms';
import { IRemovalPolicyParam } from '../../params/interface';

export interface ShareResourcesStackProps extends cdk.StackProps {
  pjPrefix: string;
  removalPolicyParam?: IRemovalPolicyParam;
  kmsPendingWindow?: cdk.Duration;
}

export class KMSStack extends cdk.Stack {
  public readonly kmsKey: kms.Key;

  constructor(scope: Construct, id: string, props: ShareResourcesStackProps) {
    super(scope, id, props);
    const kmsKey = new kms.Key(this, 'Key', {
      enableKeyRotation: true,
      description: 'Custom KMS key',
      alias: `${id}-for-app`,
      removalPolicy: props.removalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      pendingWindow: props.kmsPendingWindow ?? cdk.Duration.days(7),
    });

    this.kmsKey = kmsKey;
  }
}
