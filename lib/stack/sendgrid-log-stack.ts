import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Topic } from 'aws-cdk-lib/aws-sns';
import { Construct } from 'constructs';
import { ISendGridLogParam } from '../../params/interface';
import { SendGridLogContruct } from '../construct/sendgrid-log-construct';

interface ISendGridLogStackProps extends cdk.StackProps {
  readonly pjPrefix: string;
  readonly sendGridLogParams?: ISendGridLogParam[];
  readonly logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  readonly lifecycleRules?: s3.LifecycleRule[];
}

export class SendGridLogStack extends cdk.Stack {
  public readonly alarmTopic: Topic;
  constructor(scope: Construct, id: string, props: ISendGridLogStackProps) {
    super(scope, id, props);
    if (props.sendGridLogParams) {
      for (const value of props.sendGridLogParams) {
        new SendGridLogContruct(this, `${props.pjPrefix}-SendGridLog-${value.suffix}`, {
          pjPrefix: props.pjPrefix,
          createAlarm: value.createAlarm,
          notifyEmail: value.notifyEmail,
          s3BucketArn: value.s3BucketArn,
          logRemovalPolicyParam: props.logRemovalPolicyParam,
          lifecycleRules: props.lifecycleRules,
          suffix: value.suffix,
        });
      }
    }
  }
}
