import * as cdk from 'aws-cdk-lib';
import { Code, Function, Runtime } from 'aws-cdk-lib/aws-lambda';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import { Provider } from 'aws-cdk-lib/custom-resources';
import { Construct } from 'constructs';

interface SetPolicyConstructProps {
  properties: Record<string, string>;
  lambdaHandler?: string;
}

/**
 * A construct that creates a custom AWS resource using a Lambda function.
 * This construct sets up a Lambda function, an IAM role for the function,
 * and a custom resource provider to handle events.
 *
 * @class CustomResourceConstruct
 *
 * @extends {Construct}
 */
export class CustomResourceConstruct extends Construct {
  /**
   * The Lambda function that handles the custom resource events.
   *
   * @type {Function}
   * @memberof CustomResourceConstruct
   */
  readonly onEventHandlerFunction: cdk.aws_lambda.Function;

  /**
   * The custom resource that is created by this construct.
   *
   * @type {cdk.CustomResource}
   * @memberof CustomResourceConstruct
   */
  readonly customResource: cdk.CustomResource;
  constructor(scope: Construct, id: string, props: SetPolicyConstructProps) {
    super(scope, id);

    const lambdaRole = new cdk.aws_iam.Role(this, 'CustomResourceRole', {
      assumedBy: new cdk.aws_iam.ServicePrincipal('lambda.amazonaws.com'),
      managedPolicies: [cdk.aws_iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
    });

    this.onEventHandlerFunction = new Function(this, 'Function', {
      runtime: Runtime.PYTHON_3_12,
      handler: props.lambdaHandler ?? 'stackPolicy.on_event',
      code: Code.fromAsset('lambda'),
      timeout: cdk.Duration.seconds(900),
      memorySize: 256,
      role: lambdaRole,
    });

    const customResourceProvider = new Provider(this, 'CustomResourceProvider', {
      onEventHandler: this.onEventHandlerFunction,
      logRetention: RetentionDays.ONE_DAY,
    });

    this.customResource = new cdk.CustomResource(this, 'CustomResource', {
      serviceToken: customResourceProvider.serviceToken,
      properties: props.properties,
    });
  }
}
