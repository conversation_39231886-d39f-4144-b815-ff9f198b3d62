import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { aws_events as cwe } from 'aws-cdk-lib';
import { DataFirehose } from '../data-firehose-construct';

export interface EventsToDataFirehoseProps {
  firehoseStreamName: string;
  firehoseLogGroupName: string;
  httpEndpointUrl: string;
  httpEndpointName: string;
  s3BackupMode: 'AllData' | 'FailedDataOnly';
  secretArn: string;
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  firehoseBucketLifecycleRules: s3.LifecycleRule[];
  eventSource: string[];
  detailType: string[];
  eventTargetArn: string[];
}

/**
 * Constructs an `EventsToDataFirehose` which sets up an EventBridge rule to send events
 * to a Kinesis Data Firehose delivery stream.
 *
 * @class
 * @extends {Construct}
 *
 * @param {Construct} scope - The scope in which this construct is defined.
 * @param {string} id - The unique identifier for this construct.
 * @param {EventsToDataFirehoseProps} props - The properties required to configure the construct.
 *
 * @property {DataFirehose} eventLogFirehose - The Kinesis Data Firehose delivery stream used for logging events.
 * @property {iam.Role} eventBridgeRole - The IAM role assumed by EventBridge to write to the Firehose stream.
 *
 * @remarks
 * - The `eventLogFirehose` is configured with the provided properties such as stream name, log group name,
 *   HTTP endpoint details, S3 backup mode, and lifecycle rules.
 * - The `eventBridgeRole` is granted permissions to put records into the Firehose stream.
 * - A CloudWatch EventBridge rule is created to route events matching the specified pattern to the Firehose stream.
 *
 * @example
 * new EventsToDataFirehose(this, 'MyEventsToDataFirehose', {
 *   firehoseStreamName: 'my-stream',
 *   firehoseLogGroupName: 'my-log-group',
 *   httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
 *   httpEndpointName: 'example-endpoint',
 *   s3BackupMode: 'Enabled',
 *   secretArn: 'arn:aws:secretsmanager:region:account-id:secret:my-secret',
 *   logRemovalPolicyParam: RemovalPolicy.DESTROY,
 *   firehoseBucketLifecycleRules: [{ expiration: Duration.days(30) }],
 *   eventSource: ['aws.elasticache'],
 *   detailType: ['Elasticache Event'],
 *   eventTargetArn: 'arn:aws:elasticache:region:account-id:cluster:my-cluster',
 * });
 */
export class EventsToDataFirehose extends Construct {
  /**
   * Constructs an EventsToKinesisDataFirehoseConstruct that sets up an EventBridge rule
   * to send events to a Kinesis Data Firehose delivery stream.
   *
   * @param scope - The scope in which this construct is defined.
   * @param id - The unique identifier for this construct.
   * @param props - The properties required to configure the construct.
   *
   * @property props.firehoseStreamName - The name of the Kinesis Data Firehose stream.
   * @property props.firehoseLogGroupName - The name of the CloudWatch log group for the Firehose.
   * @property props.httpEndpointUrl - The HTTP endpoint URL for the Firehose delivery stream.
   * @property props.httpEndpointName - The name of the HTTP endpoint.
   * @property props.s3BackupMode - The S3 backup mode for the Firehose.
   * @property props.secretArn - The ARN of the secret used for authentication.
   * @property props.logRemovalPolicyParam - The removal policy for the log group.
   * @property props.firehoseBucketLifecycleRules - The lifecycle rules for the S3 bucket used by the Firehose.
   * @property props.eventSource - The source of the events for the EventBridge rule.
   * @property props.detailType - The detail type of the events for the EventBridge rule.
   * @property props.eventTargetArn - The ARN of the event source to filter events.
   */
  constructor(scope: Construct, id: string, props: EventsToDataFirehoseProps) {
    super(scope, id);

    const eventLogFirehose = new DataFirehose(this, 'Firehose', {
      firehoseStreamName: props.firehoseStreamName,
      firehoseLogGroupName: props.firehoseLogGroupName,
      httpEndpointUrl: props.httpEndpointUrl,
      httpEndpointName: props.httpEndpointName,
      s3BackupMode: props.s3BackupMode,
      secretArn: props.secretArn,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      firehoseBucketLifecycleRules: props.firehoseBucketLifecycleRules,
    });

    const eventBridgeRole = new iam.Role(this, 'EventBridgeFirehoseRole', {
      assumedBy: new iam.ServicePrincipal('events.amazonaws.com'),
    });

    eventBridgeRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['firehose:PutRecord', 'firehose:PutRecordBatch'],
        resources: [eventLogFirehose.stream.attrArn],
      }),
    );

    const rule = new cwe.Rule(this, 'ElasticacheEventLogFirehose', {
      description: 'EventBridge Rule to Kinesis Data Firehose',
      eventPattern: {
        source: props.eventSource,
        detailType: props.detailType,
        detail: {
          SourceArn: props.eventTargetArn,
        },
      },
    });

    rule.addTarget({
      bind: (): cwe.RuleTargetConfig => ({
        arn: eventLogFirehose.stream.attrArn,
        role: eventBridgeRole,
        targetResource: eventLogFirehose.stream,
      }),
      // Implementing IRuleTarget interface to configure Kinesis Firehose as a custom EventBridge target
      // since there's no direct L2 construct support for Firehose targets or deprecated
    });
  }
}
