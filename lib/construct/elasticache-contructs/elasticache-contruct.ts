import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { IElastiCacheParam } from 'params/interface';
import * as cwlog from 'aws-cdk-lib/aws-logs';

export interface ElastiCacheProps extends cdk.StackProps {
  pjPrefix: string;
  myVpc: ec2.Vpc;
  appKey: kms.IKey;
  alarmTopic: sns.Topic;
  ElastiCacheParam: IElastiCacheParam['ElastiCacheSelfDesignedParam'];
  secret: secretsmanager.CfnSecret;
  subnetgroup: elasticache.CfnSubnetGroup;
  logGroup: cwlog.LogGroup;
  securityGroup: ec2.SecurityGroup;
}

export class ElastiCache extends Construct {
  constructor(scope: Construct, id: string, props: ElastiCacheProps) {
    super(scope, id);

    // カスタマイズする場合は../elasticache-param-groupparams/config.tsファイルを修正していく。
    const CustomParameterGroup = new elasticache.CfnParameterGroup(this, 'ElastiCacheCustomParameterGroup', {
      ...props.ElastiCacheParam.elastiCacheCustomParam,
    });

    const elasticacheCluster = new elasticache.CfnReplicationGroup(this, `${props.pjPrefix}-ElastiCache`, {
      replicationGroupDescription: 'elasticache',
      atRestEncryptionEnabled: true,
      authToken: props.secret.secretString,
      automaticFailoverEnabled: true,
      cacheSubnetGroupName: props.subnetgroup.cacheSubnetGroupName,
      engine: props.ElastiCacheParam.engine,
      engineVersion: props.ElastiCacheParam.engineVersion,
      kmsKeyId: props.appKey.keyId,
      logDeliveryConfigurations: [
        {
          logType: 'slow-log',
          destinationType: 'cloudwatch-logs',
          destinationDetails: {
            cloudWatchLogsDetails: {
              logGroup: props.logGroup.logGroupName,
            },
          },
          logFormat: 'json',
        },
      ],
      multiAzEnabled: true,
      notificationTopicArn: props.alarmTopic.topicArn,
      numNodeGroups: props.ElastiCacheParam.numNodeGroups,
      replicasPerNodeGroup: props.ElastiCacheParam.replicasPerNodeGroup,
      replicationGroupId: cdk.Stack.of(this).stackName + '-repGroup',
      securityGroupIds: [props.securityGroup.securityGroupId],
      transitEncryptionEnabled: true,
      cacheParameterGroupName: CustomParameterGroup.ref,
    });

    elasticacheCluster.cfnOptions.updatePolicy = {
      useOnlineResharding: true,
    };
    if (props.ElastiCacheParam.enableAutoScale) {
      //オートスケール有効時のインスタンスタイプを設定
      elasticacheCluster.cacheNodeType = props.ElastiCacheParam.cacheNodeTypeEnableAutoScale;
      //オートスケールの設定
      const ScalableTarget = new appscaling.ScalableTarget(this, 'ElastiCacheShardsScalableTarget', {
        serviceNamespace: appscaling.ServiceNamespace.ELASTICACHE,
        scalableDimension: 'elasticache:replication-group:NodeGroups',
        minCapacity: props.ElastiCacheParam.minCapacity,
        maxCapacity: props.ElastiCacheParam.maxCapacity,
        resourceId: `replication-group/${elasticacheCluster.replicationGroupId}`,
      });

      cdk.Aspects.of(ScalableTarget).add({
        visit(node) {
          if (node instanceof appscaling.CfnScalableTarget) {
            node.addDependency(elasticacheCluster);
          }
        },
      });

      ScalableTarget.scaleToTrackMetric('ElastiCacheShardsCPUUtilization', {
        targetValue: props.ElastiCacheParam.targetValueToScale,
        predefinedMetric: props.ElastiCacheParam.predefinedMetricToScale,
      });
    } else {
      //オートスケール無効時のインスタンスタイプを設定
      elasticacheCluster.cacheNodeType = props.ElastiCacheParam.cacheNodeTypeDisableAutoScale;
    }

    // キャッシュノードのエンドポイントホスト名出力
    new cdk.CfnOutput(this, 'ElastiCacheEndPoint', {
      key: 'ElastiCacheEndPoint',
      value: elasticacheCluster.attrConfigurationEndPointAddress,
      exportName: `${props.pjPrefix}-ElastiCacheEndPoint`,
    });
  }
}
