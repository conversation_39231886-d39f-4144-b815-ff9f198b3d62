import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { IElastiCacheParam, IRemovalPolicyParam } from 'params/interface';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { DataFirehose } from '../data-firehose-construct';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as logs from 'aws-cdk-lib/aws-logs';

export interface ElastiCacheProps extends cdk.StackProps {
  pjPrefix: string;
  myVpc: ec2.IVpc;
  appKey: kms.IKey;
  alarmTopic: sns.ITopic;
  ElastiCacheParam: IElastiCacheParam['ElastiCacheSelfDesignedParam'];
  secret: secretsmanager.CfnSecret;
  subnetgroup: elasticache.CfnSubnetGroup;
  securityGroup: ec2.SecurityGroup;
  newrelicSecretArn: string;
  logRemovalPolicyParam?: IRemovalPolicyParam;
  logBucketLifecycleRules: s3.LifecycleRule[];
}

export class ElastiCache extends Construct {
  constructor(scope: Construct, id: string, props: ElastiCacheProps) {
    super(scope, id);

    // Create log delivery configurations array for replication group
    const logDeliveryConfigurations: elasticache.CfnReplicationGroup.LogDeliveryConfigurationRequestProperty[] = [];
    const logTypes = ['slow-log', 'engine-log'];

    // Store created log resources for dependency management
    const logResources: { [key: string]: any } = {};

    // Handle logs based on configuration
    const logConfig = props.ElastiCacheParam.logDeliveryConfig || { type: 'cloudwatch-logs', retentionDays: 30 };

    if (logConfig.type === 'kinesis-firehose') {
      logTypes.forEach((logType) => {
        const formattedLogType = logType.replace('-', '');
        const logFirehose = this.createFirehose(props, formattedLogType);
        logResources[logType] = logFirehose;

        logDeliveryConfigurations.push(
          this.createLogDeliveryConfig(logType, 'kinesis-firehose', {
            deliveryStream: logFirehose.stream.deliveryStreamName || '',
          }),
        );
      });
    } else {
      // Default to cloudwatch-logs if not specified or if type is cloudwatch-logs
      const retentionDays = logConfig.retentionDays || 30;

      logTypes.forEach((logType) => {
        const formattedLogType = logType.replace('-', '');
        const logGroup = new logs.LogGroup(this, `${formattedLogType}LogGroup`, {
          logGroupName: `/aws/elasticache/${props.pjPrefix}/${logType}`,
          retention:
            retentionDays > 0 ? (retentionDays as unknown as logs.RetentionDays) : logs.RetentionDays.ONE_MONTH,
          removalPolicy: props.logRemovalPolicyParam?.removalPolicy || cdk.RemovalPolicy.DESTROY,
        });

        logResources[logType] = logGroup;

        logDeliveryConfigurations.push(
          this.createLogDeliveryConfig(logType, 'cloudwatch-logs', undefined, {
            logGroup: logGroup.logGroupName,
          }),
        );
      });
    }

    // カスタマイズする場合は../elasticache-param-groupparams/config.tsファイルを修正していく。
    const customParameterGroup = new elasticache.CfnParameterGroup(this, 'ElastiCacheCustomParameterGroup', {
      ...props.ElastiCacheParam.elastiCacheCustomParam,
    });

    const elasticacheCluster = new elasticache.CfnReplicationGroup(this, `${props.pjPrefix}-ElastiCache`, {
      replicationGroupDescription: 'elasticache',
      atRestEncryptionEnabled: true,
      authToken: props.secret.secretString,
      automaticFailoverEnabled: true,
      cacheSubnetGroupName: props.subnetgroup.cacheSubnetGroupName,
      engine: props.ElastiCacheParam.engine,
      engineVersion: props.ElastiCacheParam.engineVersion,
      kmsKeyId: props.appKey.keyId,
      logDeliveryConfigurations,
      multiAzEnabled: true,
      notificationTopicArn: props.alarmTopic.topicArn,
      numNodeGroups: props.ElastiCacheParam.numNodeGroups,
      replicasPerNodeGroup: props.ElastiCacheParam.replicasPerNodeGroup,
      replicationGroupId: cdk.Stack.of(this).stackName + '-repGroup',
      securityGroupIds: [props.securityGroup.securityGroupId],
      transitEncryptionEnabled: true,
      cacheParameterGroupName: customParameterGroup.ref,
      cacheNodeType: props.ElastiCacheParam.enableAutoScale
        ? props.ElastiCacheParam.cacheNodeTypeEnableAutoScale
        : props.ElastiCacheParam.cacheNodeTypeDisableAutoScale,
    });

    elasticacheCluster.cfnOptions.updatePolicy = { useOnlineResharding: true };

    // Use Aspects to ensure log resources are created before ElastiCache
    cdk.Aspects.of(this).add({
      visit(node) {
        if (node instanceof elasticache.CfnReplicationGroup) {
          for (const logType of logTypes) {
            const resource = logResources[logType];
            if (resource) {
              if (logConfig.type === 'kinesis-firehose' && resource.stream) {
                node.addDependency(resource.stream);
              } else if (resource.node.defaultChild) {
                node.addDependency(resource.node.defaultChild as cdk.CfnResource);
              }
            }
          }
        }
      },
    });

    //オートスケール有効時のインスタンスタイプを設定
    if (props.ElastiCacheParam.enableAutoScale) {
      this.configureAutoScaling(props, elasticacheCluster);
    }

    // Store endpoint in SSM parameter
    const endpointAddress =
      props.ElastiCacheParam.numNodeGroups > 1
        ? elasticacheCluster.attrConfigurationEndPointAddress // Cluster mode enabled
        : elasticacheCluster.attrPrimaryEndPointAddress; // Cluster mode disabled

    new ssm.StringParameter(this, 'SSMElastiCacheRepolicationGroupEndPoint', {
      parameterName: `/${props.pjPrefix}/${props.ElastiCacheParam.engine}/endpoint`,
      stringValue: endpointAddress,
    });

    // キャッシュノードのエンドポイントホスト名出力
    new cdk.CfnOutput(this, 'ElastiCacheEndPoint', {
      key: 'ElastiCacheEndPoint',
      value: elasticacheCluster.attrConfigurationEndPointAddress,
      exportName: `${props.pjPrefix}-ElastiCacheEndPoint`,
    });
  }

  private createFirehose(props: ElastiCacheProps, logType: string): DataFirehose {
    return new DataFirehose(this, `${logType}DatFirehose`, {
      firehoseStreamName: `${props.pjPrefix}-ElastiCache-${logType}`,
      firehoseLogGroupName: `/aws/firehose/${props.pjPrefix}/elasticache/${logType.toLowerCase()}`,
      httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
      httpEndpointName: 'New Relic',
      s3BackupMode: 'AllData',
      secretArn: props.newrelicSecretArn,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      firehoseBucketLifecycleRules: props.logBucketLifecycleRules,
    });
  }

  private createLogDeliveryConfig(
    logType: string,
    destinationType: string,
    kinesisFirehoseDetails?: { deliveryStream: string },
    cloudWatchLogsDetails?: { logGroup: string },
  ): elasticache.CfnReplicationGroup.LogDeliveryConfigurationRequestProperty {
    let destinationDetails: any = {};

    if (destinationType === 'kinesis-firehose' && kinesisFirehoseDetails) {
      destinationDetails = {
        kinesisFirehoseDetails,
      };
    } else if (destinationType === 'cloudwatch-logs' && cloudWatchLogsDetails) {
      destinationDetails = {
        cloudWatchLogsDetails,
      };
    }

    return {
      logType,
      destinationType,
      logFormat: 'json',
      destinationDetails,
    };
  }

  private configureAutoScaling(props: ElastiCacheProps, replicationGroup: elasticache.CfnReplicationGroup): void {
    const scalableTarget = new appscaling.ScalableTarget(this, 'ElastiCacheShardsScalableTarget', {
      serviceNamespace: appscaling.ServiceNamespace.ELASTICACHE,
      scalableDimension: 'elasticache:replication-group:NodeGroups',
      minCapacity: props.ElastiCacheParam.minCapacity,
      maxCapacity: props.ElastiCacheParam.maxCapacity,
      resourceId: `replication-group/${replicationGroup.replicationGroupId}`,
    });

    cdk.Aspects.of(scalableTarget).add({
      visit(node) {
        if (node instanceof appscaling.CfnScalableTarget) {
          node.addDependency(replicationGroup);
        }
      },
    });

    scalableTarget.scaleToTrackMetric('ElastiCacheShardsCPUUtilization', {
      targetValue: props.ElastiCacheParam.targetValueToScale,
      predefinedMetric: props.ElastiCacheParam.predefinedMetricToScale,
    });
  }
}
