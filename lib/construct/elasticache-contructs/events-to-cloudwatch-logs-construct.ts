import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import { IRemovalPolicyParam } from 'params/interface';

export interface EventsToCloudWatchLogsProps {
  pjPrefix: string;
  logGroupName: string;
  logRetentionDays?: number; // Default: 30 days
  logRemovalPolicyParam?: IRemovalPolicyParam;
  eventSource: string[];
  detailType: string[];
  eventTargetArn: string[];
}

/**
 * Constructs an `EventsToCloudWatchLogs` which sets up an EventBridge rule to send events
 * to a CloudWatch Logs group.
 *
 * @class
 * @extends {Construct}
 *
 * @param {Construct} scope - The scope in which this construct is defined.
 * @param {string} id - The unique identifier for this construct.
 * @param {EventsToCloudWatchLogsProps} props - The properties required to configure the construct.
 *
 * @remarks
 * - A CloudWatch Log Group is created with the specified configuration.
 * - A CloudWatch EventBridge rule is created to route events matching the specified pattern to the Log Group.
 */
export class EventsToCloudWatchLogs extends Construct {
  public readonly logGroup: logs.LogGroup;
  public readonly rule: events.Rule;

  constructor(scope: Construct, id: string, props: EventsToCloudWatchLogsProps) {
    super(scope, id);

    // Create CloudWatch Log Group for event logs
    const retentionDays = props.logRetentionDays || 30;
    this.logGroup = new logs.LogGroup(this, 'EventLogGroup', {
      logGroupName: props.logGroupName,
      retention: retentionDays > 0 ? (retentionDays as unknown as logs.RetentionDays) : logs.RetentionDays.ONE_MONTH,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy || cdk.RemovalPolicy.DESTROY,
    });

    // Create an EventBridge rule to capture events and send to CloudWatch Logs
    this.rule = new events.Rule(this, 'EventsRule', {
      eventPattern: {
        source: props.eventSource,
        detailType: props.detailType,
        detail: {
          SourceArn: props.eventTargetArn,
        },
      },
    });

    // Add CloudWatch Logs as target
    this.rule.addTarget(new targets.CloudWatchLogGroup(this.logGroup));
  }
}
