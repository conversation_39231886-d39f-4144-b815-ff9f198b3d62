import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import { IElastiCacheParam, IRemovalPolicyParam } from 'params/interface';
import { EventsToDataFirehose } from './events-to-kinesis-data-firehose-construct';
import { EventsToCloudWatchLogs } from './events-to-cloudwatch-logs-construct';

export interface ElastiCacheServerlessProps extends cdk.StackProps {
  pjPrefix: string;
  myVpc: ec2.IVpc;
  appKey: kms.IKey;
  ElastiCacheParam: IElastiCacheParam['ElastiCacheServerlessParam'];
  securityGroup: ec2.SecurityGroup;
  newrelicSecretArn: string;
  logRemovalPolicyParam?: IRemovalPolicyParam;
  logBucketLifecycleRules: s3.LifecycleRule[];
}
/**
 * Represents a serverless ElastiCache construct.
 * This construct creates an ElastiCache serverless cluster and integrates it with
 * New Relic for handling events for cache, while also storing the cluster endpoint in AWS SSM Parameter Store.
 *
 * @extends Construct
 */
export class ElastiCacheServerless extends Construct {
  constructor(scope: Construct, id: string, props: ElastiCacheServerlessProps) {
    super(scope, id);

    const elasticacheCluster = new elasticache.CfnServerlessCache(this, `${props.pjPrefix}-ElastiCacheServerless`, {
      engine: props.ElastiCacheParam.engine,
      majorEngineVersion: props.ElastiCacheParam.engineVersion,
      serverlessCacheName: `${props.pjPrefix}-ElastiCacheServerless`.toLowerCase(),

      securityGroupIds: [props.securityGroup.securityGroupId],
      subnetIds: props.myVpc.isolatedSubnets.map(({ subnetId }) => subnetId),
      kmsKeyId: props.appKey.keyId,
      cacheUsageLimits: {
        dataStorage: {
          unit: 'GB',
          maximum: props.ElastiCacheParam.dataStorageMaximum,
          minimum: props.ElastiCacheParam.dataStorageMinimum,
        },
        ecpuPerSecond: {
          maximum: props.ElastiCacheParam.ecpuPerSecondMaximum,
          minimum: props.ElastiCacheParam.ecpuPerSecondMinimum,
        },
      },
    });

    // Get events from config - must be provided
    if (
      !props.ElastiCacheParam.logDeliveryConfig?.events ||
      props.ElastiCacheParam.logDeliveryConfig.events.length === 0
    ) {
      throw new Error('Events list must be provided in params');
    }
    const elasticacheEvents = props.ElastiCacheParam.logDeliveryConfig.events;

    // Configure the event handling based on the destination type
    const shouldUseFirehose =
      !props.ElastiCacheParam.logDeliveryConfig || props.ElastiCacheParam.logDeliveryConfig.type === 'kinesis-firehose';

    if (shouldUseFirehose) {
      new EventsToDataFirehose(this, `${props.pjPrefix}-EventLogDatFirehose`, {
        firehoseStreamName: `${props.pjPrefix}-ElastiCache-EventLog`,
        firehoseLogGroupName: `/aws/firehose/${props.pjPrefix}/elasticache/event-log`,
        httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
        httpEndpointName: 'New Relic',
        s3BackupMode: 'AllData',
        secretArn: props.newrelicSecretArn,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        firehoseBucketLifecycleRules: props.logBucketLifecycleRules,
        eventSource: ['aws.elasticache'],
        detailType: elasticacheEvents,
        eventTargetArn: [elasticacheCluster.attrArn],
      });
    } else if (props.ElastiCacheParam.logDeliveryConfig.type === 'cloudwatch-logs') {
      new EventsToCloudWatchLogs(this, 'ElastiCacheEventsToCloudWatch', {
        pjPrefix: props.pjPrefix,
        logGroupName: `/aws/elasticache/${props.pjPrefix}/event-log`,
        logRetentionDays: props.ElastiCacheParam.logDeliveryConfig.retentionDays,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        eventSource: ['aws.elasticache'],
        detailType: elasticacheEvents,
        eventTargetArn: [elasticacheCluster.attrArn],
      });
    }

    new ssm.StringParameter(this, 'SSMElastiCacheClusterEndPoint', {
      parameterName: `/${props.pjPrefix}/${props.ElastiCacheParam.engine}/endpoint`,
      stringValue: elasticacheCluster.attrEndpointAddress,
    });
  }
}
