import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import { IElastiCacheParam } from 'params/interface';

export interface ElastiCacheServerlessProps extends cdk.StackProps {
  pjPrefix: string;
  myVpc: ec2.Vpc;
  appKey: kms.IKey;
  ElastiCacheParam: IElastiCacheParam['ElastiCacheServerlessParam'];
  securityGroup: ec2.SecurityGroup;
}

export class ElastiCacheServerless extends Construct {
  constructor(scope: Construct, id: string, props: ElastiCacheServerlessProps) {
    super(scope, id);

    const elasticacheCluster = new elasticache.CfnServerlessCache(this, `${props.pjPrefix}-ElastiCacheServerless`, {
      engine: props.ElastiCacheParam.engine,
      majorEngineVersion: props.ElastiCacheParam.engineVersion,
      serverlessCacheName: `${props.pjPrefix}-ElastiCacheServerless`.toLowerCase(),

      securityGroupIds: [props.securityGroup.securityGroupId],
      subnetIds: props.myVpc.isolatedSubnets.map(({ subnetId }) => subnetId),
      kmsKeyId: props.appKey.keyId,
      cacheUsageLimits: {
        dataStorage: {
          unit: 'GB',

          maximum: props.ElastiCacheParam.dataStorageMaximum,
          minimum: props.ElastiCacheParam.dataStorageMinimum,
        },
        ecpuPerSecond: {
          maximum: props.ElastiCacheParam.ecpuPerSecondMaximum,
          minimum: props.ElastiCacheParam.ecpuPerSecondMinimum,
        },
      },
    });

    new cdk.CfnOutput(this, 'ElastiCacheEndPoint', {
      key: 'ElastiCacheEndPoint',
      value: elasticacheCluster.attrEndpointAddress,
      exportName: `${props.pjPrefix}-ElastiCacheEndPoint`,
    });
  }
}
