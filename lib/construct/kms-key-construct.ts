import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as iam from 'aws-cdk-lib/aws-iam';

export interface KMSKeyProps {
  /**
   * Removal policy for the KMS key.
   * @default RemovalPolicy.RETAIN
   */
  removalPolicy?: cdk.RemovalPolicy;

  /**
   * Duration in days after which the key is deleted after destruction of the resource that the key is associated with.
   * @default Duration.days(7)
   */
  pendingWindow?: cdk.Duration;
}

export class KMSKey extends Construct {
  public readonly kmsKey: kms.Key;

  constructor(scope: Construct, id: string, props?: KMSKeyProps) {
    super(scope, id);

    // CMK
    const kmsKey = new kms.Key(this, 'Key', {
      enableKeyRotation: true,
      description: 'Custom KMS key',
      alias: `${id}-for-app`,
      removalPolicy: props?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      pendingWindow: props?.pendingWindow ?? cdk.Duration.days(7),
    });

    // Permission to access KMS Key from CloudWatch Logs
    kmsKey.addToResourcePolicy(
      new iam.PolicyStatement({
        actions: ['kms:Encrypt*', 'kms:Decrypt*', 'kms:ReEncrypt*', 'kms:GenerateDataKey*', 'kms:Describe*'],
        principals: [new iam.ServicePrincipal(`logs.${cdk.Stack.of(this).region}.amazonaws.com`)],
        resources: ['*'],
        conditions: {
          ArnLike: {
            'kms:EncryptionContext:aws:logs:arn': `arn:aws:logs:${cdk.Stack.of(this).region}:${
              cdk.Stack.of(this).account
            }:*`,
          },
        },
      }),
    );

    this.kmsKey = kmsKey;
  }
}
